<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\DownloadInventoryReport;
use DB;
use App\Http\Resources\InventoryReportResource;
use App\InventoryReport;
use Carbon\Carbon;

class InventoryReportController extends Controller{  

    public function getCategoryProducts(){

        $queryGetListCategory ="SELECT id AS catID, `eula_text` AS catDesc
        FROM `categories` 
        WHERE deleted_at IS NULL 
        AND user_id IS NOT NULL";

        $data = DB::connection('mysql_inventory')->select($queryGetListCategory);

        return response()->json($data);
    }

    public function getProductModels(Request $request){

        $result = $request['catID'];
        $queryGetListProduct ="SELECT NAME as productName
        FROM models
        WHERE `category_id` = $result
        AND `deleted_at` IS NULL
        AND user_id IS NOT NULL
        GROUP BY NAME";

        $data = DB::connection('mysql_inventory')->select($queryGetListProduct);

        return response()->json($data);
    }

    public function index()
    {
        // Get reports
        $reports = InventoryReport::where('deleted', 0)
        ->orderBy('created_at', 'desc')->get();

        // Return collection of reports as a resource
        return InventoryReportResource::collection($reports);
    }

    public function store(Request $request)
    {
        $report = $request->isMethod('put') ? InventoryReport::findOrFail() : new InventoryReport;

        // $report->id = $request->input('report_id');
        $report->file_name = $request->input('file_name');
        $report->status = 0;
        $report->created_at = Carbon::now();
        $report->created_by = $request->input('created_by');

        if($report->save()) {
            return new InventoryReportResource($report);
        }
    }

    public function export(Request $request)
    {
        $catID = $request['catID'];

        if ($catID == 25) {
            $catName = 'NetworkEquipment';
        }
        if ($catID == 2) {
            $catName = 'Server';
        }
        if ($catID == 4) {
            $catName = 'Storage';
        }
        if ($catID == 13) {
            $catName = 'Enclosure';
        }
        if ($catID == 21) {
            $catName = 'TapeLibrary';
        }
        if ($catID == 22) {
            $catName = 'Facility';
        }
        if ($catID == 28) {
            $catName = 'NetworkLine';
        }
        if ($catID == 29) {
            $catName = 'Laptop';
        }

        $productName = $request['productName'];
        self::saveinfo($catID, $productName);

        $headers = [
            'Content-Type' => 'application/octet-stream',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Cache-Control' => 'public',
            'Content-Description' => 'File Transfer',
            'Content-Transfer-Encoding' => 'binary',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
        ];
        $date = Carbon::now()->format('Ymd');
        $time = Carbon::now()->format('his');
        $reportName = 'ReportInventory_' . $catName . '_' . $date . '_' . $time;
        Excel::store(new DownloadInventoryReport($catID, $productName), 'Export/' . $reportName . '.xlsx');
        // dump($file);
        sleep(5);
        return json_encode([
            'reportname' => $reportName . '.xlsx',
        ]);
        // return response()->download(storage_path('/app/Export/ReportInventory_' . $catName . '_' . $date . '_' . $time . '.xlsx'), 'ReportInventory_' . $catName . '_' . $date . '_' . $time . '.xlsx', $headers);
    }

     private static function saveinfo($catID,$productName){

        if($catID == 2){
            $catName = 'Server';
        }
        if($catID == 4){
            $catName = 'Storage';
        }
        if($catID == 13){
            $catName = 'Enclosure';
        }
        if($catID == 21){
            $catName = 'TapeLibrary';
        }
        if($catID == 22){
            $catName = 'Facility';
        }
        if($catID == 25){
            $catName = 'NetworkEquipment';
        }
        if($catID == 28){
            $catName = 'NetworkLine';
        }
        if($catID == 29){
            $catName = 'Laptop';
        }    
        
        $date = Carbon::now()->format('Ymd');
        $time = Carbon::now()->format('his');

         $report = new InventoryReport;
         $reportName = 'Inventory Report '. $catName .'('.$productName.')';
        // $reportName = 'Inventory Report';
         $report->name = $reportName;
         $report->category = $catName;
         $report->file_name = 'ReportInventory_'.$catName.'_'.$date.'_'.$time.'.xlsx';
         $report->status = 'completed';
         $report->parameter = json_encode([
             'category' =>  $catID,
             'product' =>  $productName
         ]);
         $report->created_at = Carbon::now();
        // $report->created_by = $request->input('created_by');

         if($report->save()) {
             return new InventoryReportResource($report);
         }
     }


}
