<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SlaReport extends Model
{
    protected $connection = 'mysql';
    protected $table = 'sla_report';
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'file_name',
        'parameter',
        'status',
        'deleted',
        'hidden',
        'log',
        'created_by',
        'updated_at'
    ];

    protected function casts(): array
    {
        return [
            'parameter' => 'array',
            'deleted' => 'boolean',
            'hidden' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }
}
