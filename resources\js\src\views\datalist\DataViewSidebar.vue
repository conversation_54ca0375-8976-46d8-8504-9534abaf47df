<template>
  <vs-sidebar
    click-not-close
    position-right
    parent="body"
    default-index="1"
    color="primary"
    class="add-new-data-sidebar items-no-padding"
    spacer
    v-model="isSidebarActiveLocal"
  >
    <div class="mt-6 flex items-center justify-between px-6">
      <h4>{{ Object.entries(this.data).length === 0 ? "ADD NEW" : "UPDATE" }} USER</h4>
      <vs-button v-if="!is_new" type="filled" color="danger" size="small" @click="resetPopup">Reset Password</vs-button>
      <feather-icon icon="XIcon" @click.stop="isSidebarActiveLocal = false" class="cursor-pointer"></feather-icon>
    </div>
    <vs-divider class="mb-0"></vs-divider>

    <VuePerfectScrollbar class="scroll-area--data-list-add-new" :settings="settings" :key="$vs.rtl">
      <form data-vv-scope="form-1" class="p-6">
        <!-- FULL NAME -->
        <vs-input
          label="Full Name"
          v-model="name"
          class="mt-5 w-full"
          name="full-name"
          v-validate="'required'"
        />
        <span
          class="text-danger text-sm"
          v-show="errors.has('full-name')"
        >{{ errors.first('full-name') }}</span>

        <!-- USERNAME -->
        <vs-input v-if="is_new"
          label="Username"
          v-model="username"
          class="mt-5 w-full"
          name="username"
          v-validate="'required'"
        />
        <vs-input v-else-if="!is_new"
          label="Username"
          v-model="username"
          class="mt-5 w-full"
          name="username"
          v-validate="'required'" disabled
        />
        <span
          class="text-danger text-sm"
          v-show="errors.has('username')"
        >{{ errors.first('username') }}</span>

        <!-- USERNAME -->
        <vs-input
          v-if="is_new"
          label="Password"
          type="password"
          v-model="password"
          class="mt-5 w-full"
          name="password"
          v-validate="'required'"
        />
        <span
          class="text-danger text-sm"
          v-show="errors.has('password')"
        >{{ errors.first('password') }}</span>

        <!-- EMAIL -->
        <vs-input
          label="Email"
          v-model="email"
          class="mt-5 w-full"
          name="email"
          v-validate="'required|email'"
        />
        <span class="text-danger text-sm" v-show="errors.has('email')">{{ errors.first('email') }}</span>

        <div class="mt-5 w-full flex items-center">
            <vs-switch v-model="isActive" color="success" @change="toggleActiveStatus" />
            <label for="userActiveSwitch" class="ml-2">{{ isActive ? 'Active' : 'Inactive' }}</label>
        </div>

        <div class="vx-row mt-8">
          <div class="vx-col w-full">
            <div class="flex items-end px-3">
              <feather-icon svgClasses="w-6 h-6" icon="LockIcon" class="mr-2" />
              <span class="font-medium text-lg leading-none">Permissions</span>
            </div>
            <vs-divider />
          </div>
        </div>

        <div class="block overflow-x-auto">
          <table class="w-full permissions-table">
            <tr>
              <!--
                You can also use `Object.keys(Object.values(data_local.permissions)[0])` this logic if you consider,
                our data structure. You just have to loop over above variable to get table headers.
                Below we made it simple. So, everyone can understand.
               -->
              <!-- <th class="font-semibold text-base text-left px-3 py-2" v-for="heading in ['Module', 'Read', 'Write', 'Create', 'Delete']" :key="heading">{{ heading }}</th> -->
            </tr>

            <tr>
              <td class="px-3 py-2">
                <vs-checkbox v-model="role" vs-value="1">Administrator</vs-checkbox>
              </td>
              <!-- <td v-for="(permission, name) in val" class="px-3 py-2" :key="name+permission">
                <vs-checkbox v-model="val[name]" class="pointer-events-none" />
              </td> -->
            </tr>
            <tr>
              <td class="px-3 py-2">
                <vs-checkbox v-model="role" vs-value="2">SLA Dashboard</vs-checkbox>
              </td>
            </tr>
            <tr>
              <td class="px-3 py-2">
                <vs-checkbox v-model="role" vs-value="3">SLA Report</vs-checkbox>
              </td>
            </tr>
            <tr>
              <td class="px-3 py-2">
                <vs-checkbox v-model="role" vs-value="4">Technical Dashboard</vs-checkbox>
              </td>
            </tr>
            <tr>
              <td class="px-3 py-2">
                <vs-checkbox v-model="role" vs-value="5">Inventory Report</vs-checkbox>
              </td>
            </tr>
          </table>
        </div>
        <!-- <ul class="centerx mt-6">
          <li>
            <vs-checkbox v-model="role" vs-value="1">Administrator</vs-checkbox>
          </li>
          <li>
            <vs-checkbox v-model="role" vs-value="2">SLA Dashboard</vs-checkbox>
          </li>
          <li>
            <vs-checkbox v-model="role" vs-value="3">SLA Report</vs-checkbox>
          </li>
          <li>
            <vs-checkbox v-model="role" vs-value="4">Technical Dashboard</vs-checkbox>
          </li>
        </ul> -->
      </form>
    </VuePerfectScrollbar>

    <div class="flex flex-wrap items-center p-6" slot="footer">
      <vs-button class="mr-6" @click="submitData" :disabled="!isFormValid">{{ Object.entries(this.data).length === 0 ? "SUBMIT" : "UPDATE" }}</vs-button>
      <vs-button type="border" color="danger" @click="isSidebarActiveLocal = false">Cancel</vs-button>
    </div>

    <form data-vv-scope="form-2" class="p-6">
    <vs-popup classContent="popup-example" :title="'Reset User Password - ' +  name" :active.sync="popupResetPass">
      <vs-input label="New Password" type="password" v-model="new_password" class="mt-5 w-full" name="new_password" v-validate="'required'" />
      <span class="text-danger text-sm" v-show="errors.has('new_password')">{{ errors.first('new_password') }}</span>

      <vs-input label="Confirm New Password" type="password" v-model="new_password_confirm" class="mt-5 w-full" name="new_password_confirm" v-validate="'required'" />
      <span class="text-danger text-sm" v-show="errors.has('new_password_confirm')">{{ errors.first('new_password_confirm') }}</span>

      <vs-button class="mt-6" @click="updateUserPassword" :disabled="!isResetFormValid" color="primary" type="filled">Confirm</vs-button>
      <vs-button class="mt-6" @click="popupResetPass = false" color="warning" type="filled">Cancel</vs-button>
    </vs-popup>
    </form>

  </vs-sidebar>
</template>

<script>
import VuePerfectScrollbar from "vue-perfect-scrollbar";
import axios from 'axios'

export default {
  props: {
    isSidebarActive: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    isSidebarActive(val) {
      if (!val) return;
      if (Object.entries(this.data).length === 0) {
        this.is_new = true;
        this.initValues();
        this.$validator.reset();
      } else {
        this.is_new = false;
        let {
          id,
          name,
          username,
          //password,
          email,
          active,
          role
        } = JSON.parse(JSON.stringify(this.data));
        this.dataId = id;
        this.name = name;
        this.username = username;
        //this.password = password;
        this.email = email;
        this.isActive = active;
        this.role = role;
        this.initValues();
      }
      // Object.entries(this.data).length === 0 ? this.initValues() : { this.dataId, this.name, this.dataCategory, this.dataOrder_status, this.dataPrice } = JSON.parse(JSON.stringify(this.data))
    }
  },
  data() {
    return {
      is_new: true,
      dataId: null,
      name: "",
      username: "",
      password: "",
      new_password: "",
      new_password_confirm: "",
      email: "",
      role: [],
      popupResetPass: false,
      isActive: true,

      settings: {
        // perfectscrollbar settings
        maxScrollbarLength: 60,
        wheelSpeed: 0.6
      }
    };
  },
  computed: {
    isSidebarActiveLocal: {
      get() {
        return this.isSidebarActive;
      },
      set(val) {
        if (!val) {
          this.$emit("closeSidebar");
          this.$validator.reset();
          this.initValues();
        }
      }
    },
    isFormValid() {
      if (this.is_new) {
        return (
          !this.errors.any() &&
          this.name &&
          this.username &&
          this.password &&
          this.email &&
          this.role.length > 0
        );
      } else {
        return (
          !this.errors.any() &&
          this.name &&
          this.username &&
          this.email &&
          this.role.length > 0
        );
      }
    },
    isResetFormValid() {
      return (
          !this.errors.any() &&
          this.new_password &&
          this.new_password_confirm &&
          this.new_password === this.new_password_confirm
        );
    }
  },
  methods: {
    initValues() {
      if (this.data.id) return;
      this.dataId = null;
      this.name = "";
      this.username = "";
      this.password = "";
      this.email = "";
      this.role = [];
      this.new_password = "";
      this.new_password_confirm = "";
    },
    submitData() {
      this.$validator.validateAll('form-1').then(result => {
        if (result) {
          const obj = {
            id: this.dataId,
            name: this.name,
            username: this.username,
            password: this.password,
            email: this.email,
            role: this.role
          };

          if (this.dataId !== null && this.dataId >= 0) {
            this.updateUser();
          } else {
            delete obj.id;
            this.register();
          }

          this.$emit("closeSidebar");
          this.initValues();
        }
      });
    },
    register() {
      axios.post('api/user/new',{ params: { name: this.name, username: this.username, email: this.email, active: this.isActive, role: this.role, password: this.password, password_confirmation: this.password }})
      .then(response => {
        this.$parent.displayUsers();
        this.$vs.notify({
            color: 'success',
            title: 'Create Successful',
            text: 'New user successfuly created.'
        });
      })
      .catch(error => {
        console.log(error);
        // TODO
      });
    },
    updateUser() {
      axios.post('api/user/update',{ params: { id: this.dataId, name: this.name, email: this.email, active: this.isActive, role: this.role }})
      .then(response => {
        this.$parent.displayUsers();
        this.$vs.notify({
            color: 'success',
            title: 'Update Successful',
            text: 'User information successfuly updated.'
        });
      })
      .catch(error => {
        console.log(error);
        // TODO
      });
    },
    resetPopup() {
      this.isSidebarActiveLocal = false
      this.popupResetPass = true
    },
    updateUserPassword() {

      this.$validator.validateAll('form-1').then(result => {
        if (result) {
          const obj = {
            id: this.dataId,
            password: this.new_password,
            password_confirmation: this.new_password_confirm,
          };

          axios.post('api/user/update-pass',{ params: { id: this.dataId, password: this.new_password, password_confirmation: this.new_password_confirm }})
          .then(response => {
            this.$parent.displayUsers();
            this.$vs.notify({
                color: 'success',
                title: 'Update Password Successful',
                text: 'User password successfuly updated.'
            });
          })
          .catch(error => {
            console.log(error);
            // TODO
          });

          this.popupResetPass = false;
          this.initValues();
        }
      });
    },
    toggleActiveStatus() {
      this.isActive = !this.isActive;
    },
  },
  components: {
    VuePerfectScrollbar
  }
};
</script>

<style lang="scss" scoped>
.add-new-data-sidebar {
  ::v-deep .vs-sidebar--background {
    z-index: 52010;
  }

  ::v-deep .vs-sidebar {
    z-index: 52010;
    width: 400px;
    max-width: 90vw;

    .img-upload {
      margin-top: 2rem;

      .con-img-upload {
        padding: 0;
      }

      .con-input-upload {
        width: 100%;
        margin: 0;
      }
    }
  }
}

.scroll-area--data-list-add-new {
  // height: calc(var(--vh, 1vh) * 100 - 4.3rem);
  height: calc(var(--vh, 1vh) * 100 - 16px - 45px - 82px);
}
</style>
