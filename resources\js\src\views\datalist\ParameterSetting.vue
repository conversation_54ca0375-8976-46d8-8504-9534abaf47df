<template>

    <div class="vx-col">
        <vs-breadcrumb :items="breadcrumbs" separator="chevron_right"></vs-breadcrumb>
        <vx-card title="SLA Dashboard Parameter Setting">

            <vs-table max-items="10" pagination search :data="parameters" stripe>

                <template slot="thead">
                    <vs-th sort-key="parameter_name" width="20%">Name</vs-th>
                    <vs-th>Description</vs-th>
                    <vs-th sort-key="parameter_value">Value</vs-th>
                    <vs-th sort-key="modified_by">Modified By</vs-th>
                    <vs-th sort-key="modified_date">Modified Date</vs-th>
                    <vs-th>Action</vs-th>
                </template>

                <template slot-scope="{data}">
                    <vs-tr :key="indextr" v-for="(tr, indextr) in data">

                        <vs-td :data="tr.parameter_name">
                            {{ tr.parameter_name }}
                        </vs-td>

                        <vs-td :data="tr.parameter_desc">
                            {{ tr.parameter_desc }}
                        </vs-td>

                        <vs-td :data="tr.parameter_value">
                            {{ tr.parameter_value }}
                            <!--<template slot="edit">
                                <vs-input-number v-model="tr.parameter_value"/>
                            </template>-->
                        </vs-td>

                        <vs-td :data="tr.modified_by_name">
                            {{ tr.modified_by_name }}
                        </vs-td>

                        <vs-td :data="tr.modified_date">
                            {{ tr.modified_date_format }}
                        </vs-td>

                        <vs-td>
                            <vs-button @click="openEditDialogue(tr.id, tr.parameter_name, tr.parameter_value)" color="warning" type="filled" icon-pack="feather" icon="icon-edit"></vs-button>
                        </vs-td>

                    </vs-tr>
                </template>
            </vs-table>

            <vs-popup classContent="popup-example" title="Edit Parameter Value" :active.sync="popupActive">
                <span>Name</span>
                <vs-input disabled placeholder="Parameter Name" vs-placeholder="Parameter Name" v-model="parameter.name" class="mt-3 w-full"/>
                <span>Parameter Value</span>
                <vs-input placeholder="Parameter Value" vs-placeholder="Parameter Value" v-model="parameter.value" class="mt-3 mb-3 w-full"/>
                <vs-button @click="updateParamValue">Update</vs-button>
                <vs-button @click="popupActive=false" color="warning">Cancel</vs-button>
            </vs-popup>
        </vx-card>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                parameters: [],
                breadcrumbs: [
                    {
                        title: 'Administrator',
                        url: '/admin'
                    },
                    {
                        title: 'SLA Dashboard',
                        active: true
                    },
                ],
                parameter: {
                    id: '',
                    name: '',
                    value: '',
                    userid: ''
                },
                popupActive: false
                // value1: '',
                // value2: '',

            }
        },
        computed: {
            // validName() {
            //     return (this.valMultipe.value1.length > 0 && this.valMultipe.value2.length > 0)
            // }
        },
        mounted() {
            const vm = this;
            vm.fetchParameterList();
        },
        methods: {
            fetchParameterList: function () {
                let api_url = '/api/parameter/sla_dashboard';
                this.$vs.loading();
                fetch(api_url)
                    .then(res => res.json())
                    .then(res => {
                        this.parameters = res.data;
                        this.$vs.loading.close();
                    })
                    .catch(err => console.log(err));

            },
            openEditDialogue(param_id, param_name, param_value) {
                console.log('parameter_id: ' + param_id);
                this.parameter.id = param_id;
                this.parameter.name = param_name;
                this.parameter.value = param_value;
                this.popupActive = true;

                this.$http({
                    url: `user`,
                    method: 'GET'
                }).then((res) => {
                    this.parameter.userid = res.data.id;
                })
                    .catch(err => console.log(err));
            },
            updateParamValue: function () {
                // Update
                fetch('api/parameter', {
                    method: 'put',
                    body: JSON.stringify(this.parameter),
                    headers: {
                        'content-type': 'application/json'
                    }
                })
                    .then(res => res.json())
                    .then(data => {
                        this.popupActive = false;
                        this.$vs.notify({
                            color: 'success',
                            title: 'Update Successfully',
                            text: 'Selected parameter value successfully updated!'
                        });
                        this.fetchParameterList();
                    })
                    .catch(err => console.log(err));
            },
            acceptAlert() {
                this.clearValMultiple();
                this.$vs.notify({
                    color: 'success',
                    title: 'Accept Selected',
                    text: 'Lorem ipsum dolor sit amet, consectetur'
                })
            },
            close() {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Closed',
                    text: 'You close a dialog!'
                })
            },
            clearValMultiple() {
                this.valMultipe.value1 = "";
                this.valMultipe.value2 = "";
            }
        }
    }
</script>
