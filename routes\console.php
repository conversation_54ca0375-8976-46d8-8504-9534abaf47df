<?php

use App\crm\sla\AlertProxyPerformance;
use App\crm\sla\InsertCallCenterSlaData;
use App\crm\sla\InsertCallCenterSlaData2;
use App\crm\sla\insertITByApproverSlaData;
use App\crm\sla\insertITCoordSlaDataAdhoc;
use App\crm\sla\insertITServiceRequestData;
use App\crm\sla\insertITSeveritySlaDataAdhoc;
use App\crm\sla\insertITSpecSlaDataAdhoc;
use App\crm\sla\InsertTalkdeskData;
use App\crm\sla\InsertTalkdeskDataByReportFile;
use App\Nagios\Performance\checkPerformanceProxiesdata;
use App\Report\SlaReportGenerator;
use Carbon\Carbon;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->describe('Display an inspiring quote');

Artisan::command('insert-data-aspect-sla', function () {
    InsertCallCenterSlaData::runInsertAspectSlaData(null);
})->describe('insert aspect data into poms');

Artisan::command('report-scheduler-sla', function () {
    SlaReportGenerator::runCheckReportQueue();
})->describe('Generate on queue report');

Artisan::command('test', function () {
    dd('TEST');
})->describe('Generate on queue report');

Artisan::command('insert-adhoc-specialist', function () {
    $begin = new DateTime('2020-01-01 00:00:00');
    $end = new DateTime('2020-07-01 00:00:00');
    insertITSpecSlaDataAdhoc::runAdhocInsertSpecialist($begin, $end);
})->describe('Adhoc insert IT Specialist SLA Data');

Artisan::command('insert-adhoc-s4', function () {
    $begin  = '2019-09-01';
    $end    = Carbon::now();
    insertITByApproverSlaData::runCheckingSlaITByApproverData($begin, $end);
})->describe('Adhoc insert S4 SLA Data');

Artisan::command('insert-adhoc-proxy-performance', function () {
    $inputDate = $this->ask('Date? yyyy-mm-dd');
    // dd(Carbon::parse($inputDate));
    checkPerformanceProxiesdata::runAdHocInsertPerformanceProxyData($inputDate);
})->describe('Adhoc insert proxy performance data');

Artisan::command('insert-adhoc-itcoord', function () {
    $begin = new DateTime('2022-03-31 00:00:00');
    $end = new DateTime('2022-03-31 11:59:59');
    insertITCoordSlaDataAdhoc::runAdhocInsertITCoord($begin, $end);
})->describe('Adhoc insert IT Coordinator SLA Data');

Artisan::command('insert-adhoc-itserverity', function () {
    $begin = new DateTime('2022-03-01 00:00:00');
    $end = new DateTime('2022-03-31 11:59:59');
    insertITSeveritySlaDataAdhoc::runAdhocInsertSeverity($begin, $end);
})->describe('Adhoc insert IT Severity SLA Data');

Artisan::command('insert-adhoc-service-request', function () {
    $begin  = '2023-01-01';
    $end    = Carbon::now();
    insertITServiceRequestData::runCheckingSlaITServiceRequestData($begin, $end);
})->describe('Adhoc insert Service Request SLA Data');

// Artisan::command('test-alert-pp', function () {
//     AlertProxyPerformance::runCheckProxyPerformance();
// })->describe('Test alert notification for proxy performance');

Artisan::command('insert-talkdesk', function () {
    $date = $this->ask('Please enter a date in the format yyyy-mm-dd');
    InsertTalkdeskData::runInsertTalkdeskData($date);
})->describe('insert aspect data into poms');

Artisan::command('check-talkdesk-report', function () {
    InsertTalkdeskDataByReportFile::runCheckTalkdeskFile();
})->describe('check talkdesk report file and insert into db');
