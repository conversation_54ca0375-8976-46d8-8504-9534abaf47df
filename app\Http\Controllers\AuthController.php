<?php

namespace App\Http\Controllers;

use App\User;
use App\ActionLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Mail\Message;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use Illuminate\Support\Facades\Request as Rquest;

class AuthController extends Controller
{
    public function register(Request $request)
    {
        $v = Validator::make($request->all(), [
            'username' => 'required|unique:users',
            'password' => 'required|min:3|confirmed',
        ]);
        if ($v->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $v->errors()
            ], 422);
        }
        $user = new User;
        $user->username = $request->username;
        $user->name = $request->name;
        $user->email = $request->email;
        $user->password = bcrypt($request->password);
        $user->role = json_encode($request->role);
        $user->save();
        return response()->json(['status' => 'success'], 200);
    }

    public function login(Request $request)
    {
        $credentials = $request->only('username', 'password');
        $user = User::where('username', $request->username)->first();

        if ($user && $user->active == 1 && $token = $this->guard()->attempt($credentials)) {
            User::where('username', $request->username)->update(['last_login' => Carbon::now()]);

            $browser = Rquest::header('User-Agent');
            $ip = Rquest::ip();
            return tap(response()->json(['status' => 'success'], 200)->header('Authorization', $token), function () use ($token, $browser, $ip) {
                ActionLog::saveActionLog('login', ['browser' => $browser, 'ip' => $ip], 'success');
            });
        }

        ActionLog::saveActionLog('login', $request->username, 'failure');
        return response()->json(['error' => 'login_error'], 401);
    }

    public function logout()
    {
        $this->guard()->logout();
        return response()->json([
            'status' => 'success',
            'msg' => 'Logged out Successfully.'
        ], 200);
    }

    public function user(Request $request)
    {
        $user = User::find(Auth::user()->id);
        return response()->json([
            'status' => 'success',
            'data' => $user
        ]);
    }

    public function refresh()
    {
        try {
            JWTAuth::parseToken()->authenticate();
            if ($token = $this->guard()->refresh()) {
                return response()
                    ->json(['status' => 'successs'], 200)
                    ->header('Authorization', $token);
            }
        } catch (\Tymon\JWTAuth\Exceptions\TokenExpiredException $e) {
            return response()->json(['error' => 'refresh_token_error', 'type' => 'TokenExpiredException', 'message' => $e->getMessage()], 401);
        } catch (\Tymon\JWTAuth\Exceptions\TokenInvalidException $e) {
            return response()->json(['error' => 'refresh_token_error', 'type' => 'TokenInvalidException', 'message' => $e->getMessage()], 401);
        } catch (\Tymon\JWTAuth\Exceptions\JWTException $e) {
            return response()->json(['error' => 'refresh_token_error', 'type' => 'JWTException', 'message' => $e->getMessage()], 401);
        }

        return response()->json(['error' => 'refresh_token_error'], 401);
    }

    private function guard()
    {
        return Auth::guard();
    }
}
