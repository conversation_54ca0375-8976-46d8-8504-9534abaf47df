<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class CmsService
{
    public function getFulfilmentDataByDate($date)
    {
        $currentYear = Carbon::now()->yesterday()->year;
        $tableName = 'ep_fulfilment_dtl_' . $currentYear;

        $query = "SELECT fl_latest_status, COUNT(*) AS total FROM $tableName
                WHERE fl_created_date IS NOT NULL 
                AND fl_financial_year = ? 
                AND DATE(fl_created_date) = ?
                AND fl_latest_status IS NOT NULL
                GROUP BY 1 ORDER BY 2 ASC";

        return DB::connection('mysql_cdccms')->select($query, array($currentYear, $date->format('Y-m-d')));
    }

    public function getFulfilmentMonthlyData($year, $month)
    {

        $tableName = 'ep_fulfilment_dtl_' . $year;

        $query = "SELECT DAY(fl_created_date) AS day, COUNT(*) AS total FROM $tableName
        WHERE fl_created_date IS NOT NULL
        AND MONTH(fl_created_date) = ?
        AND fl_financial_year = ?
        AND fl_latest_status IS NOT NULL
        GROUP BY 1";

        return DB::connection('mysql_cdccms')->select($query, array($month, $year));
    }

    public function getTodayBidding()
    {

        $query = "SELECT count(*) as total
        from SC_BID a, SC_BID_SCHEDULE b
        where a.bid_id = b.bid_id
        and b.status_id in (63351, 63352, 63353)
        and trunc(b.start_date) = trunc(sysdate)";

        return DB::connection('oracle_nextgen_rpt')->select($query);
    }

    public function getTomorrowBidding()
    {

        $query = "SELECT count(*) as total
        from SC_BID a, SC_BID_SCHEDULE b
        where a.bid_id = b.bid_id
        and b.status_id in (63351, 63352, 63353)
        and trunc(b.start_date) = trunc(sysdate+1)";

        return DB::connection('oracle_nextgen_rpt')->select($query);
    }

    public function getMonthToDateBidding()
    {
        $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
        $endDate = Carbon::now()->endOfMonth()->format('Y-m-d');

        $query = "SELECT count(*) as total
        from SC_BID a, SC_BID_SCHEDULE b
        where a.bid_id = b.bid_id
        and b.status_id = 63352
        and b.end_date between TO_DATE(?, 'yyyy-mm-dd') AND TO_DATE(?, 'yyyy-mm-dd')";

        return DB::connection('oracle_nextgen_rpt')->select($query, array($startDate, $endDate));
    }

}
