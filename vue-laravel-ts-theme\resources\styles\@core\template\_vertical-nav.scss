@use "@configured-variables" as variables;
@use "@core-scss/base/mixins";
@use "@layouts/styles/mixins" as layoutsMixins;

.layout-nav-type-vertical {
  // 👉 Layout Vertical nav
  .layout-vertical-nav {
    color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));

    @include mixins.elevation(4);

    .nav-header {
      padding-inline-end: 0.125rem;

      .app-logo {
        .app-title {
          font-size: 22px;
        }
      }
    }

    .nav-items {
      padding-block: 0.25rem;
    }

    // 👉 Nav group & Link
    .nav-link,
    .nav-group {
      /* shadow cut issue fix */
      margin-block-end: -0.5rem;
      padding-block-end: 0.5rem;

      a {
        outline: none;
      }
    }

    .nav-section-title .placeholder-icon {
      margin-inline-start: 0.0625rem;
      transform: scaleX(1.6);

      @include layoutsMixins.rtl {
        margin-inline-start: 0.125rem;
      }
    }
  }

  &.layout-vertical-nav-collapsed {
    .layout-vertical-nav:not(.hovered) {
      .nav-header {
        .header-action {
          opacity: 0;
        }
      }
    }
  }
}
