<template>
    <div id="extra-component-chartjs-demo">
        <!-- <h4>SLA Dashboard</h4> -->
        <div class="vx-row" style="display: flex;">
            <div class="vx-col w-full md:w-2/3" style="flex: 2;">
                <ep-user-login style="height: 100%"></ep-user-login>
            </div>
            <div class="vx-col w-full md:w-1/3" style="flex: 1;">
                <ep-system-availability style="height: 100%"></ep-system-availability>
            </div>
        </div>
        <div class="mt-8 vx-row" style="display: flex;">
            <div class="vx-col w-full md:w-1/3" style="flex: 1;">
                <call-center-dashboard style="height: 100%"></call-center-dashboard>
            </div>
            <div class="vx-col w-full md:w-1/3" style="flex: 1;">
                <crm-channel style="height: 100%"></crm-channel>
            </div>
            <div class="vx-col w-full md:w-1/3" style="flex: 1;">
                <crm-cases style="height: 100%"></crm-cases>
            </div>
        </div>
        <div class="mt-8 vx-row">
            <div class="vx-col w-full md:w-1/1">
                <ep-response-time></ep-response-time>
            </div>
        </div>
    </div>
</template>

<style>
    .echarts {
        height: 180px;
    }

    .vx-card {
        height: 100%;
    }

    .con-vs-popup {
        z-index: 53000;
    }
</style>

<script>
    import EpSystemAvailability from './charts/EpSystemAvailability.vue'
    import EpUserLogin from './charts/EpUserLogin.vue'
    import CallCenterDashboard from './charts/CallCenterDashboard.vue'
    import CrmChannel from './charts/CrmChannel.vue'
    import CrmCases from './charts/CrmCases.vue'
    import EpResponseTime from './charts/EpResponseTime.vue'

    export default {
        components: {
            EpSystemAvailability,
            EpUserLogin,
            CallCenterDashboard,
            CrmChannel,
            CrmCases,
            EpResponseTime
        }
    }
</script>
