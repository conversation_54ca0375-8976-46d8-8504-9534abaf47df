<template>
    <div>
        <e-charts :height="250" :data="data" :options="gauge"></e-charts>
        <p class="text-center mb-5">Service Availability</p>
    </div>
</template>

<script>
    import ECharts from "vue-echarts/components/ECharts";
    import "echarts/lib/component/tooltip";
    import "echarts/lib/component/legend";

    import "echarts/theme/dark";

    export default {
        data() {
            return {
                themeDark: false,
                gauge: {
                    grid: {
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 0
                    },
                    tooltip: {
                        formatter: "{a}: <br/>{c}%"
                    },
                    toolbox: {
                        show: true,
                        feature: {
                            mark: {show: true}
                        }
                    },
                    series: [
                        {
                            title: {
                                show: true,
                                offsetCenter: [0, 100],
                                color: "#7367F0",
                                fontSize: 20
                            },
                            name: "Service Availability",
                            type: "gauge",
                            radius: "100%",
                            detail: {
                                formatter: "{value}%",
                                offsetCenter: [0, "60%"],
                                textStyle: {
                                    fontSize: 16
                                }
                            },
                            data: [
                                {
                                    value: 99.25
                                    // name: 'System'
                                }
                            ],
                            axisLine: {
                                lineStyle: {
                                    color: [[0.8, "#ea5455"], [1, "#28c76f"]],
                                    width: 20
                                }
                            },
                            splitLine: {
                                show: true,
                                length: 30,
                                lineStyle: {
                                    color: "auto"
                                }
                            },
                            axisTick: {
                                splitNumber: 5,
                                length: 8
                            }
                        }
                    ],
                    animationDuration: 2000
                }
            };
        },
        components: {
            ECharts
        },
        mounted() {
            this.getGaugeDemo();
        },

        methods: {
            getGaugeDemo: function () {
                let chart = this.gauge;
                setInterval(function () {
                    chart.series[0].data[0].value = (Math.random() * (100 - 75) + 75).toFixed(2) - 0;
                }, 2000);
            }
        }
    };
</script>
