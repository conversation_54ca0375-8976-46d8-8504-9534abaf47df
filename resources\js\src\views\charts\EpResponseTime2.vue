<template>
    <div class="vx-col w-full mb-base">
        <vx-card title="16 SLA Response Time (Average)">
            <template slot="">
                <div class="vx-row">
                    <div class="vx-col w-full md:w-1/2">
                        <div v-for="(response, index) in responseTimeData" :key="response.id" :class="{'mt-4': index}">
                            <div v-if="index % 2 === 0" class="flex justify-between">
                                <div class="flex flex-col whitespace-no-wrap" style="width: 50%;">
                                    <span class="mb-1">{{ response.name }}</span>
                                </div>
                                <vs-progress :percent="response.ratio"
                                             :color=" response.ratio < 80 ? 'danger' : 'success'"></vs-progress>
                                <div class="flex flex-col text-right">
                            <span class="flex -mr-1">
                                <h5 class="mr-1">{{ response.ratio }}%</h5>
                                <feather-icon
                                    :icon=" response.ratio < 80 ? 'FrownIcon' : 'SmileIcon'"
                                    :svgClasses="[response.ratio < 80 ? 'text-danger' : 'text-success'  ,
                                    'stroke-current h-4 w-4 mb-1 mr-1']">
                                </feather-icon>
                            </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="vx-col w-full md:w-1/2">
                        <div v-for="(response, index) in responseTimeData" :key="response.id" :class="{'mt-4': index}">
                            <div v-if = "index % 2 === 1" class="flex justify-between">
                                <div class="flex flex-col whitespace-no-wrap" style="width: 50%;">
                                    <span class="mb-1">{{ response.name }}</span>
                                </div>
                                <vs-progress :percent="response.ratio"
                                             :color=" response.ratio < 80 ? 'danger' : 'success'"></vs-progress>
                                <div class="flex flex-col text-right">
                            <span class="flex -mr-1">
                                <h5 class="mr-1">{{ response.ratio }}%</h5>
                                <feather-icon
                                    :icon=" response.ratio < 80 ? 'FrownIcon' : 'SmileIcon'"
                                    :svgClasses="[response.ratio < 80 ? 'text-danger' : 'text-success'  ,
                                    'stroke-current h-4 w-4 mb-1 mr-1']">
                                </feather-icon>
                            </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </template>
            <div class="flex justify-between text-center" slot="no-body-bottom">
            </div>
        </vx-card>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                responseTimeData: [
                    {
                        id: 1,
                        name: "General Login",
                        ratio: 98,
                    },
                    {
                        id: 2,
                        name: "Search for Template",
                        ratio: 79,
                    },
                    {
                        id: 3,
                        name: "Load First Page of Image",
                        ratio: 89,
                    },
                    {
                        id: 4,
                        name: "Invoice Creation",
                        ratio: 96,
                    },
                    {
                        id: 5,
                        name: "Proposal Submission",
                        ratio: 91,
                    },
                    {
                        id: 6,
                        name: "Purchase Request Creation",
                        ratio: 91,
                    },
                    {
                        id: 7,
                        name: "Online Bidding",
                        ratio: 91,
                    },
                    {
                        id: 8,
                        name: "Contract Submission",
                        ratio: 91,
                    },
                    {
                        id: 9,
                        name: "Contract Display",
                        ratio: 91,
                    },
                    {
                        id: 10,
                        name: "Purchase Requisition Submission",
                        ratio: 91,
                    },
                    {
                        id: 11,
                        name: "Request Note (RN) Creation",
                        ratio: 91,
                    },
                    {
                        id: 12,
                        name: "Invoice Creation",
                        ratio: 91,
                    },
                    {
                        id: 13,
                        name: "Submission of MOF Account Application",
                        ratio: 91,
                    },
                    {
                        id: 14,
                        name: "Catalogue Search",
                        ratio: 91,
                    },
                    {
                        id: 15,
                        name: "Load One Basic BI Report",
                        ratio: 91,
                    },
                    {
                        id: 16,
                        name: "Course Search",
                        ratio: 91,
                    },

                ]
            }
        }
    }
</script>
