<?php

/*
 * Function : To read the content from text file and insert into table 'performance_proxies', filter to prevent inserting duplicate data
 * Date : 27 Aug 2019
 * Author : Nur Asmaa
 */

namespace App\Nagios\Performance;

use Carbon\Carbon;
use DateTime;
use Ramsey\Uuid\Uuid;
use App\Nagios\MigrateUtils;
use Config;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class checkPerformanceProxiesdata
{

    public static function runCheckingPerformProxyData($insertDate = null)
    {

        Log::debug(self::class . ' Starting ... Checking Performance Proxies Data ', ['Query to Insert Date' => $insertDate]);

        self::checkPerformanceProxiesdata($insertDate);
    }

    public static function runAdHocInsertPerformanceProxyData($insertDate = null)
    {
        Log::debug(self::class . ' Starting ... AdHoc Checking Performance Proxies Data ', ['Query to Insert Date' => $insertDate]);

        self::checkPerformanceProxiesdata($insertDate);
    }

    /* Checking file and open it */

    private static function checkPerformanceProxiesdata($insertDate)
    {
        $startTime = Carbon::now();
        Log::info(self::class . ' Start : ' . __FUNCTION__ . ' ->> Insert Date: ' . $insertDate . ' ->> Start Time: ' . $startTime);

        $yesterdayData = Carbon::parse($insertDate);
        $getCurr = strtotime($yesterdayData);
        $processDate = date('Ymd', $getCurr);
        $url = "/data/ep-data-" . $processDate . ".dat";

        $open = fopen($url, 'r');

        // Get line count of the file
        $lineCount = 0;
        while (!feof($open)) {
            fgets($open);
            $lineCount++;
        }

        // Reset file pointer to start
        fseek($open, 0);

        // Display line count
        Log::info('Total lines in file: ' . $lineCount);

        // Start a transaction
        DB::beginTransaction();

        try {
            $data = [];
            $tempData = [];

            while (!feof($open)) {
                $getTextLine = fgets($open);
                $explodeLineResult = array_pad(explode(',', $getTextLine), 5, null);
                list($dateTime, $module, $transaction, $durationResult, $userDetails) = $explodeLineResult;

                // Trim the variable
                $userDetails = trim($userDetails);

                // Ensure datetime is not null
                if ($dateTime !== null && $module !== null && $transaction !== null && $userDetails !== null) {
                    /*format to millisecond*/
                    $dateTimeFormatOri = new DateTime($dateTime);
                    $dateTimeFormat = $dateTimeFormatOri->format('Y-m-d H:i:s.u');

                    // Generate a unique key for each entry
                    $uniqueKey = $dateTimeFormat . $transaction . $durationResult . $userDetails;

                    // Check if data is unique both in the array and the database
                    if (!isset($tempData[$uniqueKey]) && !DB::table('performance_proxies')
                        ->where('date_time', $dateTimeFormat)
                        ->where('transaction', $transaction)
                        ->where('duration_result', $durationResult)
                        ->where('user_details', $userDetails)
                        ->exists()) {
                        $data[] = [
                            'date_time' => $dateTimeFormat,
                            'module' => $module,
                            'transaction' => $transaction,
                            'duration_result' => $durationResult,
                            'user_details' => $userDetails
                        ];
                        $tempData[$uniqueKey] = true; // Mark as seen
                    }
                }
            }

            // Bulk insert data in batches
            if (!empty($data)) {
                $chunkedData = array_chunk($data, 1000);
                foreach ($chunkedData as $chunk) {
                    DB::table('performance_proxies')->insert($chunk);
                    Log::info('Inserted chunk of ' . count($chunk) . ' records');
                }
            }

            // Commit transaction
            DB::commit();

            Log::info(self::class . ' Finished : ' . __FUNCTION__ . ' > ' . MigrateUtils::getTakenTime($startTime));

        } catch (Exception $e) {
            // Rollback transaction on error
            DB::rollBack();
            Log::error('Error: ' . $e->getMessage());
        }

        fclose($open);
    }
}
