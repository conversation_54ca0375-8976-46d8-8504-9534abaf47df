<template>
    <div>
        <h2>Articles</h2>

        <div class="vx-row">

            <!-- HORIZONTAL LAYOUT -->
            <div class="vx-col md:w-1/1 w-full mb-base">
                <vx-card title="Add Article">
                    <div class="vx-row mb-6">
                        <div class="vx-col sm:w-1/6 w-full">
                            <span>Title</span>
                        </div>
                        <div class="vx-col sm:w-5/6 w-full">
                            <vs-input class="w-full" v-model="article.title"/>
                        </div>
                    </div>
                    <div class="vx-row mb-6">
                        <div class="vx-col sm:w-1/6 w-full">
                            <span>Body</span>
                        </div>
                        <div class="vx-col sm:w-5/6 w-full">
                            <vs-textarea class="w-full" v-model="article.body"/>
                        </div>
                    </div>
                    <div class="vx-row">
                        <div class="vx-col sm:w-2/3 w-full ml-auto">
                            <vs-button class="mr-3 mb-2" @click="addArticle">Save</vs-button>
                            <vs-button color="warning" type="border" class="mb-2"
                                       @click="reset">Reset
                            </vs-button>
                        </div>
                    </div>
                </vx-card>
            </div>
        </div>

        <p>Current: {{ currentx }}</p>
        <div>
            <vs-pagination :total="pagination.last_page" v-model="currentx"
                           @change="pageChange"
                           :max="pagination.last_page"></vs-pagination>
        </div>
        <vx-card class="mt-2 mb-2" :title="article.title" v-for="article in articles" v-bind:key="article.id">
            <p>{{ article.body }}</p>
            <vs-button color="warning" type="filled" @click="editArticle(article)">Edit</vs-button>
            <vs-button color="danger" type="filled" @click="deleteArticle(article.id)">Delete</vs-button>
        </vx-card>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                currentx: 1,
                articles: [],
                article: {
                    id: '',
                    title: '',
                    body: ''
                },
                article_id: '',
                pagination: {},
                edit: false
            }
        },

        created() {
            this.fetchArticles();
        },

        methods: {
            fetchArticles(page_url) {
                let vm = this;
                page_url = page_url || '/api/articles';
                fetch(page_url)
                    .then(res => res.json())
                    .then(res => {
                        this.articles = res.data;
                        vm.makePagination(res.meta, res.links);
                    })
                    .catch(err => console.log(err));
            },

            makePagination(meta, links) {
                let pagination = {
                    current_page: meta.current_page,
                    last_page: meta.last_page,
                    next_page_url: links.next,
                    prev_page_url: links.prev,
                    api_path: meta.path
                };

                this.pagination = pagination;
            },

            pageChange: function (evt) {
                this.$emit("change", evt);
                console.log(this.currentx);
                this.fetchArticles(this.pagination.api_path + '?page=' + this.currentx);
            },

            deleteArticle(id) {
                if (confirm('Are you Sure?')) {

                    fetch(`api/article/${id}`, {
                        method: 'delete'
                    })
                        .then(res => res.json())
                        .then(data => {
                            alert('Article Removed');
                            this.fetchArticles();
                        })
                        .catch(err => console.log(err));

                }
            },

            addArticle() {
                if (this.edit === false) {
                    // Edit
                    fetch('api/article', {
                        method: 'post',
                        body: JSON.stringify(this.article),
                        headers: {
                            'content-type': 'application/json'
                        }
                    })
                        .then(res => res.json())
                        .then(data => {
                            this.article.title = '';
                            this.article.body = '';
                            alert('Article Added');
                            this.fetchArticles();
                        })
                        .catch(err => console.log(err));
                } else {
                    // Update
                    fetch('api/article', {
                        method: 'put',
                        body: JSON.stringify(this.article),
                        headers: {
                            'content-type': 'application/json'
                        }
                    })
                        .then(res => res.json())
                        .then(data => {
                            this.article.title = '';
                            this.article.body = '';
                            alert('Article Updated');
                            this.fetchArticles();
                        })
                        .catch(err => console.log(err));

                }
            },

            editArticle(article) {
                this.edit = true;
                this.article.id = article.id;
                this.article.article_id = article.id;
                this.article.title = article.title;
                this.article.body = article.body;

                window.scroll({
                    top: 0,
                    left: 0,
                    behavior: 'smooth'
                })

            },

            reset() {
                this.edit = false;
                this.article.id = '';
                this.article.article_id = '';
                this.article.title = '';
                this.article.body = '';
            }
        }
    }
</script>

<style scoped>

</style>