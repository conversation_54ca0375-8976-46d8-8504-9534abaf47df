<!-- =========================================================================================
    File Name: ChartBarChart.vue
    Description: Create bar chart
    ----------------------------------------------------------------------------------------
    Item Name: Vuesax Admin - VueJS Dashboard Admin Template
      Author: Pixinvent
    Author URL: http://www.themeforest.net/user/pixinvent
========================================================================================== -->


<template>
    <vx-card title="Gauge Chart" class="mb-base">

        <div class="mt-5">
            <chartjs-component-gauge-chart :height=250 :data="data" :options="options"></chartjs-component-gauge-chart>
        </div>
    </vx-card>
</template>

<script>
    import ChartjsComponentGaugeChart from "../../components/charts-components/ChartjsComponentGaugeChart.vue"

    export default {
        data() {
            return {
                data: {
                    labels: ["Danger", "Good"],
                    datasets: [{
                        label: 'GitHub Commits',
                        backgroundColor: ['#28C76F', '#EA5455'],
                        borderWidth: 0,
                        hoverBorderWidth: 0,
                        data: [80, 20],
                    }],
                },
                options: {
                    cutoutPercentage: 0,
                    rotation: -3.1415926535898,
                    circumference: 3.1415926535898,
                }
            }
        },
        components: {
            ChartjsComponentGaugeChart
        }
    }
</script>