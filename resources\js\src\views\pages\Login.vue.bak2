<!-- =========================================================================================
    File Name: Login.vue
    Description: Login Page
    ----------------------------------------------------------------------------------------
    Item Name: Vuesax Admin - VueJS Dashboard Admin Template
      Author: Pixinvent
    Author URL: http://www.themeforest.net/user/pixinvent
========================================================================================== -->


<template>
    <div class="h-screen flex w-full bg-img vx-row no-gutter items-center justify-center" id="page-login">
        <div class="vx-col sm:w-1/2 md:w-1/2 lg:w-3/4 xl:w-3/5 sm:m-0 m-4">
            <vx-card>
                <div slot="no-body" class="full-page-bg-color">
                    <div class="vx-row no-gutter justify-center items-center">
                        <div class="vx-col hidden lg:block lg:w-1/2">
                            <img src="@assets/images/pages/login.png" alt="login" class="mx-auto">
                        </div>
                        <div class="vx-col sm:w-full md:w-full lg:w-1/2 d-theme-dark-bg">
                            <div class="p-8">
                                <div class="vx-card__title mb-8">
                                    <h4 class="mb-4"><strong>POMS</strong> Login</h4>
                                    <p>Welcome, please login to your account.</p>
                                </div>
                                <vs-input
                                    name="username"
                                    icon="icon icon-user"
                                    icon-pack="feather"
                                    label-placeholder="Username"
                                    v-model="username"
                                    class="w-full no-icon-border" @keyup.enter="triggerEnter"/>

                                <vs-input
                                    type="password"
                                    name="password"
                                    icon="icon icon-lock"
                                    icon-pack="feather"
                                    label-placeholder="Password"
                                    v-model="password"
                                    class="w-full mt-6 no-icon-border" @keyup.enter="triggerEnter"/>

                                <div class="flex flex-wrap justify-between my-5">
                                    <vs-alert v-if="has_error" color="danger" icon-pack="feather" icon="icon-info">
                                        <span class="text-sm">Login failed. Please check username and password.</span>
                                    </vs-alert>
                                    <!--<vs-checkbox v-model="checkbox_remember_me" class="mb-3">Remember Me</vs-checkbox>
                                    <router-link to="#">Forgot Password?</router-link>-->
                                </div>
                                <!--<vs-button type="border">Register</vs-button>
                                <vs-button class="float-right">Login</vs-button>-->
                                <vs-button class="float-right mb-8" @click="login">Login</vs-button>

                            </div>
                        </div>
                    </div>
                </div>
            </vx-card>
        </div>
    </div>
</template>

<script>

    export default {
        data() {
            return {
                username: null,
                password: null,
                has_error: false,
                checkbox_remember_me: false
            }
        },

        mounted() {
            //
        },

        methods: {
            login() {
                // get the redirect object
                var redirect = this.$auth.redirect()
                var app = this
                this.$auth.login({
                    params: {
                        username: app.username,
                        password: app.password
                    },
                    success: function () {
                        // handle redirection
                        // const redirectTo = redirect ? redirect.from.name : this.$auth.user().role === 2 ? '' : ''
                        // this.$router.push({name: redirectTo})
                    },
                    error: function () {
                        app.has_error = true
                    },
                    checkbox_remember_me: true,
                    fetchUser: true
                })
            },

            triggerEnter() {
                this.login();
            }

        }
    }
</script>

<style lang="scss">
    #page-login {
        .social-login {
            .bg-facebook {
                background-color: #1551b1;
            }

            .bg-twitter {
                background-color: #00aaff;
            }

            .bg-google {
                background-color: #4285F4;
            }

            .bg-github {
                background-color: #333;
            }
        }
    }
</style>
