<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Parameter extends Model
{
    protected $connection = 'mysql';
    protected $table = 'parameters';

    const UPDATED_AT = 'modified_date';

    protected $fillable = [
        'name',
        'value',
        'description',
        'type',
        'modified_date',
    ];

    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
            'modified_date' => 'datetime',
        ];
    }
}
