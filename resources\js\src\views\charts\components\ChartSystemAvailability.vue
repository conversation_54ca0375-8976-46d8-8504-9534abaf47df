<template>
    <div>
        <e-charts :height="250" :options="gauge"></e-charts>
    </div>
</template>

<script>
    import ECharts from "vue-echarts/components/ECharts";
    import "echarts/lib/component/tooltip";
    import "echarts/lib/component/legend";

    import "echarts/theme/dark";

    export default {
        data() {
            return {
                nagiosData: null,
                themeDark: false,
                gauge: {
                    grid: {
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 0
                    },
                    tooltip: {
                        // formatter: "{a} <br/>{b} : {c}%"
                        formatter: "{a}: <br/>{c}%"
                    },
                    toolbox: {
                        show: true,
                        feature: {
                            mark: {show: true}
                        }
                    },
                    series: [
                        {
                            title: {
                                show: true,
                                offsetCenter: [0, 100],
                                color: "#7367F0",
                                fontSize: 20
                            },
                            name: "System Availability",
                            type: "gauge",
                            radius: "100%",
                            detail: {
                                formatter: "{value}%",
                                offsetCenter: [0, "60%"],
                                textStyle: {
                                    fontSize: 16
                                }
                            },
                            data: [
                                {
                                    value: 96.66
                                    // name: 'System'
                                }
                            ],
                            axisLine: {
                                lineStyle: {
                                    color: [[0.8, "#ea5455"], [1, "#28c76f"]],
                                    width: 20
                                }
                            },
                            splitLine: {
                                show: true,
                                length: 30,
                                lineStyle: {
                                    color: "auto"
                                }
                            },
                            axisTick: {
                                splitNumber: 5,
                                length: 8
                            }
                        }
                    ],
                    animationDuration: 2000
                }
            };
        },

        components: {
            ECharts
        },

        mounted() {
            var vm = this;
            // this.getGaugeDemo();
            vm.fetchNagiosData();
            setInterval(function () {
                vm.fetchNagiosData();
            },300000); // 5 min
        },

        methods: {

            fetchNagiosData: function () {
                let chart = this.gauge;
                let api_url = '/api/nagios/hostlist';
                this.$vs.loading({
                    container: '#card-system-availability',
                    scale: 0.6
                });
                fetch(api_url)
                    .then(res => res.json())
                    .then(res => {
                        this.nagiosData = res.data;
                        chart.series[0].data[0].value = this.nagiosData.host_up_percentage;
                        this.$vs.loading.close('#card-system-availability > .con-vs-loading');
                    })
                    .catch(err => console.log(err));
            }
        }
    };
</script>
