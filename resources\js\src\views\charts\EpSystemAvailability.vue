<template>
    <vx-card id="card-system-availability" title="ePerolehan System Availability"
             class="mb-base vs-con-loading__container">
        <div class="w-full md:w-1/1 mt-8" slot="no-body">
            <chart-system-availability></chart-system-availability>
            <div class="flex justify-center items-center bg-grey-light">
                <div class="inline-flex justify-between py-1 px-6">
                    <span class="flex items-center">
                        <span class="inline-block h-3 w-3 rounded-full mr-2 bg-success"></span>
                        <span class="font-semibold">Up</span>
                    </span>
                </div>
                <div class="inline-flex justify-between py-1 px-6">
                    <span class="flex items-center">
                        <span class="inline-block h-3 w-3 rounded-full mr-2 bg-danger"></span>
                        <span class="font-semibold">Down</span>
                    </span>
                </div>
                <div class="inline-flex justify-between py-1 px-6">
                    <span class="flex items-center">
                        <span class="inline-block h-3 w-3 rounded-full mr-2 bg-warning"></span>
                        <span class="font-semibold">Passive</span>
                    </span>
                </div>
            </div>
            <!-- CHART DATA -->
            <div style="position: relative;">
                <ul class="mb-1">
                    <li class="flex justify-between py-1 px-6 border d-theme-border-grey-light border-solid border-r-0 border-l-0 border-b-0">
                        <span class="flex items-center">
                            <span v-html="portalMainStatus"></span>
                            <span class="font-semibold">Portal</span>
                        </span>
                        <span v-if="nagiosHostData.portal">
                            <a v-for="item in nagiosHostData.portal.host" style="margin: 0; padding: 0">
                                <vx-tooltip
                                    :class="['inline-block', 'h-3', 'w-3', 'rounded-full mr-2', item.availability_status === 'UP' ? 'bg-success' : 'bg-danger']"
                                    :text="item.host_name" position="top">
                                </vx-tooltip>
                            </a>
                        </span>
                    </li>
                    <li class="flex justify-between py-1 px-6 border d-theme-border-grey-light border-solid border-r-0 border-l-0 border-b-0">
                        <span class="flex items-center">
                          <span v-html="webMainStatus"></span>
                          <span class="font-semibold">Web</span>
                        </span>
                        <span v-if="nagiosHostData.web">
                            <a v-for="item in nagiosHostData.web.host" style="margin: 0; padding: 0">
                                <vx-tooltip
                                    :class="['inline-block', 'h-3', 'w-3', 'rounded-full mr-2', item.availability_status === 'UP' ? 'bg-success' : 'bg-danger']"
                                    :text="item.host_name" position="top">
                                </vx-tooltip>
                            </a>
                        </span>
                    </li>
                    <li class="flex justify-between py-1 px-6 border d-theme-border-grey-light border-solid border-r-0 border-l-0 border-b-0">
                        <span class="flex items-center">
                          <span class="inline-block h-3 w-3 rounded-full mr-2 bg-success"></span>
                          <span class="font-semibold">Network</span>
                        </span>
                        <span v-if="nagiosHostData.network">
                            <a v-for="item in nagiosHostData.network.host" style="margin: 0; padding: 0">
                                <vx-tooltip
                                    :class="['inline-block', 'h-3', 'w-3', 'rounded-full mr-2', item.availability_status === 'UP' ? 'bg-success' : item.availability_status === 'PASSIVE' ? 'bg-warning' : 'bg-danger']"
                                    :text="item.host_name" position="top">
                                </vx-tooltip>
                            </a>
                        </span>
                    </li>
                    <li class="flex justify-between py-1 px-6 border d-theme-border-grey-light border-solid border-r-0 border-l-0 border-b-0">
                        <span class="flex items-center">
                          <span v-html="ssoMainStatus"></span>
                          <span class="font-semibold">SSO</span>
                        </span>
                        <span v-if="nagiosHostData.sso">
                            <a v-for="item in nagiosHostData.sso.host" style="margin: 0; padding: 0">
                                <vx-tooltip
                                    :class="['inline-block', 'h-3', 'w-3', 'rounded-full mr-2', item.availability_status === 'UP' ? 'bg-success' : 'bg-danger']"
                                    :text="item.host_name" position="top">
                                </vx-tooltip>
                            </a>
                        </span>
                    </li>
                    <li class="flex justify-between py-1 px-6 border d-theme-border-grey-light border-solid border-r-0 border-l-0 border-b-0">
                        <span class="flex items-center">
                          <span v-html="solrMainStatus"></span>
                          <span class="font-semibold">SOLR (HA)</span>
                        </span>
                        <span v-if="nagiosHostData.solr">
                            <a v-for="item in nagiosHostData.solr.host" style="margin: 0; padding: 0">
                                <vx-tooltip
                                    :class="['inline-block', 'h-3', 'w-3', 'rounded-full mr-2', item.availability_status === 'UP' ? 'bg-success' : 'bg-danger']"
                                    :text="item.host_name" position="top">
                                </vx-tooltip>
                            </a>
                        </span>
                    </li>
                    <li class="flex justify-between py-1 px-6 border d-theme-border-grey-light border-solid border-r-0 border-l-0 border-b-0">
                        <span class="flex items-center">
                          <span v-html="dbMainStatus"></span>
                          <span class="font-semibold">Database</span>
                        </span>
                        <span v-if="nagiosHostData.database">
                            <a v-for="item in nagiosHostData.database.host" style="margin: 0; padding: 0">
                                <vx-tooltip
                                    :class="['inline-block', 'h-3', 'w-3', 'rounded-full mr-2', item.availability_status === 'UP' ? 'bg-success' : 'bg-danger']"
                                    :text="item.host_name" position="top">
                                </vx-tooltip>
                            </a>
                        </span>
                    </li>
                    <li class="flex justify-between py-1 px-6 border d-theme-border-grey-light border-solid border-r-0 border-l-0 border-b-0">
                        <span class="flex items-center">
                          <span v-html="bpmMainStatus"></span>
                          <span class="font-semibold">BPM</span>
                        </span>
                        <span v-if="nagiosHostData.bpm">
                            <a v-for="item in nagiosHostData.bpm.host" style="margin: 0; padding: 0">
                                <vx-tooltip
                                    :class="['inline-block', 'h-3', 'w-3', 'rounded-full mr-2', item.availability_status === 'UP' ? 'bg-success' : 'bg-danger']"
                                    :text="item.host_name" position="top">
                                </vx-tooltip>
                            </a>
                        </span>

                    </li>
                </ul>
            </div>
        </div>
    </vx-card>
</template>

<script>
    import ChartSystemAvailability from "./components/ChartSystemAvailability.vue";
    // import ChartServiceAvailability from "./components/ChartServiceAvailability.vue";

    var hostUp = '<span class="inline-block h-3 w-3 rounded-full mr-2 bg-success"></span>'
    var hostDown = '<span class="inline-block h-3 w-3 rounded-full mr-2 bg-danger"></span>'

    export default {
        data() {
            var hostDefault = hostDown
            return {
                portalMainStatus: hostDefault,
                webMainStatus: hostDefault,
                networkMainStatus: hostDefault,
                ssoMainStatus: hostDefault,
                solrMainStatus: hostDefault,
                dbMainStatus: hostDefault,
                bpmMainStatus: hostDefault,
                nagiosHostData: [],
            }
        },
        components: {
            ChartSystemAvailability,
            // ChartServiceAvailability
        },
        mounted() {
            var vm = this;
            vm.fetchNagiosData();
            setInterval(function () {
                vm.fetchNagiosData();
            }, 300000); // 5 min
        },
        methods: {
            fetchNagiosData: function () {
                let chart = this.gauge;
                let api_url = '/api/nagios/hostlist';
                fetch(api_url)
                    .then(res => res.json())
                    .then(res => {
                        var host = res.data;
                        this.nagiosHostData = host;

                        if (host.portal.host_availability === "UP") {
                            this.portalMainStatus = hostUp;
                        }
                        if (host.web.host_availability === "UP") {
                            this.webMainStatus = hostUp;
                        }
                        if (host.network.host_availability === "UP") {
                            this.networkMainStatus = hostUp;
                        }
                        if (host.sso.host_availability === "UP") {
                            this.ssoMainStatus = hostUp;
                        }
                        if (host.solr.host_availability === "UP") {
                            this.solrMainStatus = hostUp;
                        }
                        if (host.database.host_availability === "UP") {
                            this.dbMainStatus = hostUp;
                        }
                        if (host.bpm.host_availability === "UP") {
                            this.bpmMainStatus = hostUp;
                        }
                    })
                    .catch(err => console.log(err));
            },

            setHtml: function (hostname, status) {
                var style = "";
                if (status === 2) {
                    style = "bg-success";
                } else {
                    style = "bg-danger"
                }
                return style;
            },

            checkHostAvailability: function (hostAvailability) {
                if (hostAvailability === "UP") {
                    this.portalMainStatus = this.hostUp;
                }
            }
        }
    };
</script>
