<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class SlaReportResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            'id'    => $this->id,
            'name' => $this->name,
            'file_name' => $this->file_name,
            'status'  => $this->status,
            'deleted' => $this->deleted,
            'hidden' => $this->hidden,
            'created_at'  => $this->created_at,
            'created_at_format'  => Carbon::parse($this->created_at)->format('d/m/Y h:i A'),
            'created_by'  => $this->created_by
        ];
    }

    public function with($request)
    {
        return [
            'version'       => '1.0.0',
            'author_url'    => 'http://www.commercedc.com.my/'
        ];
    }
}
