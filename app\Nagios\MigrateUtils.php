<?php

namespace App\Nagios;
use Carbon\Carbon;
use Carbon\CarbonInterval;

class MigrateUtils {

    public static function getTakenTime($dtStartTime) {
        return $takentime = array(
            'TakenTime' => $dtStartTime->diffForHumans(Carbon::now()),
            'TakenTimePerMinutes' => $dtStartTime->diffInMinutes(Carbon::now()),
            'TakenTimePerSeconds' => $dtStartTime->diffInSeconds(Carbon::now())
        );
    }
    
    public static function getDateStartByPeriod($periodMinute){
       $startCarbonInterval = CarbonInterval::create(0, 0, 0, 0, 0, $periodMinute, 0); 
       $cDateStart = Carbon::now()->sub($startCarbonInterval);
       $cDateStart->subSecond($cDateStart->second); //Set second clear 00
       return $cDateStart;
    }
    
    public static function getDateEndByPeriod(Carbon $cDateStart , $periodMinute){
       $endCarbonInterval = CarbonInterval::create(0, 0, 0, 0, 0, $periodMinute, 0); 
       $cDateEnd = new Carbon($cDateStart->format('Y-m-d H.i.s'));
       $cDateEnd->add($endCarbonInterval);
       return $cDateEnd;
    }

}
