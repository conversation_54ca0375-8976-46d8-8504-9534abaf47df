<?php

namespace App\Services;

use DB;
use Carbon\Carbon;
use App\Tasks;
use App\TaskCustom;
use App\Cases;
use <PERSON>\Uuid\Uuid;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
class CRMService {

    public static $CRM_GROUP_USER= array(

            'Group Business Coordinator' =>
                array(
                    'name' => 'Group Business Coordinator (Case Owner)',
                    'name_task' => 'Assigned to Case Owner',
                    'user_group_id' => '2732c16c-1fff-9e6c-bb4e-58997edd3340',
                    'assign_group' => 'Case Owner'  // Dont Changed It
                ),
            'Group Production Support' =>
                array(
                    'name' => 'Group Production Support',
                    'name_task' => 'Assigned to Production Support',
                    'user_group_id' => 'd3bf216c-122b-4ce5-9410-899317762b60',
                    'assign_group' => 'Group IT Specialist(Production Support)'  // Dont Changed It
                ),
            'Group Archisoft Build Team' =>
                array(
                    'name' => 'Group Archisoft Build Team',
                    'name_task' => 'Assigned to Group Archisoft Build Team',
                    'user_group_id' => '15c7bd74-12f3-311b-9e8f-5a810702a1a5',
                    'assign_group' => 'Archissoft Build Team'  // Dont Changed It
                ),
            'Group Middleware' =>
                array(
                    'name' => 'Group Middleware',
                    'name_task' => 'Assigned to Group Middleware',
                    'user_group_id' => '5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e',
                    'assign_group' => 'Group Middleware'  // Dont Changed It
                ),
            'Group IT Network Admin' =>
                array(
                    'name' => 'Group IT Network Admin',
                    'name_task' => 'Assigned to Group IT Specialist(Network Admin)',
                    'user_group_id' => 'bb59c521-4a4d-a001-a8aa-58d09f694ae7',
                    'assign_group' => 'Group IT Specialist(Network Admin)'  // Dont Changed It
                )
        );

    public static $CRM_GROUP_USER_SUPPORT= array(

            'Group Business Coordinator' =>
                array(
                    'name' => 'Group Business Coordinator (Case Owner)',
                    'name_task' => 'Assigned to Case Owner',
                    'user_group_id' => '2732c16c-1fff-9e6c-bb4e-58997edd3340',
                    'assign_group' => 'Case Owner'  // Dont Changed It
                ),
            'Group Production Support' =>
                array(
                    'name' => 'Group Production Support',
                    'name_task' => 'Assigned to Production Support',
                    'user_group_id' => 'd3bf216c-122b-4ce5-9410-899317762b60',
                    'assign_group' => 'Group IT Specialist(Production Support)'  // Dont Changed It
                )
        );

    public static $IT_COORDINATOR_ID = 'bd305f97-902e-e186-f506-58997eeecc12';

    public function getDetailLookupCRM($type,$value){
        $query = DB::table('cstm_list_app');
        $query->where('type_code', $type);
        $query->where('value_code', $value);
        $data = $query->first();
        return $data;
    }

    public function getDetailUserCRM($id){
        $query = DB::table('users');
        $query->where('id', $id);
        $data = $query->first();
        return $data;
    }

    public function getDetailUserCRMByUsername($username){
        $query = DB::table('users');
        $query->where('user_name', $username);
        $data = $query->first();
        return $data;
    }

    public function getDetailContactCRM($id){
        $query = DB::table('contacts');
        $query->where('id', $id);
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountCRM($id){
        if($id == null){ return null;}

        $query = DB::table('accounts');
        $query->where('id', $id);
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountPtjToKementerianCRM($id){
        $query = DB::table('accounts as ptj');
        $query->join('accounts as kumpulanptj', 'ptj.parent_id', '=', 'kumpulanptj.id');
        $query->join('accounts as pegawaipengawal', 'kumpulanptj.parent_id', '=', 'pegawaipengawal.id');
        $query->join('accounts as kementerian', 'pegawaipengawal.parent_id', '=', 'kementerian.id');
        $query->where('ptj.id', $id);
        $query->where('ptj.deleted', 0);
        $query->select('ptj.name as ptj_name','kumpulanptj.name as kumpulanptj_name','pegawaipengawal.name as pegawaipengawal_name','kementerian.name as kementerian_name');
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountKumpulanPtjToKementerianCRM($id){
        $query = DB::table('accounts as kumpulanptj');
        $query->join('accounts as pegawaipengawal', 'kumpulanptj.parent_id', '=', 'pegawaipengawal.id');
        $query->join('accounts as kementerian', 'pegawaipengawal.parent_id', '=', 'kementerian.id');
        $query->where('kumpulanptj.id', $id);
        $query->where('kumpulanptj.deleted', 0);
        $query->select('kumpulanptj.name as kumpulanptj_name','pegawaipengawal.name as pegawaipengawal_name','kementerian.name as kementerian_name');
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountPegawaiPengawalToKementerianCRM($id){
        $query = DB::table('accounts as pegawaipengawal');
        $query->join('accounts as kementerian', 'pegawaipengawal.parent_id', '=', 'kementerian.id');
        $query->where('pegawaipengawal.id', $id);
        $query->where('pegawaipengawal.deleted', 0);
        $query->select('pegawaipengawal.name as pegawaipengawal_name','kementerian.name as kementerian_name');
        $data = $query->first();
        return $data;
    }

    public function getDetailCase($caseNumber){
        $query = DB::table('cases');
        $query->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c');
        $query->where('cases.case_number', $caseNumber);
        $data = $query->first();
        return $data;
    }

    public function getDetailTaskLatestCRM($caseId){
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c');
        $query->where('tasks.parent_id', $caseId);
        $query->where('tasks.parent_type', 'Cases');
        $query->where('tasks.deleted', 0);
        $query->orderBy('tasks_cstm.task_number_c','desc');
        $data = $query->first();
        return $data;
    }

    public function getDetailCaseAndTaskLatestCRM($caseNumber){
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->where('cases.case_number', $caseNumber);
        $query->where('tasks.parent_type', 'Cases');
        $query->where('tasks.deleted', 0);
        $query->select('tasks.*');
        $query->addSelect('tasks_cstm.*','tasks.id as task_id');
        $query->addSelect('cases.id as case_id','cases.name as case_name','cases.description as case_description','cases.case_number','cases.status as case_status','cases.state as case_state');
        $query->orderBy('tasks_cstm.task_number_c','desc');
        $data = $query->first();
        return $data;
    }

    public function getListDetailGroupCRM($userId){
        $query = DB::table('securitygroups');
        $query->join('securitygroups_users', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->where('securitygroups_users.user_id', $userId);
        $query->where('securitygroups_users.deleted', 0);
        $data = $query->get();
        return $data;
    }

    public function getEmailDetail($module,$moduleId){
        $query = DB::table('email_addresses');
        $query->join('email_addr_bean_rel', 'email_addresses.id', '=', 'email_addr_bean_rel.email_address_id');
        $query->where('email_addr_bean_rel.bean_module', $module);
        $query->where('email_addr_bean_rel.bean_id', $moduleId);
        $data = $query->first();
        return $data;
    }
    /*
    SELECT * FROM notes WHERE parent_type='AOP_Case_Updates' AND parent_id IN (SELECT id FROM aop_case_updates WHERE case_id = '5ce1020f-b092-7c14-d706-5b67ad64ca41')
AND filename IS NOT NULL;

SELECT * FROM notes WHERE parent_type='Cases' AND parent_id = '5ce1020f-b092-7c14-d706-5b67ad64ca41'
AND filename IS NOT NULL;
    */

    /**
     *
     * @param type $parentType
     * @param type $listParentId  array ['aaa','bbb']
     * @return list
     */
    public function listNotesFile($parentType,$listParentId){
        $query = DB::table('notes');
        $query->where('parent_type', $parentType);
        $query->whereIn('parent_id', $listParentId);
        //$query->whereNotNull('filename');
        $data = $query->get();
        return $data;
    }

    public function getDetailLeadCRM($id){
        $query = DB::table('leads');
        $query->join('leads_cases_1_c', 'leads.id', '=', 'leads_cases_1_c.leads_cases_1cases_idb');
        $query->join('leads_cstm', 'leads.id', '=', 'leads_cstm.id_c');
        $query->where('leads_cases_1_c.leads_cases_1leads_ida', $id);
        $data = $query->first();
        return $data;
    }

    public function getValueLookupCRM($type,$value){
        $data = $this->getDetailLookupCRM($type, $value);
        if($data){
            return $data->value_name;
        }
    }

    public function getNameUserCRM($id){
        $data = $this->getDetailUserCRM($id);
        if($data){
            return $data->first_name;
        }
    }

    public function getNameContactCRM($id){
        $data = $this->getDetailContactCRM($id);
        if($data){
            return $data->first_name;
        }
    }

    public function getEmailCRM($module,$moduleId){
        $data = $this->getEmailDetail($module,$moduleId);
        if($data){
            return $data->email_address;
        }
    }

    /**
     *
     * @param type $caseObj
     * @param type $newResolution
     * @param type $typeTask
     * @param string $actionBy
     */
    public static function  updateTaskCaseToResolve($caseObj,$newResolution,$resolutionCategory,$typeTask=null,$actionBy = null ){

        if($actionBy == null){$actionBy = '61795c8d-9bbf-46eb-9187-dc59f49101bf';}  // default (must be not null)

        // update case Completed
        if($typeTask=='4HOUR'){
            DB::table('tasks')
                ->where('id', $caseObj->task_id)
                ->update([
                    'status'                    => 'Completed',
                    'date_start'                => Carbon::now()->subHour(8),
                    'date_due'                  => Carbon::now()->subHour(8)->addHour(4),
                    'task_justification'        => 'technical',
                    'date_modified'     => Carbon::now()->subHour(8),
                    'modified_user_id'  => $actionBy,
                   ]);
            DB::table('tasks_cstm')
                ->where('id_c', $caseObj->task_id)
                ->update([
                    'resolution_c'              => $newResolution,
                    'sla_start_4hr_c'           => Carbon::now()->subHour(8),
                    'sla_stop_4hr_c'            => Carbon::now()->subHour(8)->addMinute(1),
                    'date_execution_time_c'     => Carbon::now()->subHour(8)->addMinute(1),
                    'sla_task_flag_c'           => '3',
                    'acknowledge_time_c'        => Carbon::now()->subHour(8),
                    'acknowledge_by_userid_c'   => $actionBy,
                    'category_factor_c'         => 'external_factor',
                    'resolution_category_c'     => $resolutionCategory
                   ]);
        }else{
            //This refer Initial Task
            DB::table('tasks')
                ->where('id', $caseObj->task_id)
                ->update([
                    'status'            => 'Completed',
                    'date_modified'     => Carbon::now()->subHour(8),
                    'modified_user_id'  => $actionBy,
                   ]);
            DB::table('tasks_cstm')
                ->where('id_c', $caseObj->task_id)
                ->update([
                    'resolution_c'  => $newResolution,
                    'resolution_category_c'     => $resolutionCategory
                   ]);
        }

        $newTask = new Tasks;
        $newTask->id =Uuid::uuid4()->toString();
        $newTask->name = 'Assigned to Case Owner';
        $newTask->description = $caseObj->case_description;
        $newTask->date_entered = Carbon::now()->subHour(8);
        $newTask->date_modified = Carbon::now()->subHour(8);
        $newTask->modified_user_id = $actionBy;
        $newTask->created_by = $actionBy;
        $newTask->deleted = 0;
        $newTask->assigned_user_id = '2732c16c-1fff-9e6c-bb4e-58997edd3340';  //Group Business Coordinator
        $newTask->status = 'Pending Acknowledgement';
        $newTask->date_due_flag = 0;
        $newTask->date_start = Carbon::now()->subHour(8);
        $newTask->date_due = Carbon::now()->subHour(8)->addMinutes(15); // NOW() + 15 minutes
        $newTask->date_start_flag = 0;
        $newTask->parent_type = 'Cases';
        $newTask->parent_id = $caseObj->case_id;
        $newTask->priority = 'Low';
        $newTask->task_justification = 'technical';
        $newTask->save();

        $newTaskCustom = new TaskCustom;
        $newTaskCustom->id_c = $newTask->id;
        $newTaskCustom->assign_group_c = 'Case Owner';
        $newTaskCustom->resolution_c = $newResolution;
        $newTaskCustom->sla_flag_c = 0;
        $newTaskCustom->checkbox_add_day_c = 0;
        $newTaskCustom->category_factor_c = 'external_factor';
        $newTaskCustom->resolution_category_c = $resolutionCategory;
        $newTaskCustom->save();

        $caseModel = Cases::find($caseObj->case_id);
        $caseModel->date_modified =  Carbon::now()->subHour(8);
        $caseModel->modified_user_id = $actionBy;
        $caseModel->status = 'Open_Resolved';
        $caseModel->resolution = $newResolution;
        $caseModel->save();

    }


    public static function  updateTaskToAssignAnotherGroup($caseObj,$newResolution,$resolutionCategory,$userGroupId,$assignGroupName,$taskNameGroup,$typeTask=null,$actionBy = null,$redmineNo = null ){

        if($actionBy == null){$actionBy = '61795c8d-9bbf-46eb-9187-dc59f49101bf';}  // default (must be not null)

        // update case Completed
        if($typeTask=='4HOUR'){
            DB::table('tasks')
                ->where('id', $caseObj->task_id)
                ->update([
                    'status'                    => 'Completed',
                    'date_start'                => Carbon::now()->subHour(8),
                    'date_due'                  => Carbon::now()->subHour(8)->addHour(4),
                    'task_justification'        => 'technical',
                    'date_modified'     => Carbon::now()->subHour(8),
                    'modified_user_id'  => $actionBy,
                   ]);
            DB::table('tasks_cstm')
                ->where('id_c', $caseObj->task_id)
                ->update([
                    'resolution_c'              => $newResolution,
                    'sla_start_4hr_c'           => Carbon::now()->subHour(8),
                    'sla_stop_4hr_c'            => Carbon::now()->subHour(8)->addMinute(1),
                    'date_execution_time_c'     => Carbon::now()->subHour(8)->addMinute(1),
                    'sla_task_flag_c'           => '3',
                    'acknowledge_time_c'        => Carbon::now()->subHour(8),
                    'acknowledge_by_userid_c'   => $actionBy,
                    'category_factor_c'         => 'external_factor',
                    'resolution_category_c'     => $resolutionCategory
                   ]);
        }else{
            //This refer Initial Task
            DB::table('tasks')
                ->where('id', $caseObj->task_id)
                ->update([
                    'status'            => 'Completed',
                    'date_modified'     => Carbon::now()->subHour(8),
                    'modified_user_id'  => $actionBy,
                   ]);
            DB::table('tasks_cstm')
                ->where('id_c', $caseObj->task_id)
                ->update([
                    'resolution_c'  => $newResolution,
                    'resolution_category_c'     => $resolutionCategory
                   ]);
        }

        $newTask = new Tasks;
        $newTask->id =Uuid::uuid4()->toString();
        $newTask->name = $taskNameGroup;
        $newTask->description = $caseObj->case_description;
        $newTask->date_entered = Carbon::now()->subHour(8);
        $newTask->date_modified = Carbon::now()->subHour(8);
        $newTask->modified_user_id = $actionBy;
        $newTask->created_by = $actionBy;
        $newTask->deleted = 0;
        $newTask->assigned_user_id = $userGroupId;   // Group User ID
        $newTask->status = 'Pending Acknowledgement';
        $newTask->date_due_flag = 0;
        $newTask->date_start = Carbon::now()->subHour(8);
        $newTask->date_due = Carbon::now()->subHour(8)->addMinutes(15); // NOW() + 15 minutes
        $newTask->date_start_flag = 0;
        $newTask->parent_type = 'Cases';
        $newTask->parent_id = $caseObj->case_id;
        $newTask->priority = 'Low';
        $newTask->task_justification = 'technical';
        if($redmineNo != null){
            $newTask->case_redmine_number = $redmineNo;
        }
        $newTask->save();

        $newTaskCustom = new TaskCustom;
        $newTaskCustom->id_c = $newTask->id;
        $newTaskCustom->assign_group_c = $assignGroupName;
        $newTaskCustom->resolution_c = $newResolution;
        $newTaskCustom->sla_flag_c = 0;
        $newTaskCustom->checkbox_add_day_c = 0;
        $newTaskCustom->category_factor_c = 'external_factor';
        $newTaskCustom->resolution_category_c = $resolutionCategory;
        $newTaskCustom->save();

        if($redmineNo != null){
            DB::table('cases')
                ->where('id', $caseObj->case_id)
                ->update([
                    'redmine_number' => $redmineNo
                   ]);
        }

    }


    /**
     * Request on email to resolve task Pharmaniaga as Resolve based on list case given.
     */
    public static function  resolveTaskByListCaseNo(){

        $listCaseNo = array(
            '2967556',
            '2963812',
            '2960748',
            '2967552',
            '2955038',
            '2967517',
            '2939563',
            '2956189',
            '2964501',
            '2967147',
            '2968697',
            '2967045',
            '2936768',
            '2961701',
            '2959366',
            '2967226',
            '2962907',
            '2966919',
            '2973248',
            '2952206',
            '2969259',
            '2936831',
            '2958198',
            '2967177',
            '2944690',
            '2970424',
            '2957017',
            '2958805',
            '2958431',
            '2971278',
            '2967019',
            '2964644',
            '2949090',
            '2948237',
            '2955177',
            '2963828',
            '2961951',
            '2973218',
            '2966971',
            '2967537',
            '2958518',
            '2955104',
            '2940763',
            '2969131',
            '2966980',
            '2936823',
            '2957736',
            '2967300',
            '2936776',
            '2966952',
            '2947467',
            '2963649',
            '2967570',
            '2963195',
            '2936899',
            '2973754',
            '2966628',
            '2960830',
            '2965552',
            '2951663',
            '2939197',
            '2970434',
            '2969458',
            '2966376',
            '2957372',
            '2958245',
            '2967563',
            '2960131',
            '2936803',
            '2964431',
            '2967079',
            '2948802',
            '2950117',
            '2963811',
            '2971440',
            '2967337',
            '2965773',
            '2938537',
            '2936692',
            '2956826',
            '2957446',
            '2969563',
            '2967324',
            '2952262',
            '2954142',
            '2950011',
            '2941279',
            '2950187',
            '2961666',
            '2962826',
            '2940133',
            '2966757',
            '2966965',
            '2961885',
            '2971983',
            '2937273',
            '2951716',
            '2958825',
            '2957776',
            '2975022',
            '2950087',
            '2967061',
            '2967568',
            '2958542',
            '2940642',
            '2947508',
            '2956905',
            '2966837',
            '2967250',
            '2947958',
            '2951648',
            '2967545',
            '2945718',
            '2956516',
            '2961788',
            '2963619',
            '2964444',
            '2955387',
            '2938921',
            '2957065',
            '2944613',
            '2935304',
            '2956204',
            '2972993',
            '2966669',
            '2961635',
            '2957641',
            '2967375',
            '2963254',
            '2944092',
            '2958716',
            '2937157',
            '2962577',
            '2950084',
            '2958731',
            '2967361',
            '2960429',
            '2952210',
            '2963668',
            '2959247',
            '2957395',
            '2947144',
            '2961538',
            '2967269',
            '2963618',
            '2952279',
            '2970972',
            '2937214',
            '2957719',
            '2967293',
            '2966990',
            '2955694',
            '2956918',
            '2965874',
            '2966733',
            '2974734',
            '2966662',
            '2952355',
            '2966215',
            '2969543',
            '2960336',
            '2971944',
            '2961098',
            '2936709',
            '2950082',
            '2969508',
            '2966829',
            '2947819',
            '2953543',
            '2936795',
            '2963653',
            '2967491',
            '2957265',
            '2952190',
            '2952347',
            '2961477',
            '2938532',
            '2954706',
            '2967397',
            '2966754',
            '2955025',
            '2966891',
            '2967303',
            '2962499',
            '2959652',
            '2967385',
            '2967110',
            '2942378'
        );
        $newResolution = 'Hasil dari semakan yang dibuat.
Masalah yang dihadapi pada dokumen ini sudah diselesaikan.';
        foreach ($listCaseNo as $caseNo) {
            $crmServ = new CRMService();
            $caseTask = $crmServ->getDetailCaseAndTaskLatestCRM($caseNo);
            if ($caseTask && ( $caseTask->case_status == 'In_Progress' || $caseTask->case_status == 'Open_Assigned')) {

                //RESOLVED CASE
                if (strpos($caseTask->name, 'Initial Task') !== false) {
                    $crmServ->updateTaskCaseToResolve($caseTask, $newResolution, "INITIAL", null);
                    dump('Resolved Initial Task to Case Owner : '.$caseNo);
                } else {
                    $crmServ->updateTaskCaseToResolve($caseTask, $newResolution, '4HOUR', null);
                    dump('Resolved Specialist Task to Case Owner : '.$caseNo);
                }
            } else {
                dump('This case number: '.$caseNo. ' already as status: '.$caseTask->case_status);
            }
        }
    }

    public static $RESOLUTION_CATEGORY = array(
    	'data_fix' => 'Data Fix - User Request',
        'data_fix_system_problem' => 'Data Fix - System Problem',
        'program_fix' => 'Program Fix',
        'data_program_fix' => 'Data Fix & Program Fix',
        'user_familiarity' => 'User Familiarity',
    );

    public function getDashboardCRM()
    {

        $query = DB::connection('mysql_crm')->table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.incident_service_type_c', 'incident_it');
        $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        $query->select('tasks_cstm.task_number_c as taskno', 'cases.case_number as caseno', 'tasks.status as status', 'tasks.name as taskname', 'tasks_cstm.sla_task_flag_c as flag',
            'tasks.date_start as datestart', 'tasks.date_due as datedue', 'tasks_cstm.acknowledge_time_c as acknowledgetime', 'tasks_cstm.task_duration_c as taskduration');
        $query->orderBy('tasks.date_start', 'asc');
        $data = $query->get();
        return $data;
    }

    public function getDashboardCRMSpecialist(){
        $query = DB::connection('mysql_crm')->table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->join('poms_itspec', 'poms_itspec.case_number', '=', 'cases.case_number');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.incident_service_type_c', 'incident_it');
        $query->whereIn('tasks.status', ['Acknowledge','Pending Acknowledgement']);
        $query->where('poms_itspec.itspec_sla_flag', 3);
        $query->select('tasks_cstm.task_number_c as taskno','cases.case_number as caseno','tasks.status as status','tasks.name as taskname','poms_itspec.itspec_sla_flag as flag',
                'tasks.date_start as datestart','tasks.date_due as datedue','tasks_cstm.acknowledge_time_c as acknowledgetime','tasks_cstm.task_duration_c as taskduration');
        $query->orderBy('tasks.date_start','asc');
        $data = $query->get();
        return $data;
    }

    public function getLatestJobQue($name){
        $query = DB::table('job_queue');
        $query->where('name', $name);
        $query->orderBy('execute_time','desc');
        $data = $query->first();
        return $data;
    }

    public function getCsPerformanceSlaCount($contactMode, $date)
    {
        $query = "
        SELECT DISTINCT COUNT(*) as total
        FROM ((cases c
            JOIN cases_cstm cc
              ON ((c.id = cc.id_c)))
           LEFT JOIN cstm_list_app cmode
             ON (((cc.contact_mode_c = cmode.value_code)
                  AND (TRIM(cmode.value_code) <> '')
                  AND (cmode.type_code = 'cdc_contact_mode_list'))))
        WHERE (c.date_entered BETWEEN '$date 00:00:00' AND '$date 23:59:59')
        AND c.sla_flag = 1
        AND cmode.value_name = '$contactMode'";

        return DB::connection('mysql_crm')->select($query)[0]->total;
    }

    public function getCsPerformanceSlaBreachCount($contactMode, $date, $threshold)
    {
        $query = "
        SELECT DISTINCT COUNT(*) as total_breach
        FROM ((cases c
            JOIN cases_cstm cc
              ON ((c.id = cc.id_c)))
           LEFT JOIN cstm_list_app cmode
             ON (((cc.contact_mode_c = cmode.value_code)
                  AND (TRIM(cmode.value_code) <> '')
                  AND (cmode.type_code = 'cdc_contact_mode_list'))))
        WHERE (c.date_entered BETWEEN '$date 00:00:00' AND '$date 23:59:59')
        AND c.sla_flag = 1
        AND cmode.value_name = '$contactMode'
        AND (CASE WHEN(c.cntc_mode_sla_startdate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,c.cntc_mode_sla_startdate,c.cntc_mode_executiondate) END) > $threshold";

        return DB::connection('mysql_crm')->select($query)[0]->total_breach;
    }

    public function getCsPerformanceSlaList($date)
    {
        $query = "SELECT DISTINCT
          c.case_number              AS case_number,
          c.date_entered             AS created_date,
          cc.incident_service_type_c AS type_of_incident,
          c.status                   AS case_status,
          c.sla_flag                 AS cs_sla_flag,
          cmode.value_name           AS contact_mode,
          c.cntc_mode_sla_startdate  AS cs_start_datetime,
          c.cntc_mode_sla_duedate    AS cs_due_datetime,
          c.cntc_mode_sla_startdate  AS cs_actual_start_datetime,
          c.cntc_mode_executiondate  AS cs_completed_datetime,
          (CASE WHEN (c.cntc_mode_sla_startdate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,c.cntc_mode_sla_startdate,c.cntc_mode_sla_duedate) END) AS cs_available_duration,
          (CASE WHEN (c.cntc_mode_sla_startdate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,c.cntc_mode_sla_startdate,c.cntc_mode_executiondate) END) AS cs_actual_duration
        FROM ((cases c
            JOIN cases_cstm cc
              ON ((c.id = cc.id_c)))
           LEFT JOIN cstm_list_app cmode
             ON (((cc.contact_mode_c = cmode.value_code)
                  AND (TRIM(cmode.value_code) <> '')
                  AND (cmode.type_code = 'cdc_contact_mode_list'))))
        WHERE (c.date_entered BETWEEN '$date 00:00:00' AND '$date 23:59:59')
        AND c.sla_flag = 1
        AND cmode.value_name IN ('Open Portal', 'Email', 'Fax', 'Call-in', 'Letter Correspondence')";

        return DB::connection('mysql_crm')->select($query);
    }


}
