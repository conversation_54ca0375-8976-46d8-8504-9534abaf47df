<?php

namespace App\Http\Controllers;

use App\Http\Resources\InventoryReportResource;
use App\InventoryReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use DB;

class DownloadInventoryReport extends Controller
{  
    public function getCategoryProducts()
    {
        $queryGetListCategory ="SELECT id AS catID, `eula_text` AS catDesc
        FROM `categories` 
        WHERE deleted_at IS NULL 
        AND user_id IS NOT NULL";

        $data = DB::connection('mysql_inventory')->select($queryGetListCategory);

        return response()->json($data);
    }

    public function getProductModels(Request $request)
    {
        $result = $request['catID'];
        $queryGetListProduct ="SELECT NAME as productName
        FROM models
        WHERE `category_id` = $result
        AND `deleted_at` IS NULL
        AND user_id IS NOT NULL
        GROUP BY NAME";

        $data = DB::connection('mysql_inventory')->select($queryGetListProduct);

        return response()->json($data);
    }

    public function downloadExcel(Request $request) {
        
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', 300); //3 minutes
      //  dd($request['catID']);
        $catID = $request['catID'];
        $productName = $request['productName'];
        $fileName = 'ReportInventory';
        $list = self::getQueryInventory($catID,$productName);
//dd($list);
        Excel::download($fileName, function($excel)use($list) {
            $excel->setTitle('List of Top 10 Incident Service');

            $excel->sheet('TopIncServ', function($sheet) use ($list) {
                $sheet->setOrientation('landscape');

                $sheet->setStyle(array(
                    'font' => array(
                        'name' => 'Calibri',
                        'size' => 11,
                        'bold' => false
                    )
                ));

                //http://www.maatwebsite.nl/laravel-excel/docs/reference-guide#formatting
                $sheet->setColumnFormat(array(
                    'A' => '@',
                    'B' => '@',
                    'C' => '@',
                    'D' => '@',
                    'E' => '@',
                    'F' => '@',
                ));

                $sheet->row(1, array(
                    'ASSET TAG', 'PRODUCT NAME', 'MODEL NUMBER', 'MANUFACTURER', 'LOCATION'
                ));    
                $sheet->row(1, function($row) {
                    // call cell manipulation methods
                    $row->setBackground('#684E49');
                });
                $sheet->setAutoSize(true);

                $sheet->cells('A1:F1', function($cells) {
                    // manipulate the range of cells
                    // Set with font color
                    $cells->setFontColor('#fff');

                    // Set font family
                    $cells->setFontFamily('Calibri');

                    // Set font size
                    $cells->setFontSize(11);

                    // Set font weight to bold
                    $cells->setFontWeight('bold');

                    // Set all borders (top, right, bottom, left)
                    $cells->setBorder('solid', 'solid', 'solid', 'solid');
                });

                $count = 2;

                foreach ($list as $obj) {
                                        
                    $sheet->row($count, array(
                            $obj->AssetTag,
                            $obj->productDesc,
                            $obj->modelNumber,
                            $obj->ManufatureName,
                            $obj->childLocation,
                        )
                    );
                    $count++;
                }
            });         

        })->export('xlsx', storage_path('app/Report'));

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $fullPath = storage_path('app/Report/'.$fileName.'.xlsx');
        return response()->download($fullPath, $fileName.'.xlsx', $headers);
      
   }

   protected static function getQueryInventory($catID,$productName) {

        $queryGetListInv ="SELECT a.`asset_tag` AS AssetTag,b.name AS productDesc, c.`name` AS ManufatureName,f.`name` AS childLocation , b.model_number as modelNumber,e.`name` AS categoryName,e.`id` as cat_id,b.`id` as model_id,
        e.`eula_text` AS detailsCategoryname, a.`serial` AS serialNum, a.`created_at` AS dateCreated
       , g.`name` AS parentLocation,d.`name` AS companyName,location_id as locID,
       _snipeit_ip_30 as IP,_snipeit_hostname_39 as hostName,_snipeit_location_40 as Location
       FROM assets a
       LEFT JOIN `models` b ON a.`model_id`=b.`id`
       LEFT JOIN `manufacturers` c ON c.`id`=b.`manufacturer_id`
       LEFT JOIN `companies` d ON d.`id`=a.`company_id`
       LEFT JOIN `categories` e ON e.`id`=b.`category_id`
       LEFT JOIN locations f ON f.`id`= a.`location_id`
       LEFT JOIN `locations` g ON g.`id`=f.`parent_id`
       WHERE a.`deleted_at` IS NULL
       and e.`id` = $catID 
       AND b.`name` LIKE '$productName'";

        $data = DB::connection('mysql_inventory')->select($queryGetListInv);

        return $data;  
}

}
