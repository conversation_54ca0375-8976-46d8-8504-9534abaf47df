<?php

namespace App\crm\sla;

use Carbon\Carbon;
use DateInterval;
use DatePeriod;
use DateTime;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class InsertTalkdeskData
{

    public static function runInsertTalkdeskData($queryDate)
    {

        Log::debug(self::class . ' Starting... Get Talkdesk Data ', ['Query Date' => $queryDate]);
        if ($queryDate === null) {
            dump('Process by specified date...');
            $begin = new DateTime('2023-08-05 00:00:00');
            $end = new DateTime('2023-08-06 00:00:00');

            $interval = DateInterval::createFromDateString('1 day');
            $period = new DatePeriod($begin, $interval, $end);

            foreach ($period as $dt) {
                self::getTalkdeskData($dt);
            }
        } else {
            dump('Process by scheduler...');
            self::getTalkdeskData($queryDate);
        }
    }

    public static function getAccessToken()
    {
        $start = microtime(true);
        Log::info(self::class . '::getAccessToken job started at: ' . now());

        $client_id = '60e33d2057d74ed6be91ed9c94e44392';
        $client_secret = 'NF1EX1ZaHLpHU-mfKJ-nOpql_V5cPyO7NMpNDLcQdoLbdDeiGj2R2f2EqetnNWyDCLTc75bBErVYQIi0H6loSg';
        $access_token_url = 'https://casb-my.talkdeskid.com/oauth/token';

        $client = new Client();
        $response = $client->request('POST', $access_token_url, [
            'headers' => [
                'Authorization' => 'Basic ' . base64_encode($client_id . ':' . $client_secret),
                'accept' => 'application/json',
                'content-type' => 'application/x-www-form-urlencoded',
            ],
            'form_params' => [
                'grant_type' => 'client_credentials'
            ]
        ]);

        Log::info(self::class . '::getAccessToken job ended at: ' . now());
        Log::info(self::class . '::getAccessToken execution time: ' . round(microtime(true) - $start, 2) . ' seconds');

        return json_decode($response->getBody()->getContents())->access_token;
    }

    public static function getTalkdeskData($date)
    {
        $start = microtime(true);
        Log::info(self::class . '::getTalkdeskData job started at: ' . now());

        $api_url = 'https://api.talkdeskapp.com/data/reports/calls/jobs';

        $access_token = self::getAccessToken();

        $client = new Client();
        $response = $client->post($api_url, [
            'headers' => [
                'Authorization' => "Bearer $access_token",
            ],
            'json' => [
                'name' => 'poms_sla_' . Carbon::now()->format('Ymd'),
                'timezone' => 'Asia/Singapore',
                'format' => 'json',
                'timespan' => [
                    'from' => Carbon::parse($date)->startOfDay()->toIso8601ZuluString(),
                    'to' => Carbon::parse($date)->addDay()->startOfDay()->toIso8601ZuluString(),
                ],
            ],
        ]);

        $data = json_decode($response->getBody()->getContents());

        sleep(10);

        self::getFileData($data);

        dump('Completed!');

        Log::info(self::class . '::getTalkdeskData job ended at: ' . now());
        Log::info(self::class . '::getTalkdeskData execution time: ' . round(microtime(true) - $start, 2) . ' seconds');
    }

    public static function insertTalkdeskRowData($fileContentData)
    {

        $start = microtime(true);
        Log::info(self::class . '::insertTalkdeskRowData job started at: ' . now());

        try {
            foreach ($fileContentData as $row) {
                $callId = isset($row["call_id"]) ? (string)$row["call_id"] : null;
                if ($callId && !self::isCallIdExist($callId)) {
                    DB::connection('mysql')->table('talkdesk_call_data')->insert([
                        "call_id" => $callId,
                        "callsid" => isset($row["callsid"]) ? (string)$row["callsid"] : null,
                        "type" => isset($row["type"]) ? (string)$row["type"] : null,
                        "start_at" => isset($row["start_at"]) ? date("Y-m-d H:i:s", strtotime($row["start_at"])) : null,
                        "end_at" => isset($row["end_at"]) ? date("Y-m-d H:i:s", strtotime($row["end_at"])) : null,
                        "talkdesk_phone_number" => isset($row["talkdesk_phone_number"]) ? (string)$row["talkdesk_phone_number"] : null,
                        "talkdesk_phone_display_name" => isset($row["talkdesk_phone_display_name"]) ? (string)$row["talkdesk_phone_display_name"] : null,
                        "contact_phone_number" => isset($row["contact_phone_number"]) ? (string)$row["contact_phone_number"] : null,
                        "user_id" => isset($row["user_id"]) ? (string)$row["user_id"] : null,
                        "user_name" => isset($row["user_name"]) ? (string)$row["user_name"] : null,
                        "user_email" => isset($row["user_email"]) ? (string)$row["user_email"] : null,
                        "total_time" => isset($row["total_time"]) ? (int)$row["total_time"] : null,
                        "talk_time" => isset($row["talk_time"]) ? (int)$row["talk_time"] : null,
                        "wait_time" => isset($row["wait_time"]) ? (int)$row["wait_time"] : null,
                        "hold_time" => isset($row["hold_time"]) ? (int)$row["hold_time"] : null,
                        "abandon_time" => isset($row["abandon_time"]) ? (int)$row["abandon_time"] : null,
                        "total_ringing_time" => isset($row["total_ringing_time"]) ? (int)$row["total_ringing_time"] : null,
                        "disposition_code" => isset($row["disposition_code"]) ? (int)$row["disposition_code"] : null,
                        "notes" => isset($row["notes"]) ? (string)$row["notes"] : null,
                        "user_voice_rating" => isset($row["user_voice_rating"]) ? (int)$row["user_voice_rating"] : null,
                        "ring_groups" => isset($row["ring_groups"]) ? (string)($row["ring_groups"]) : null,
                        "ivr_options" => isset($row["ivr_options"]) ? (string)($row["ivr_options"]) : null,
                        "is_in_business_hours" => isset($row["is_in_business_hours"]) ? (bool)$row["is_in_business_hours"] : null,
                        "is_callback_from_queue" => isset($row["is_callback_from_queue"]) ? (bool)$row["is_callback_from_queue"] : null,
                        "is_transfer" => isset($row["is_transfer"]) ? (bool)$row["is_transfer"] : null,
                        "handling_user_id" => isset($row["handling_user_id"]) ? (int)$row["handling_user_id"] : null,
                        "handling_user_name" => isset($row["handling_user_name"]) ? (string)$row["handling_user_name"] : null,
                        "handling_user_email" => isset($row["handling_user_email"]) ? (string)$row["handling_user_email"] : null,
                        "recording_url" => isset($row["recording_url"]) ? (string)$row["recording_url"] : null,
                        "is_external_transfer" => isset($row["is_external_transfer"]) ? (bool)$row["is_external_transfer"] : null,
                        "is_if_no_answer" => isset($row["is_if_no_answer"]) ? (bool)$row["is_if_no_answer"] : null,
                        "is_call_forwarding" => isset($row["is_call_forwarding"]) ? (bool)$row["is_call_forwarding"] : null,
                        "csat_score" => isset($row["csat_score"]) ? (int)$row["csat_score"] : null,
                        "csat_survey_time" => isset($row["csat_survey_time"]) ? (string) $row["csat_survey_time"] : null,
                        "team_id" => isset($row["team_id"]) ? (int) $row["team_id"] : null,
                        "team_name" => isset($row["team_name"]) ? (string) $row["team_name"] : null,
                        "rating_reason" => isset($row["rating_reason"]) ? (string) $row["rating_reason"] : null,
                        "agent_disconnected" => isset($row["agent_disconnected"]) ? (bool)$row["agent_disconnected"] : null,
                        "data_status" => isset($row["data_status"]) ? (string)$row["data_status"] : null,
                    ]);
                } else {
                    Log::info(self::class . '::insertTalkdeskRowData - Skipped insertion for call_id: ' . $callId . '. Already exist!');
                }
            }
        } catch (\Exception $e) {
            Log::error(self::class . '::insertTalkdeskRowData encountered an error: ' . $e->getMessage());
        }

        Log::info(self::class . '::insertTalkdeskRowData job ended at: ' . now());
        Log::info(self::class . '::insertTalkdeskRowData execution time: ' . round(microtime(true) - $start, 2) . ' seconds');
    }

    public static function getFileData($data)
    {
        $start = microtime(true);
        Log::info(self::class . '::getFileData job started at: ' . now());

        $access_token = self::getAccessToken();

        if (is_object($data) && isset($data->_links->files->href)) {
            $fileHref = $data->_links->files->href;

            $client = new Client();
            $response = $client->get($fileHref, [
                'headers' => [
                    'Authorization' => "Bearer $access_token",
                ],
            ]);

            $fileContent = json_decode($response->getBody()->getContents(), true);

            if (isset($fileContent['entries']) && is_array($fileContent['entries'])) {
                $entryCount = count($fileContent['entries']);
                dump('Total call entries: ' . $entryCount);
                Log::info(self::class . '::getFileData - Number of call entries in the file: ' . $entryCount);
                self::insertTalkdeskRowData($fileContent['entries']);
            } else {
                Log::warning(self::class . '::getFileData - No valid entries found in the file content.');
            }

            Log::info(self::class . '::getFileData job ended at: ' . now());
            Log::info(self::class . '::getFileData execution time: ' . round(microtime(true) - $start, 2) . ' seconds');
        }
    }

    public static function isCallIdExist($callId)
    {
        return DB::connection('mysql')->table('talkdesk_call_data')->where('call_id', $callId)->exists();
    }
}
