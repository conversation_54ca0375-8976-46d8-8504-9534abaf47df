<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="67e4bd23-76dd-4ec2-ade6-fc5e52d6342a" name="Default Changelist" comment="">
      <change afterPath="$PROJECT_DIR$/resources/js/src/views/datalist/DataViewSidebar.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Http/Middleware/CheckIsAdmin.php" beforeDir="false" afterPath="$PROJECT_DIR$/app/Http/Middleware/CheckIsAdmin.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/public/css/material-icons/MaterialIcons-Regular.svg" beforeDir="false" afterPath="$PROJECT_DIR$/public/css/material-icons/MaterialIcons-Regular.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/public/css/material-icons/codepoints" beforeDir="false" afterPath="$PROJECT_DIR$/public/css/material-icons/codepoints" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/public/css/material-icons/codepoints.json" beforeDir="false" afterPath="$PROJECT_DIR$/public/css/material-icons/codepoints.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/public/css/material-icons/codepoints.scss" beforeDir="false" afterPath="$PROJECT_DIR$/public/css/material-icons/codepoints.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/public/css/material-icons/material-icons.css" beforeDir="false" afterPath="$PROJECT_DIR$/public/css/material-icons/material-icons.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/public/css/material-icons/material-icons.scss" beforeDir="false" afterPath="$PROJECT_DIR$/public/css/material-icons/material-icons.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/public/css/material-icons/mixins.scss" beforeDir="false" afterPath="$PROJECT_DIR$/public/css/material-icons/mixins.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/public/css/material-icons/variables.scss" beforeDir="false" afterPath="$PROJECT_DIR$/public/css/material-icons/variables.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/public/css/prism-tomorrow.css" beforeDir="false" afterPath="$PROJECT_DIR$/public/css/prism-tomorrow.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/public/mix-manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/public/mix-manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/js/src/main.js" beforeDir="false" afterPath="$PROJECT_DIR$/resources/js/src/main.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/js/src/router.js" beforeDir="false" afterPath="$PROJECT_DIR$/resources/js/src/router.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/js/src/views/Home.vue" beforeDir="false" afterPath="$PROJECT_DIR$/resources/js/src/views/Home.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/js/src/views/datalist/AddNewDataSidebar.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/js/src/views/datalist/UserList.vue" beforeDir="false" afterPath="$PROJECT_DIR$/resources/js/src/views/datalist/UserList.vue" afterDir="false" />
    </list>
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings" doNotAsk="true">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution>
      <executable />
    </execution>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="PhpWorkspaceProjectConfiguration">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/vendor/namshi/jose" />
      <path value="$PROJECT_DIR$/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/vendor/dnoegel/php-xdg-base-dir" />
      <path value="$PROJECT_DIR$/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/vendor/laravel/tinker" />
      <path value="$PROJECT_DIR$/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/vendor/phpspec/prophecy" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-token-stream" />
      <path value="$PROJECT_DIR$/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/vendor/symfony/browser-kit" />
      <path value="$PROJECT_DIR$/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/vendor/symfony/debug" />
      <path value="$PROJECT_DIR$/vendor/symfony/dom-crawler" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-iconv" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php56" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php73" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-util" />
      <path value="$PROJECT_DIR$/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/vendor/doctrine/instantiator" />
      <path value="$PROJECT_DIR$/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/vendor/lcobucci/jwt" />
      <path value="$PROJECT_DIR$/vendor/fideloper/proxy" />
      <path value="$PROJECT_DIR$/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/vendor/sebastian/resource-operations" />
      <path value="$PROJECT_DIR$/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/vendor/beyondcode/laravel-dump-server" />
      <path value="$PROJECT_DIR$/vendor/fzaninotto/faker" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/nunomaduro/collision" />
      <path value="$PROJECT_DIR$/vendor/swiftmailer/swiftmailer" />
      <path value="$PROJECT_DIR$/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/vendor/jakub-onderka/php-console-color" />
      <path value="$PROJECT_DIR$/vendor/jakub-onderka/php-console-highlighter" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/vendor/opis/closure" />
      <path value="$PROJECT_DIR$/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/vendor/tymon/jwt-auth" />
      <path value="$PROJECT_DIR$/vendor/yajra/laravel-oci8" />
      <path value="$PROJECT_DIR$/vendor/yajra/laravel-pdo-via-oci8" />
      <path value="$PROJECT_DIR$/vendor/erusev/parsedown" />
      <path value="$PROJECT_DIR$/vendor/fabpot/goutte" />
      <path value="$PROJECT_DIR$/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/vendor/symfony/console" />
    </include_path>
  </component>
  <component name="ProjectId" id="1T5YE9uEX7HJcBxDaoIZgRV7Q2l" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/resources/js/src/views/datalist" />
    <property name="nodejs_package_manager_path" value="npm" />
    <property name="settings.editor.selected.configurable" value="preferences.pluginManager" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Workspace\poms\resources\js\src\views\datalist" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="67e4bd23-76dd-4ec2-ade6-fc5e52d6342a" name="Default Changelist" comment="" />
      <created>1572751309182</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1572751309182</updated>
      <workItem from="1572751311362" duration="464000" />
      <workItem from="1572751812923" duration="1270000" />
      <workItem from="1573318754194" duration="39000" />
      <workItem from="1575468742566" duration="4514000" />
      <workItem from="1576276097424" duration="3512000" />
      <workItem from="1576366242632" duration="2039000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="COLUMN_ORDER" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>