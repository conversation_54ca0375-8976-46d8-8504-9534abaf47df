<template>
    <vs-list>

        <vs-list-header icon-pack="feather" class="text-color-base" icon="icon-pie-chart" title="Dashboard Setting"></vs-list-header>
        <router-link to="/list-param-sla">
            <vs-list-item icon-pack="feather" icon="icon-grid" title="SLA Dashboard" subtitle="Set SLA dashboard threshold and parameters"></vs-list-item>
        </router-link>
        <router-link to="#">
            <vs-list-item icon-pack="feather" icon="icon-grid" title="Technical Dashboard" subtitle="Set technical dashboard threshold and parameters"></vs-list-item>
        </router-link>

        <vs-list-header icon-pack="feather" class="text-color-base" icon="icon-users" title="Access Control"></vs-list-header>
        <router-link to="/list-users">
            <vs-list-item icon-pack="feather" icon="icon-user" title="User Management" subtitle="Create, update or delete user"></vs-list-item>
        </router-link>
        <!-- <router-link to="#">
            <vs-list-item icon-pack="feather" icon="icon-unlock" title="User Access Control" subtitle="Manage user access control"></vs-list-item>
        </router-link> -->

    </vs-list>
</template>

<script>
    export default {
        data() {
            return {
                value1: '',
                url: '/sla-dashboard'
            }
        }
    }
</script>
