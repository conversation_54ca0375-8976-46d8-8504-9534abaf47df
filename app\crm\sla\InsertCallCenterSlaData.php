<?php

/*
 * Function : Insert data from table in db CCMData into table sla_mitel in db poms
 * Date : 8 Oct 2019
 * Author : Nur Asmaa
 */

namespace App\crm\sla;

use App\Services\PomsService;
use App\Services\Traits\MitelService;
use Carbon\Carbon;
use DateTime;
use Log;
use DB;
use Ramsey\Uuid\Uuid;
use App\Nagios\MigrateUtils;
use Config;
use DateInterval;
use DatePeriod;

class InsertCallCenterSlaData
{

    public static function pomsService()
    {
        return new PomsService();
    }

    public static function runInsertMitelSlaData($dateStart = null, $dateEnd = null)
    {

        Log::debug(self::class . ' Starting ... Checking SLA Mitel Data ', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);

        self::insertMitelSlaData($dateStart, $dateEnd);
    }

    public static function runInsertAspectSlaData($queryDate)
    {

        Log::debug(self::class . ' Starting ... Checking SLA Aspect Data ', ['Query Date' => $queryDate]);

        // insert specific date
        if ($queryDate === null) {
            dump('Process by specified date...');
            $begin = new DateTime('2020-01-25 00:00:00');
            $end = new DateTime('2020-01-31 00:00:00');

            $interval = DateInterval::createFromDateString('1 day');
            $period = new DatePeriod($begin, $interval, $end);

            foreach ($period as $dt) {
                self::insertAspectSlaData($dt);
            }
        } else {
            dump('Process by scheduler...');
            self::insertAspectSlaData($queryDate);
        }

        
        
    }

    /* Read data from Database CCMData copy into database cdc_poms */

    private static function insertMitelSlaData($dateStart, $dateEnd)
    {

        Log::info(self::class . ' Start : ' . __FUNCTION__ . '     ->> Date Start: ' . $dateStart . ', Date End: ' . $dateEnd);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);
        $dtStartTime = Carbon::now();

        $queryStartDate = Carbon::parse($dateStart)->format('Y-m-d');
        $queryEndDate = Carbon::parse($dateEnd)->format('Y-m-d');

        try {

            $results = self::pomsService()->getTotalAnswer($queryStartDate, $queryEndDate);

            if (is_array($results) || is_object($results)) {
                dump('Result count: ' . count($results));
                $counter = 0;
                foreach ($results as $data) {
                    $rowDate = $data->day_per_month;
                    $existDate = DB::connection('mysql')->table('sla_mitel')
                        ->where('date_call', $rowDate)
                        ->count();

                    if ($existDate == 0) {
                        $insertDataSet = array(
                            [
                                'date_call' => $rowDate,
                                'acd_call_offer' => intval($data->call_offered),
                                'acd_call_handle' => intval($data->call_handled),
                                'call_abandon_short' => intval($data->call_abandoned_short),
                                'call_abandon_long' => intval($data->call_abandoned_long),
                                'abandon_percentage' => round($data->abandon_perc, 1),
                                'answer_percentage' => round($data->answer_perc, 1),
                                'service_level' => round($data->service_level, 1),
                                'mitel_insert_data_datetime' => Carbon::now()
                            ]
                        );

                        DB::connection('mysql')->table('sla_mitel')->insert($insertDataSet);

                        dump('Success insert data for date: ' . $data->day_per_month);

                        $counter++;
                        if ($counter == 10) {
                            sleep(1);
                            $counter = 0;
                        }
                    }
                }

                $total = count($results);
                $logsdata = self::class . ' Successfully insert data for SLA Mitel => Date Start : ' . $dtStartTime . ' -> '
                    . 'Query Date End : ' . Carbon::now() . ' Total Data : ' . $total . ' , Completed --- Taken Time : ' .
                    json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
                Log::info($logsdata);
                dump($logsdata);
            }
            return $results;
        } catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }
        return null;
    }

    /* Read data from Database Aspect copy into database cdc_poms */
    private static function insertAspectSlaData($queryDate)
    {

        $queryDate = Carbon::parse($queryDate);

        Log::info(self::class . ' Start : ' . __FUNCTION__ . '     ->> Date Query: ' . $queryDate);
        var_dump('Date Start: ' . $queryDate);
        $dtStartTime = Carbon::now();

        try {
            $rowDate = $queryDate->format('Y-m-d');
            $existDate = DB::connection('mysql')->table('sla_mitel')
                ->where('date_call', $rowDate)
                ->count();

            if ($existDate == 0) {
                $data = self::pomsService()->getNewAspectSlaData($queryDate)[0];
                dump($data);
                // $abandonPercentage = ($data->call_long / $data->call_offer) * 100;
                // $answerPercentage = ($data->call_handle / $data->call_offer) * 100;
                $insertDataSet = array(
                    [
                        'date_call' => $rowDate,
                        'acd_call_offer' => intval($data->acd_call_offer),
                        'acd_call_handle' => intval($data->acd_call_handle),
                        'call_WI_lvl' => intval($data->call_WI_lvl),
                        'call_abandon_short' => intval($data->call_abandon_short),
                        'call_abandon_long' => intval($data->call_abandon_long),
                        'abandon_percentage' => round($data->abandon_percentage, 2),
                        'answer_percentage' => round($data->answer_percentage, 2),
                        'service_level' => round($data->service_level, 2),
                        'mitel_insert_data_datetime' => Carbon::now()
                    ]
                );

                DB::connection('mysql')->table('sla_mitel')->insert($insertDataSet);

                dump('Success insert data for date: ' . $queryDate);
            } else {
                var_dump('Record exist. Skipped!');
            }

            $logsdata = self::class . ' Successfully insert data for SLA Aspect => Process Start : ' . $dtStartTime . ' -> '
                . 'Process End : ' . Carbon::now() . ', Completed --- Taken Time : ' .
                json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            Log::info($logsdata);
            dump($logsdata);
        } catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }
        return null;
    }

}
