<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\SlaReport;
use App\Mail\SlaReportMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Report\SlaReportGenerator;
use RuntimeException;

class ScheduleMonthlySlaReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sla:generate-monthly-report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate and email SLA report on the 1st of each month at 6:00 AM';

    /**
     * The SlaReportGenerator instance.
     *
     * @var SlaReportGenerator
     */
    protected $reportGenerator;

    /**
     * Create a new command instance.
     *
     * @param SlaReportGenerator $reportGenerator
     * @return void
     */
    public function __construct(SlaReportGenerator $reportGenerator)
    {
        parent::__construct();
        $this->reportGenerator = $reportGenerator;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('Starting monthly SLA report generation at ' . Carbon::now());

        try {
            // Get the previous month's data
            $now = Carbon::now();
            $reportDate = $now->copy()->subMonth();
            
            $year = $reportDate->year;
            $month = $reportDate->month;
            $monthName = $reportDate->monthName;

            // Create a new report record
            $report = new SlaReport();
            $report->name = 'SLA Report ' . $monthName . ' ' . $year;
            $report->parameter = json_encode([
                'month' => $month,
                'year' => $year
            ]);
            $report->status = 'queue';
            $report->deleted = 0;
            $report->hidden = 1;
            $report->created_by = 'Administrator';
            $report->save();

            Log::info("Scheduled monthly SLA report for {$monthName} {$year} has been queued. Report ID: {$report->id}");
            
            // Process the queue immediately
            $this->reportGenerator->runCheckReportQueue();
            
            // Refresh the report to get the latest status
            $report->refresh();
            
            if ($report->status === 'completed') {
                // Send email notification
                Mail::to('<EMAIL>')
                    // ->cc(['<EMAIL>', '<EMAIL>'])
                    ->queue(new SlaReportMail($report, $year, $month));
                
                Log::info('Monthly SLA report generated and email sent successfully.');
            } else {
                $errorMessage = 'Failed to generate SLA report. Status: ' . $report->status;
                Log::error($errorMessage);
                throw new \RuntimeException($errorMessage);
            }
            
            Log::info('Monthly SLA report generation completed at ' . Carbon::now());
            
        } catch (\Exception $e) {
            Log::error('Error generating monthly SLA report: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            $this->error('Error generating monthly SLA report: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
