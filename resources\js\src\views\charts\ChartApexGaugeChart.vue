<template>
    <vx-card title="Bar Chart" class="mb-base">

        <div class="mt-5">
            <vue-apex-charts type="radialBar" height="350" :options="radialBarChart.chartOptions"
                             :series="radialBarChart.series"></vue-apex-charts>
        </div>
    </vx-card>
</template>

<script>
    import VueApexCharts from 'vue-apexcharts'

    const themeColors = ['#7367F0'];

    export default {
        data() {
            return {
                radialBarChart: {
                    series: [25],
                    chartOptions: {
                        colors: themeColors,
                        plotOptions: {
                            radialBar: {
                                startAngle: -90,
                                endAngle: 90,
                                track: {
                                    background: '#333',
                                    startAngle: -90,
                                    endAngle: 90,
                                },
                                dataLabels: {
                                    name: {
                                        fontSize: '18px',
                                    },
                                    value: {
                                        fontSize: '16px',
                                    },
                                }
                            }
                        },
                        labels: ['System'],
                    }
                }
            }
        },
        components: {
            VueApexCharts
        }
    }
</script>