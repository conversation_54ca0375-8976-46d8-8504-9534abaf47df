<template>
  <div class>
    <vs-row vs-justify="left">
      <vs-col v-if="isRole('2')" type="flex" vs-justify="center" vs-align="center" vs-w="3" class="pr-8">
        <router-link to="/sla-dashboard">
          <vs-card to="/sla-dashboard" actionable class="text-center cardx" fixedHeight>
            <feather-icon
              icon="TargetIcon"
              class="p-6 mt-4 mb-8 bg-primary inline-flex rounded-full text-white shadow"
              svgClasses="h-8 w-8"
            ></feather-icon>
            <h3 class="mb-6">SLA Dashboard</h3>
            <p class="xl:w-3/4 lg:w-4/5 md:w-2/3 w-4/5 mx-auto">Service-Level Agreement Dashboard</p>
          </vs-card>
        </router-link>
      </vs-col>
      <vs-col v-if="isRole('4')" type="flex" vs-justify="center" vs-align="center" vs-w="3" class="pr-8">
        <router-link to="/ep-process-dashboard">
          <vs-card actionable class="text-center cardx" fixedHeight>
            <feather-icon
              icon="CpuIcon"
              class="p-6 mt-4 mb-8 bg-primary inline-flex rounded-full text-white shadow"
              svgClasses="h-8 w-8"
            ></feather-icon>
            <h3 class="mb-6">KPI / eP Process</h3>
            <p class="xl:w-3/4 lg:w-4/5 md:w-2/3 w-4/5 mx-auto">KPI / eP Process Dashboard</p>
          </vs-card>
        </router-link>
      </vs-col>
    </vs-row>
  </div>
</template>

<script>
export default {
  methods: {
    isRole(role) {
      // console.log(role);
      return this.containsKey(this.$auth.user().role, role)
    },

    containsKey(obj, key) {
      return obj.includes(key)
    },
  }
};
</script>

