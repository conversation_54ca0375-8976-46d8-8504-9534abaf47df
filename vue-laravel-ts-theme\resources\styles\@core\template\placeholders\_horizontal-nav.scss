@use "@core-scss/base/mixins";

// Horizontal nav item icon (Including sub nav items)
%horizontal-nav-item-icon {
  font-size: 1.375rem;
}

// Horizontal nav item styles (including nested)
%horizontal-nav-item {
  padding-block: 0.5rem;
  padding-inline: 1rem;
}

%nav-group-label-and-nav-link-style {
  border-radius: 0.375rem;
  margin-block: 0.125rem;
  margin-inline: 0.5rem;
}

// Active styles for sub nav link
%horizontal-nav-sub-nav-link-active {
  background: rgba(var(--v-theme-primary), 0.08);

  // Remove before pseudo element from sub nav link to avoid overlapping with active state
  &::before {
    content: none;
  }
}

// Popper content styles
%horizontal-nav-popper-content {
  @include mixins.elevation(8);
}

// Top level horizontal nav item styles (`a` tag & group label)
%horizontal-nav-top-level-item {
  border-radius: 0.375rem;
}

// Horizontal nav item title
%horizontal-nav-item-title {
  line-height: 1.375rem;
}

%third-level-nav-item-icon {
  margin-inline: 0 0.5rem;
}
