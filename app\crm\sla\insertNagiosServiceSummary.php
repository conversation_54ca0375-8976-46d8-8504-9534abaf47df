<?php


namespace App\crm\sla;

use App\Nagios\MigrateUtils;
use App\Services\PomsService;
use App\SlaNagios;
use Carbon\Carbon;
use DateInterval;
use DatePeriod;
use DateTime;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class insertNagiosServiceSummary
{

    public static function runFetchNagiosServiceByPeriod()
    {
        $fetchDate = Carbon::now();

        $begin = new DateTime('2019-09-19 06:00:00');
        $end = new DateTime('2019-09-30');

        $logsStart = self::class . ' Starting ... Fetch and insert data from nagios api into sla_nagios from date '
            . $begin->format('Y-m-d H:i:s') . ' to date ' . $end->format('Y-m-d H:i:s') . ' Fetch Datetime: ' . $fetchDate;
        Log::debug($logsStart);
        dump($logsStart);

        $interval = DateInterval::createFromDateString('10 minute');
        $period = new DatePeriod($begin, $interval, $end);

        foreach ($period as $dt) {
            self::runInsertSlaNagiosProcess($dt);
        }

        $logsEnd = self::class . ' Process completed ' . $fetchDate . ' -> '
            . 'Fetch End: ' . Carbon::now() . ', Completed --- Taken Time: ' .
            json_encode(['Time' => MigrateUtils::getTakenTime($fetchDate)]);
        Log::info($logsEnd);
        dump($logsEnd);
    }

    public static function runFetchNagiosServiceCurrent()
    {
        $fetchDate = Carbon::now();

        $logsStart = self::class . ' Starting ... Fetch and insert data from nagios api into sla_nagios. Fetch Datetime: ' . $fetchDate;
        Log::debug($logsStart);
        dump($logsStart);

        self::runInsertSlaNagiosProcess(Carbon::now());
        
        // check eperolehan sso web status
        self::checkEperolehanWebAccessibility($fetchDate);

        $logsEnd = self::class . ' Process completed ' . $fetchDate . ' -> '
            . 'Fetch End: ' . Carbon::now() . ', Completed --- Taken Time: ' .
            json_encode(['Time' => MigrateUtils::getTakenTime($fetchDate)]);
        Log::info($logsEnd);
        dump($logsEnd);
    }

    private static function runInsertSlaNagiosProcess($dt)
    {
        echo 'Start inserting data for date: ' . $dt->format("l, Y-m-d H:i:s\n");

        self::getServiceList('portal', PomsService::$NAGIOS_HOST_PORTAL, $dt);
        self::getServiceList('web', PomsService::$NAGIOS_HOST_WEB, $dt);
        self::getServiceList('network', PomsService::$NAGIOS_HOST_NETWORK, $dt);
        self::getServiceList('sso', PomsService::$NAGIOS_HOST_SSO, $dt);
        self::getServiceList('solr', PomsService::$NAGIOS_HOST_SOLR, $dt);
        self::getServiceList('database', PomsService::$NAGIOS_HOST_DB, $dt);
        self::getServiceList('bpm', PomsService::$NAGIOS_HOST_BPM, $dt);
    }

    private static function checkEperolehanWebAccessibility($fetchDate = null)
    {
        $fetchDate = $fetchDate ?? Carbon::now();
        $url = 'https://www.eperolehan.gov.my/ssologin';
        $hostgroup = 'web';
        $hostName = 'eperolehan.gov.my';
        $service_name = 'ePerolehan SSO Login';

        try {
            $client = new Client(['timeout' => 10]);
            $response = $client->get($url);
            $statusCode = $response->getStatusCode();

            $status = ($statusCode === 200) ? 2 : 16; // 2 = OK, 16 = UNKNOWN
            Log::info("✅ Web check succeeded for $url with status $statusCode");
        } catch (\Exception $e) {
            $status = 16; // UNKNOWN
            Log::error("❌ Web check failed for $url: " . $e->getMessage());
        }

        self::insertSlaNagios($fetchDate, $hostgroup, $hostName, null, $service_name, $status);
    }

    private static function getServiceList($hostgroup, $hostlist, $datetime)
    {
        //        $dateFormat = '1574215200000';
        //        $queryDate = Carbon::createFromTimestamp($dateFormat / 1000);

        $queryDate = Carbon::parse($datetime);
        $dateTimestamp = $queryDate->timestamp * 1000; //timestamp in milliseconds

        foreach ($hostlist as $host) {
            $hostName = $host;
            $serviceUri = 'http://nagios-apps01.eperolehan.com.my/nagios/cgi-bin/statusjson.cgi?query=servicelist';
            $objService = self::fetchNagiosData($serviceUri . '&dateformat=' . $dateTimestamp . '&hostname=' . $hostName);

            $mainService = $objService->data->servicelist;

            foreach ($mainService as $host => $service) {
                if ($hostgroup == 'network' || $hostgroup == 'solr') {
                    $ping = self::checkServiceComponent($service, 'Ping', $queryDate, $hostgroup, $hostName);
                } else {
                    $tcp = self::checkServiceComponent($service, 'TCP Port ', $queryDate, $hostgroup, $hostName);
                    $tns = self::checkServiceComponent($service, 'TNS Ping', $queryDate, $hostgroup, $hostName);
                }
            }
        }
    }

    private static function checkServiceComponent($service, $componentName, $fetchDate, $hostgroup, $hostName)
    {
        $componentObj = (object)array();
        foreach ($service as $key => $status) {
            if (strpos($key, $componentName) !== false) {
                self::insertSlaNagios($fetchDate, $hostgroup, $hostName, $service, $key, $status);
            }
        }

        return $componentObj;
    }

    private static function fetchNagiosData($uri)
    {
        $username = 'middleware';
        $password = 'cDc@2019';

        $client = new Client();
        $body = $client->request('GET', $uri, [
            'headers' => [
                'Accept' => 'application/json',
                'Content-type' => 'application/json'
            ],
            'auth' => [
                $username,
                $password
            ]
        ])->getBody();

        return json_decode($body);
    }

    private static function insertSlaNagios($fetchDate, $hostgroup, $hostName, $obj, $service_name, $status)
    {
        Log::info(self::class . ' Start : ' . __FUNCTION__ . '     ->> Fetch Datetime: ' . $fetchDate);

        /* ADD exclude service name from array listed */
        $isExclude = 0; //default
        if (in_array($service_name, PomsService::$NAGIOS_EXCLUDE_SERVICE)) {
            $isExclude = 1;
        }

        $id = (string)Str::uuid();

        $nData = new SlaNagios;
        $nData->id = $id;
        $nData->query_datetime = $fetchDate;
        $nData->host_group = $hostgroup;
        $nData->host_name = $hostName;
        $nData->service_name = $service_name;
        $nData->service_status = $status;
        $nData->created_at = Carbon::now();
        $nData->is_exclude = $isExclude;

        if ($nData->save()) {
            $logsdata = self::class . ' Successfully insert data to Sla_nagios => ID: ' . $id;
            Log::info($logsdata);
            echo $logsdata . "\n";
        }
    }
}
