<?php

/*
 * Function : Insert data from table in db crm into table sla_byapprover in db poms
 * Date : 22 Oct 2019
 * Author : Nur Asmaa
 */

namespace App\crm\sla;

use Carbon\Carbon;
use DateTime;
use Log;
use DB;
use Ramsey\Uuid\Uuid;
use App\Nagios\MigrateUtils;
use Config;
use App\BatchMonitoringStatistic;

class insertITByApproverSlaData {  
    
    public static function runCheckingSlaITByApproverData($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting ... Checking SLA IT Specialist Data after approve by Approver', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);

        self::insertITByApproverSlaData($dateStart, $dateEnd);
    }

    /* Read data from Database crm copy into database cdc_poms */

    private static function insertITByApproverSlaData($dateStart, $dateEnd) {

        Log::info(self::class . ' Start : ' . __FUNCTION__ . '     ->> Date Start: ' . $dateStart . ', Date End: ' . $dateEnd);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);
        $dtStartTime = Carbon::now();
        
        $yesterdayData = Carbon::yesterday();
        $getCurr = strtotime($yesterdayData);
        $processDate = date('Y-m-d', $getCurr);
                
         try{
             
             $result = "SELECT DISTINCT c.case_number AS case_number, 
                        c.redmine_number AS redmine_number,
                        UPPER(c.redmine_implementation_issue) AS redmine_implementation_issue,
                        UPPER(approver_t.child_parent_redmine) AS redmine_child_parent,
                        CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS itapprover_case_created,
                        c.name AS itapprover_name,
                        approver_t.name AS itapprover_task_name,
                        approver_tc.task_number_c AS itapprover_task_number,
                        approver_tc.sla_task_flag_c AS itapprover_sla_flag,
                        CONVERT_TZ(approver_t.date_start,'+00:00','+08:00') AS itapprover_start_datetime,
                        CONVERT_TZ(approver_t.date_due,'+00:00','+08:00') AS itapprover_due_datetime,
                        CONVERT_TZ(approver_t.date_start,'+00:00','+08:00') AS itapprover_actual_start_datetime,
                        CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00') AS itapprover_completed_datetime,
                        approver_tc.task_duration_c AS itapprover_duration,
                        DATEDIFF(STR_TO_DATE(CONVERT_TZ(approver_t.date_due,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(approver_t.date_start,'+00:00','+08:00'),'%Y-%m-%d')) AS itapprover_available_duration,
                        DATEDIFF(STR_TO_DATE(CONVERT_TZ(approver_t.reassign_time_c,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(approver_t.date_start,'+00:00','+08:00'),'%Y-%m-%d')) AS itapprover_actual_duration, 
                        approver_tc.is_sent
                        FROM ((((cases c 
                        JOIN cases_cstm cc ON((c.id = cc.id_c))) 
                        LEFT JOIN tasks approver_t ON((c.id = approver_t.parent_id))) 
                        LEFT JOIN tasks_cstm approver_tc ON(((approver_t.id = approver_tc.id_c) 
                        AND (approver_tc.sla_task_flag_c = '4')))) 
                        LEFT JOIN cstm_list_app subcat ON(((cc.sub_category_c = subcat.value_code) 
                        AND (subcat.type_code = 'cdc_sub_category_list') 
                        AND (TRIM(subcat.value_code) <> '') 
                        AND (subcat.value_code NOT IN ('10712_15034','10714_15842','10713_15534'))))) 
                        WHERE ((cc.incident_service_type_c = 'incident_it') 
                        AND (c.status IN ('Pending_User_Verification', 'Open_Resolved', 'Closed_Approved', 'Closed_Cancelled_Eaduan', 'Closed_Closed', 'Closed_Rejected', 'Closed_Rejected_Eaduan', 'Closed_Verified_Eaduan'))
                        AND (approver_t.status = 'Completed') 
                        AND (cc.request_type_c = 'incident')
                        AND (c.deleted = 0)
                        AND (approver_t.deleted = 0)
                        AND (approver_tc.task_number_c IS NOT NULL) 
                        AND (approver_tc.task_duration_c > 0)
                        AND (TRIM(subcat.value_code) <> '')) 
                        AND (STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') <= ?)
                        ORDER BY c.case_number DESC";
     
            $results = DB::connection('mysql_crm')->select($result, array($processDate));
            // dump('total query cases: ' . count($results));
                       
            if (is_array($results) || is_object($results)) {
                $counter = 0;
                foreach ($results as $data) {
                    // dump($data->itapprover_task_number);
                    $count = DB::connection('mysql')->table('sla_byapprover')
                            ->where('itapprover_task_number', $data->itapprover_task_number)
                            ->count();

                    // dump('is sla_byapprover exist? ' . $count);

                    $insertedTaskNum = DB::connection('mysql_crm')->table('cases')
                            ->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                            ->join('tasks', 'tasks.parent_id', '=', 'cases_cstm.id_c')
                            ->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
                            ->where('tasks_cstm.task_number_c', $data->itapprover_task_number)
                            ->where('cases_cstm.incident_service_type_c', 'incident_it')
                            ->where('tasks_cstm.sla_task_flag_c', 4)
                            ->where('tasks_cstm.is_sent', 1)
                            ->count();

                    // dump('is crm sent? ' . $insertedTaskNum);

                    if ($count == 0 && $insertedTaskNum == 0) {
                        // dump('inserting... ' . $data->case_number);
                        
                        $insertDataDate = Carbon::now();

                        DB::connection('mysql')
                                ->insert('insert into sla_byapprover 
                            (case_number, redmine_number, redmine_implementation_issue, redmine_child_parent, itapprover_case_created, itapprover_name, itapprover_task_name, itapprover_task_number, itapprover_sla_flag, itapprover_duration, itapprover_start_datetime, itapprover_due_datetime, itapprover_actual_start_datetime, itapprover_completed_datetime, itapprover_available_duration, itapprover_actual_duration, itapprover_insert_data_datetime) 
                            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                                    $data->case_number,
                                    $data->redmine_number,
                                    $data->redmine_implementation_issue,
                                    $data->redmine_child_parent,
                                    $data->itapprover_case_created,
                                    $data->itapprover_name,
                                    $data->itapprover_task_name,
                                    $data->itapprover_task_number,
                                    $data->itapprover_sla_flag,
                                    $data->itapprover_duration,
                                    $data->itapprover_start_datetime,
                                    $data->itapprover_due_datetime,
                                    $data->itapprover_actual_start_datetime,
                                    $data->itapprover_completed_datetime,
                                    $data->itapprover_available_duration,
                                    $data->itapprover_actual_duration,
                                    $insertDataDate
                        ]);
                        
                        DB::connection('mysql_crm')
                                ->table('cases')
                                ->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                                ->join('tasks', 'tasks.parent_id', '=', 'cases_cstm.id_c')
                                ->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
                                ->where('tasks_cstm.task_number_c', $data->itapprover_task_number)
                                ->where('cases_cstm.incident_service_type_c', 'incident_it')
                                ->where ('tasks_cstm.sla_task_flag_c', 4)
                                ->update([
                                    'tasks_cstm.is_sent' => 1,
                                    'tasks_cstm.poms_inserted_date' => $insertDataDate
                        ]);


                        $counter++;
                        if ($counter == 20) {
                            sleep(1);
                            $counter = 0;
                        }

                        $logsdata = self::class . ' Successfully insert data for SLA IT Specialist by Approver => Date Start : ' . $dtStartTime . ' -> '
                                . 'Query Date End : ' . Carbon::now() . ' Task Number : ' . $data->itapprover_task_number . ' , Completed --- Taken Time : ' .
                                json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

                        Log::info($logsdata);
                        dump($logsdata);
                    }
                }
                
                $total = count($results);
                $note = ' Successfully insert data for SLA by Approver (s4) => Total : ' . $total;
                $projName = 'POMS Integration';
                $batchName = 'insertITByApproverSlaData';
                $remarks = $note;
                $dateModified = Carbon::now();
                $status = 'Success';
                $list = BatchMonitoringStatistic::getBatchName($batchName);
                $dataList =BatchMonitoringStatistic::where('batch_name', 'insertITByApproverSlaData')
                    ->first();
                log::info('Masuk Success');
                if (count($list) > 0) {
                    log::info('Success Satu');
                      BatchMonitoringStatistic::updateBatchMonitoring($dataList,$status,$remarks,$dateModified);
                } else {
                      BatchMonitoringStatistic::createBatchMonitoring($projName,$batchName,$status,$remarks,$dateModified);
                    log::info('Success Dua');
                }
            }

            return $results;
             
         }catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
                $projName = 'POMS Integration';
                $batchName = 'insertITByApproverSlaData';
                $remarks = $exc->getMessage();
                $dateModified = Carbon::now();
                $status = 'Failed';
                $list = BatchMonitoringStatistic::getBatchName($batchName);
                $dataList =BatchMonitoringStatistic::where('batch_name', 'insertITByApproverSlaData')
                    ->first();
                log::info('Masuk Error');
                if (count($list) > 0) {
                    log::info('Error Satu');
                      BatchMonitoringStatistic::updateBatchMonitoring($dataList,$status,$remarks,$dateModified);
                } else {
                       BatchMonitoringStatistic::createBatchMonitoring($projName,$batchName,$status,$remarks,$dateModified);
                    log::info('Error Dua');
                }
            echo $exc->getTraceAsString();
        }     
        return null;
    }

}
