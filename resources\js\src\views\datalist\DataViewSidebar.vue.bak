<!-- =========================================================================================
  File Name: AddNewDataSidebar.vue
  Description: Add New Data - Sidebar component
  ----------------------------------------------------------------------------------------
  Item Name: Vuexy - Vue<PERSON>s, HTML & Laravel Admin Dashboard Template
  Author: Pixinvent
  Author URL: http://www.themeforest.net/user/pixinvent
========================================================================================== -->


<template>
  <vs-sidebar
    click-not-close
    position-right
    parent="body"
    default-index="1"
    color="primary"
    class="add-new-data-sidebar items-no-padding"
    spacer
    v-model="isSidebarActiveLocal"
  >
    <div class="mt-6 flex items-center justify-between px-6">
      <h4>{{ Object.entries(this.data).length === 0 ? "ADD NEW" : "UPDATE" }} ITEM</h4>
      <feather-icon icon="XIcon" @click.stop="isSidebarActiveLocal = false" class="cursor-pointer"></feather-icon>
    </div>
    <vs-divider class="mb-0"></vs-divider>

    <VuePerfectScrollbar class="scroll-area--data-list-add-new" :settings="settings" :key="$vs.rtl">
      <div class="p-6">

        {{ dataId }}

        <!-- FULL NAME -->
        <vs-input
          label="Full Name"
          v-model="name"
          class="mt-5 w-full"
          name="full-name"
          v-validate="'required'"
        />
        <span
          class="text-danger text-sm"
          v-show="errors.has('full-name')"
        >{{ errors.first('full-name') }}</span>

        <!-- USERNAME -->
        <vs-input
          label="Username"
          v-model="username"
          class="mt-5 w-full"
          name="username"
          v-validate="'required'"
        />
        <span
          class="text-danger text-sm"
          v-show="errors.has('username')"
        >{{ errors.first('username') }}</span>

        <!-- USERNAME -->
        <vs-input
          label="Password"
          v-model="password"
          class="mt-5 w-full"
          name="password"
          v-validate="'required'"
        />
        <span
          class="text-danger text-sm"
          v-show="errors.has('password')"
        >{{ errors.first('password') }}</span>

        <!-- EMAIL -->
        <vs-input
          label="Email"
          v-model="email"
          class="mt-5 w-full"
          name="email"
          v-validate="'required|email'"
        />
        <span class="text-danger text-sm" v-show="errors.has('email')">{{ errors.first('email') }}</span>

        <!-- ROLE -->
        <!-- <div class="mt-6">
            <div class="modelx">
              {{form.roles}}
            </div>
            <vs-col v-for="(role, index) in roles" :key="index">
              <vs-checkbox v-model="form.roles" :vs-value="role" >{{ role.name }}</vs-checkbox>
            </vs-col>
        </div>-->
        <ul class="centerx">
          <li class="modelx">{{role}}</li>
          <li>
            <vs-checkbox v-model="role" vs-value="1">Administrator</vs-checkbox>
          </li>
          <li>
            <vs-checkbox v-model="role" vs-value="2">SLA Dashboard</vs-checkbox>
          </li>
          <li>
            <vs-checkbox v-model="role" vs-value="3">SLA Report</vs-checkbox>
          </li>
          <li>
            <vs-checkbox v-model="role" vs-value="4">Technical Dashboard</vs-checkbox>
          </li>
        </ul>

        <!-- CATEGORY -->
        <!-- <vs-select v-model="dataCategory" label="Category" class="mt-5 w-full" name="item-category" v-validate="'required'">
          <vs-select-item :key="item.value" :value="item.value" :text="item.text" v-for="item in category_choices" />
        </vs-select>
        <span class="text-danger text-sm" v-show="errors.has('item-category')">{{ errors.first('item-category') }}</span>-->

        <!-- ORDER STATUS -->
        <!-- <vs-select v-model="dataOrder_status" label="Order Status" class="mt-5 w-full">
          <vs-select-item :key="item.value" :value="item.value" :text="item.text" v-for="item in order_status_choices" />
        </vs-select>-->

        <!-- PRICE -->
        <!-- <vs-input
          icon-pack="feather"
          icon="icon-dollar-sign"
          label="Price"
          v-model="dataPrice"
          class="mt-5 w-full"
          v-validate="{ required: true, regex: /\d+(\.\d+)?$/ }"
          name="item-price" />
        <span class="text-danger text-sm" v-show="errors.has('item-price')">{{ errors.first('item-price') }}</span>-->

        <!-- Upload -->
        <!-- <vs-upload text="Upload Image" class="img-upload" ref="fileUpload" /> -->

        <!-- <div class="upload-img mt-5" v-if="!dataImg">
          <input type="file" class="hidden" ref="uploadImgInput" @change="updateCurrImg" accept="image/*">
          <vs-button @click="$refs.uploadImgInput.click()">Upload Image</vs-button>
        </div>-->
      </div>
    </VuePerfectScrollbar>

    <div class="flex flex-wrap items-center p-6" slot="footer">
      <vs-button class="mr-6" @click="submitData" :disabled="!isFormValid">Submit</vs-button>
      <vs-button type="border" color="danger" @click="isSidebarActiveLocal = false">Cancel</vs-button>
    </div>
  </vs-sidebar>
</template>

<script>
import VuePerfectScrollbar from "vue-perfect-scrollbar";

export default {
  props: {
    isSidebarActive: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    isSidebarActive(val) {
      if (!val) return;
      if (Object.entries(this.data).length === 0) {
        this.initValues();
        this.$validator.reset();
      } else {
        let {
          id,
          name,
          username,
          //password,
          email,
          role
        } = JSON.parse(JSON.stringify(this.data));
        this.dataId = id;
        this.name = name;
        this.username = username;
        //this.password = password;
        this.email = email;
        this.role = role;
        this.initValues();
      }
      // Object.entries(this.data).length === 0 ? this.initValues() : { this.dataId, this.name, this.dataCategory, this.dataOrder_status, this.dataPrice } = JSON.parse(JSON.stringify(this.data))
    }
  },
  data() {
    return {
      dataId: null,
      name: "",
      username: "",
      password: "",
      email: "",
      role:[],

      settings: {
        // perfectscrollbar settings
        maxScrollbarLength: 60,
        wheelSpeed: 0.6
      }
    };
  },
  computed: {
    isSidebarActiveLocal: {
      get() {
        return this.isSidebarActive;
      },
      set(val) {
        if (!val) {
          this.$emit("closeSidebar");
          // this.$validator.reset()
          // this.initValues()
        }
      }
    },
    isFormValid() {
      return (
        !this.errors.any() &&
        this.name &&
        this.username &&
        this.password &&
        this.email &&
        this.role.length > 0
      );
    }
  },
  methods: {
    initValues() {
      if (this.data.id) return;
      this.dataId = null;
      this.name = "";
      this.username = "";
      this.password = "";
      this.email = "";
      this.role = [];
    },
    submitData() {
      this.$validator.validateAll().then(result => {
        if (result) {
          const obj = {
            id: this.dataId,
            name: this.name,
            username: this.username,
            password: this.password,
            email: this.email,
            role: this.role
          };

          if (this.dataId !== null && this.dataId >= 0) {
            this.$store.dispatch("dataList/updateItem", obj).catch(err => {
              console.error(err);
            });
          } else {
            delete obj.id;
            //obj.popularity = 0;
            // this.$store.dispatch("dataList/addItem", obj).catch(err => {
            //   console.error(err);
            // });
            this.register();
          }

          this.$emit("closeSidebar");
          this.initValues();
        }
      });
    },
    register() {
        var app = this
        this.$auth.register({
          data: {
            name: app.name,
            username: app.username,
            email: app.email,
            role: app.role,
            password: app.password,
            password_confirmation: app.password
          },
          success: function () {
            //app.success = true
            //this.$router.push({name: 'login', params: {successRegistrationRedirect: true}})
          },
          error: function (res) {
            console.log(res.response.data.errors)
            app.has_error = true
            app.error = res.response.data.error
            app.errors = res.response.data.errors || {}
          }
        })
      }
  },
  components: {
    VuePerfectScrollbar
  }
};
</script>

<style lang="scss" scoped>
.add-new-data-sidebar {
  ::v-deep .vs-sidebar--background {
    z-index: 52010;
  }

  ::v-deep .vs-sidebar {
    z-index: 52010;
    width: 400px;
    max-width: 90vw;

    .img-upload {
      margin-top: 2rem;

      .con-img-upload {
        padding: 0;
      }

      .con-input-upload {
        width: 100%;
        margin: 0;
      }
    }
  }
}

.scroll-area--data-list-add-new {
  // height: calc(var(--vh, 1vh) * 100 - 4.3rem);
  height: calc(var(--vh, 1vh) * 100 - 16px - 45px - 82px);
}
</style>
