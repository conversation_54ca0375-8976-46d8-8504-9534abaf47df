<?php

namespace App\Http\Controllers;

use App\EpPerformance;
use App\Http\Resources\CrmPerformanceResources;
use App\Http\Resources\EpPerformanceResource;
use App\Services\PomsService;
use Carbon\Carbon;
use DateTime;
use GuzzleHttp\Client;
use Goutte\Client as GClient;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class PomsController extends Controller
{

    public static function pomsService()
    {
        return new PomsService;
    }

    public function getEpPerformanceByDate()
    {
        // $perfromanceDate = Carbon::parse('2019-09-01');
        $perfromanceDate = Carbon::now();

        // Get parameters
        $performanceList = EpPerformance::whereDate('date_time', $perfromanceDate)->get();

        // Return collection of parameters as a resource
        return EpPerformanceResource::collection($performanceList);
    }

    public function fetchEPPerformanceData()
    {

        $cacheKey = 'ep_performance_data';
        $cacheDuration = now()->addHours(24);

        // Check if data exists in the cache
        if (Cache::has($cacheKey)) {
            $cachedData = Cache::get($cacheKey);
            return response()->json($cachedData);
        }

        // $perfromanceDate = Carbon::now();
        $latestDate = EpPerformance::select('date_time')->orderBy('date_time', 'desc')->first(); // latest data date
        $perfromanceDate = Carbon::parse($latestDate->date_time);

        // Get parameters
        $performanceList = EpPerformance::whereDate('date_time', $perfromanceDate)
            ->addSelect(
                'module',
                'transaction',
                DB::raw('count(transaction) as total_trans'),
                DB::raw('SUM(IF(duration_result > 3000, 1, 0)) as total_trans_exceed')
            )
            ->groupBy('module', 'transaction')
            ->get();

        $epPerformanceData = (object) array();
        $portalArray = array();
        $pPlanArray = array();
        $qtArray = array();
        $dpArray = array();
        $ctMgmtArray = array();
        $flArray = array();
        $smArray = array();
        $catalogueArray = array();

        //count
        $totalTrans_portal = 0;
        $totalTrans_pPlan = 0;
        $totalTrans_qt = 0;
        $totalTrans_dp = 0;
        $totalTrans_ctMgmt = 0;
        $totalTrans_fl = 0;
        $totalTrans_sm = 0;
        $totalTrans_catalogue = 0;

        $totalExceedTrans_portal = 0;
        $totalExceedTrans_pPlan = 0;
        $totalExceedTrans_qt = 0;
        $totalExceedTrans_dp = 0;
        $totalExceedTrans_ctMgmt = 0;
        $totalExceedTrans_fl = 0;
        $totalExceedTrans_sm = 0;
        $totalExceedTrans_catalogue = 0;

        foreach ($performanceList as $data) {
            //set parent - module
            if ($data->module == 'SLA-PORTAL-LOGIN') {
                $totalTrans_portal += $data->total_trans;
                $totalExceedTrans_portal += $data->total_trans_exceed;
                array_push($portalArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-SM-SP') {
                $totalTrans_sm += $data->total_trans;
                $totalExceedTrans_sm += $data->total_trans_exceed;
                array_push($smArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-SM-VA') {
                $totalTrans_sm += $data->total_trans;
                $totalExceedTrans_sm += $data->total_trans_exceed;
                array_push($smArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-SM-SR') {
                $totalTrans_sm += $data->total_trans;
                $totalExceedTrans_sm += $data->total_trans_exceed;
                array_push($smArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-SM-') {
                $totalTrans_sm += $data->total_trans;
                $totalExceedTrans_sm += $data->total_trans_exceed;
                array_push($smArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-FL-IN') {
                $totalTrans_fl += $data->total_trans;
                $totalExceedTrans_fl += $data->total_trans_exceed;
                array_push($flArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-SC-RN') {
                $totalTrans_dp += $data->total_trans;
                $totalExceedTrans_dp += $data->total_trans_exceed;
                array_push($dpArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-FL-FN') {
                $totalTrans_fl += $data->total_trans;
                $totalExceedTrans_fl += $data->total_trans_exceed;
                array_push($flArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-FL-PR') {
                $totalTrans_fl += $data->total_trans;
                $totalExceedTrans_fl += $data->total_trans_exceed;
                array_push($flArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-CT-FD') {
                $totalTrans_ctMgmt += $data->total_trans;
                $totalExceedTrans_ctMgmt += $data->total_trans_exceed;
                array_push($ctMgmtArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-CT-SA') {
                $totalTrans_ctMgmt += $data->total_trans;
                $totalExceedTrans_ctMgmt += $data->total_trans_exceed;
                array_push($ctMgmtArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-CM-SA') {
                $totalTrans_ctMgmt += $data->total_trans;
                $totalExceedTrans_ctMgmt += $data->total_trans_exceed;
                array_push($ctMgmtArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-CM-AC') {
                $totalTrans_ctMgmt += $data->total_trans;
                $totalExceedTrans_ctMgmt += $data->total_trans_exceed;
                array_push($ctMgmtArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-CT-AC') {
                $totalTrans_ctMgmt += $data->total_trans;
                $totalExceedTrans_ctMgmt += $data->total_trans_exceed;
                array_push($ctMgmtArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-PM-PS') {
                $totalTrans_pPlan += $data->total_trans;
                $totalExceedTrans_pPlan += $data->total_trans_exceed;
                array_push($pPlanArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-SC-PN') {
                $totalTrans_qt += $data->total_trans;
                $totalExceedTrans_qt += $data->total_trans_exceed;
                array_push($qtArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-SC-BD') {
                $totalTrans_qt += $data->total_trans;
                $totalExceedTrans_qt += $data->total_trans_exceed;
                array_push($qtArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-SC-SE') {
                $totalTrans_qt += $data->total_trans;
                $totalExceedTrans_qt += $data->total_trans_exceed;
                array_push($qtArray, $this->setResponseObj($data));
            }

            if ($data->module == 'SLA-CM-SC') {
                $totalTrans_catalogue += $data->total_trans;
                $totalExceedTrans_catalogue += $data->total_trans_exceed;
                array_push($catalogueArray, $this->setResponseObj($data));
            }
        }

        // calculate ratio
        $ratio_sm = $this->calcRatio($totalTrans_sm, $totalExceedTrans_sm);
        $ratio_portal = $this->calcRatio($totalTrans_portal, $totalExceedTrans_portal);
        $ratio_pPlan = $this->calcRatio($totalTrans_pPlan, $totalExceedTrans_pPlan);
        $ratio_qt = $this->calcRatio($totalTrans_qt, $totalExceedTrans_qt);
        $ratio_dp = $this->calcRatio($totalTrans_dp, $totalExceedTrans_dp);
        $ratio_ctMgmt = $this->calcRatio($totalTrans_ctMgmt, $totalExceedTrans_ctMgmt);
        $ratio_fl = $this->calcRatio($totalTrans_fl, $totalExceedTrans_fl);
        $ratio_catalogue = $this->calcRatio($totalTrans_catalogue, $totalExceedTrans_catalogue);

        $epPerformanceData = array(
            array(
                'module_name' => 'Portal',
                'total_trans' => $totalTrans_portal,
                'total_exceed_trans' => $totalExceedTrans_portal,
                'ratio' => $ratio_portal,
                'module_data' => $portalArray
            ),
            array(
                'module_name' => 'Procurement Plan',
                'total_trans' => $totalTrans_pPlan,
                'total_exceed_trans' => $totalExceedTrans_pPlan,
                'ratio' => $ratio_pPlan,
                'module_data' => $pPlanArray
            ),
            array(
                'module_name' => 'Quotation / Tender',
                'total_trans' => $totalTrans_qt,
                'total_exceed_trans' => $totalExceedTrans_qt,
                'ratio' => $ratio_qt,
                'module_data' => $qtArray
            ),
            array(
                'module_name' => 'Direct Purchase',
                'total_trans' => $totalTrans_dp,
                'total_exceed_trans' => $totalExceedTrans_dp,
                'ratio' => $ratio_dp,
                'module_data' => $dpArray
            ),
            array(
                'module_name' => 'Contract Management',
                'total_trans' => $totalTrans_ctMgmt,
                'total_exceed_trans' => $totalExceedTrans_ctMgmt,
                'ratio' => $ratio_ctMgmt,
                'module_data' => $ctMgmtArray
            ),
            array(
                'module_name' => 'Fulfillment',
                'total_trans' => $totalTrans_fl,
                'total_exceed_trans' => $totalExceedTrans_fl,
                'ratio' => $ratio_fl,
                'module_data' => $flArray
            ),
            array(
                'module_name' => 'Supplier Management',
                'total_trans' => $totalTrans_sm,
                'total_exceed_trans' => $totalExceedTrans_sm,
                'ratio' => $ratio_sm,
                'module_data' => $smArray
            ),
            array(
                'module_name' => 'Catalogue Management',
                'total_trans' => $totalTrans_catalogue,
                'total_exceed_trans' => $totalExceedTrans_catalogue,
                'ratio' => $ratio_catalogue,
                'module_data' => $catalogueArray
            ),
        );

        $epPerformanceData = [
            'query_date' => $perfromanceDate->format('Y-m-d'),
            'data' => $epPerformanceData // Update this with your final processed data structure
        ];

        // Store the data in the cache
        Cache::put($cacheKey, $epPerformanceData, $cacheDuration);

        return response()->json($epPerformanceData);
    }

    private function setResponseObj($data)
    {
        $obj = (object) array();

        $obj->module = $data->module;
        $obj->transaction = $data->transaction;
        $obj->total_trans = $data->total_trans;
        $obj->total_trans_exceed = $data->total_trans_exceed;
        $obj->ratio = $this->calcRatio($data->total_trans, $data->total_trans_exceed);

        return $obj;
    }

    private function calcRatio($total, $totalExceed)
    {
        if($total > 0) {
            return round((($total - $totalExceed) / $total) * 100, 2);
        }

        return 100;

    }

    private function setTransValue($data, $module, $totalTrans, $totalExceedTrans, $moduleArray)
    {
        if ($data->module == $module) {
            $totalTrans += $data->total_trans;
            $totalExceedTrans += $data->total_trans_exceed;
            array_push($moduleArray, $this->setResponseObj($data));
        }
    }
}
