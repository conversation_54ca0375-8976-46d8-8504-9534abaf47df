APP_NAME=eP-POMS
APP_ENV=local
APP_KEY=base64:08TUaJslHmV3qjxTGezY6PAHYaf96HmwYAyfp87tleA=
APP_DEBUG=true
APP_URL=http://localhost:8000
MIX_APP_URL="${APP_URL}"

LOG_CHANNEL=stack

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
#DB_DATABASE=cdc_poms
DB_DATABASE=cdcpoms
DB_USERNAME=root
DB_PASSWORD=password

DB_MYSQL_EP_SUPPORT_HOST=**************
DB_MYSQL_EP_SUPPORT_PORT=3306
DB_MYSQL_EP_SUPPORT_DATABASE=ep_support
DB_MYSQL_EP_SUPPORT_USERNAME=epssadmin
DB_MYSQL_EP_SUPPORT_PASSWORD=cDc@2018

DB_NEXTGEN_RPT_HOST=racrpt-cluster-scan.eperolehan.com.my
DB_NEXTGEN_RPT_PORT=1521
DB_NEXTGEN_RPT_DATABASE=ngeprpt
DB_NEXTGEN_RPT_USERNAME=NGEP_READ
DB_NEXTGEN_RPT_PASSWORD=ng3p_r34d

DB_MITEL_HOST=************
DB_MITEL_PORT=1433
DB_MITEL_DATABASE=CCMData
DB_MITEL_USERNAME=middleware-db
DB_MITEL_PASSWORD=Cdc@m1ddl3ware

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

PASSPORT_LOGIN_ENDPOINT="http://poms.local/oauth/token"
PASSPORT_CLIENT_ID=2
PASSPORT_CLIENT_SECRET=IzyChxRWlvDttxwOKnyDQp6ZOOb9L52OCq3f9u3w

JWT_SECRET=m12XAvyqqeUFJctmcQoZS6s9sfMsmfcsU9mjRGKeH6O0C57KjcHQjo0TzVF9FaQm
