<template>
    <div id="ag-grid-demo">
        <vx-card>

            <!-- TABLE ACTION ROW -->
            <div class="flex flex-wrap justify-between items-center">

                <!-- ITEMS PER PAGE -->
                <div class="mb-4 md:mb-0 mr-4 ag-grid-table-actions-left">
                    <vs-dropdown vs-trigger-click class="cursor-pointer">
                        <div
                            class="p-4 border border-solid d-theme-border-grey-light rounded-full d-theme-dark-bg cursor-pointer flex items-center justify-between font-medium">
                            <span class="mr-2">{{ currentPage * paginationPageSize - (paginationPageSize - 1) }} - {{ contacts.length - currentPage * paginationPageSize > 0 ? currentPage * paginationPageSize : contacts.length }} of {{ contacts.length }}</span>
                            <feather-icon icon="ChevronDownIcon" svgClasses="h-4 w-4"/>
                        </div>
                        <!-- <vs-button class="btn-drop" type="line" color="primary" icon-pack="feather" icon="icon-chevron-down"></vs-button> -->
                        <vs-dropdown-menu>

                            <vs-dropdown-item @click="gridApi.paginationSetPageSize(20)">
                                <span>20</span>
                            </vs-dropdown-item>
                            <vs-dropdown-item @click="gridApi.paginationSetPageSize(50)">
                                <span>50</span>
                            </vs-dropdown-item>
                            <vs-dropdown-item @click="gridApi.paginationSetPageSize(100)">
                                <span>100</span>
                            </vs-dropdown-item>
                            <vs-dropdown-item @click="gridApi.paginationSetPageSize(150)">
                                <span>150</span>
                            </vs-dropdown-item>
                        </vs-dropdown-menu>
                    </vs-dropdown>
                </div>

                <!-- TABLE ACTION COL-2: SEARCH & EXPORT AS CSV -->
                <div class="flex flex-wrap items-center justify-between ag-grid-table-actions-right">
                    <vs-input class="mb-4 md:mb-0 mr-4" v-model="searchQuery" @input="updateSearchQuery"
                              placeholder="Search..."/>
                    <vs-button class="mb-4 md:mb-0 mr-4" @click="gridApi.exportDataAsCsv()" icon-pack="feather" icon="icon-file-text">Export as CSV</vs-button>
                    <vs-button class="mb-4 md:mb-0" to="/sla-dashboard" icon-pack="feather" icon="icon-arrow-left">Back</vs-button>
                </div>
            </div>
            <ag-grid-vue
                :gridOptions="gridOptions"
                class="ag-theme-material w-100 my-4 ag-grid-table"
                :columnDefs="columnDefs"
                :defaultColDef="defaultColDef"
                :rowData="contacts"
                rowSelection="multiple"
                colResizeDefault="shift"
                :animateRows="true"
                :floatingFilter="true"
                :pagination="true"
                :paginationPageSize="paginationPageSize"
                :suppressPaginationPanel="true">
            </ag-grid-vue>
            <vs-pagination
                :total="totalPages"
                :max="maxPageNumbers"
                v-model="currentPage"/>
        </vx-card>
    </div>
</template>

<script>
    import {AgGridVue} from "ag-grid-vue"
    // import contacts from './data.json' <-- json data here
    // const VxTour = () => import('@/components/VxTour.vue')
    import '@sass/vuesax/extraComponents/agGridStyleOverride.scss'

    export default {
        components: {
            AgGridVue,
            //VxTour,
        },
        data() {
            return {
                searchQuery: '',
                gridOptions: {},
                maxPageNumbers: 7,
                gridApi: null,
                defaultColDef: {
                    sortable: true,
                    editable: true,
                    resizable: true,
                    suppressMenu: true
                },
                columnDefs: [
                    {
                        headerName: 'Case Number',
                        field: 'case_no',
                        filter: true,
                        width: 150,
                        pinned: 'left'
                    },
                    {
                        headerName: 'Status',
                        field: 'status',
                        width: 250,
                        filter: true
                    },
                    {
                        headerName: 'SLA Start',
                        field: 'date_start',
                        filter: true,
                        width: 175,
                    },
                    {
                        headerName: 'SLA End',
                        field: 'date_due',
                        filter: true,
                        width: 250,
                    },
                    {
                        headerName: 'Acknowledge Time',
                        field: 'time_remaining',
                        filter: true,
                        width: 250,
                        suppressSizeToFit: true
                    },
                    {
                        headerName: 'Exceed?',
                        field: 'case_exceed',
                        filter: true,
                        width: 250,
                        hide: true
                    },
                ],
                contacts: 0,
            }
        },
        watch: {
            '$store.state.windowWidth'(val) {
                if (val <= 576) {
                    this.maxPageNumbers = 4;
                    this.gridOptions.columnApi.setColumnPinned('caseNumber', null);
                } else this.gridOptions.columnApi.setColumnPinned('caseNumber', 'left')
            }
        },
        computed: {
            paginationPageSize() {
                if (this.gridApi) return this.gridApi.paginationGetPageSize()
                else return 50
            },
            totalPages() {
                if (this.gridApi) return this.gridApi.paginationGetTotalPages()
                else return 0
            },
            currentPage: {
                get() {
                    if (this.gridApi) return this.gridApi.paginationGetCurrentPage() + 1
                    else return 1
                },
                set(val) {
                    this.gridApi.paginationGoToPage(val - 1);
                }
            }
        },
        methods: {
            updateSearchQuery(val) {
                this.gridApi.setQuickFilter(val);
            },

            fetchCrmData: function () {
                let api_url = '/api/crm/list-it-coordinator';
                this.$vs.loading();
                fetch(api_url)
                    .then(res => res.json())
                    .then(res => {
                        this.contacts = res.data[0].all_cases;
                        this.filterByStatus();
                        this.$vs.loading.close();
                    })
                    .catch(err => console.log(err));
            },

            filterByStatus() {
                setTimeout(() => {
                    let filterStatus = this.gridApi.getFilterInstance("status");
                    filterStatus.selectedFilter = 'equals';
                    filterStatus.filterText = 'Acknowledge';
                    filterStatus.onFilterChanged();
                    let filterExceed = this.gridApi.getFilterInstance("case_exceed");
                    filterExceed.filterText = 'NO';
                    filterExceed.onFilterChanged();
                },200);
            },
        },
        mounted() {
            this.gridApi = this.gridOptions.api;
            this.gridOptions.api.sizeColumnsToFit();
            this.fetchCrmData();
        }
    }

</script>
