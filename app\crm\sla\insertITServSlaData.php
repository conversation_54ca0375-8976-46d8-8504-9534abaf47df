<?php

/*
 * Function : Insert data from table in db crm into table sla_itserv in db poms
 * Date : 11 Oct 2019
 * Author : Nur Asmaa
 */

namespace App\crm\sla;

use Carbon\Carbon;
use DateTime;
use Log;
use DB;
use Ramsey\Uuid\Uuid;
use App\Nagios\MigrateUtils;
use Config;

class insertITServSlaData {  
    
    public static function runCheckingSlaITServData($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting ... Checking SLA IT Coordinator Data ', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);

        self::insertITServSlaData($dateStart, $dateEnd);
    }

    /* Read data from Database crm copy into database cdc_poms */

    private static function insertITServSlaData($dateStart, $dateEnd) {

        Log::info(self::class . ' Start : ' . __FUNCTION__ . '     ->> Date Start: ' . $dateStart . ', Date End: ' . $dateEnd);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);
        $dtStartTime = Carbon::now();
        
        $yesterdayData = Carbon::yesterday();
        $getCurr = strtotime($yesterdayData);
        $processDate = date('Y-m-d', $getCurr);  
                
        try{
             
             $result = "SELECT `cases`.`name` AS `case_name`,
			tasks_cstm.`is_sent`,
			cases.name AS itserv_name,
                        `cases`.`date_entered` AS `case_date_created`,
                        `cases`.`case_number` AS `case_number`,
                        `cases_cstm`.`incident_service_type_c` AS `incident_type`,
                        `tasks_cstm`.`task_number_c` AS `task_number`,
                        `tasks`.`status` AS `task_status`,
                        `cases`.`sla_flag` AS `sla_case_flag`,
                        `tasks_cstm`.`sla_task_flag_c` AS `sla_task_flag`,
                        CONVERT_TZ(`tasks`.`date_start`,'+00:00','+08:00') AS `task_date_start`,
                        CONVERT_TZ(`tasks`.`date_due`,'+00:00','+08:00') AS `task_date_due`,
                        CONVERT_TZ(`tasks`.`sugarfield_datestart_c`,'+00:00','+08:00') AS `task_sugarfield_datestart`,
                        CONVERT_TZ(`tasks`.`sugarfield_dateend_c`,'+00:00','+08:00') AS `task_sugarfield_dateend` ,
                        TIMESTAMPDIFF(SECOND,CONVERT_TZ(`tasks`.`date_start`,'+00:00','+08:00'),CONVERT_TZ(`tasks`.`date_due`,'+00:00','+08:00')) AS `itserv_available_duration`,
                        TIMESTAMPDIFF(SECOND,CONVERT_TZ(`tasks`.`sugarfield_datestart_c`,'+00:00','+08:00'),CONVERT_TZ(`tasks`.`sugarfield_dateend_c`,'+00:00','+08:00')) AS `itserv_actual_duration`
                        FROM ((((`cases` 
                        JOIN `cases_cstm` ON((`cases_cstm`.`id_c` = `cases`.`id`))) 
                        JOIN `tasks` ON((`tasks`.`parent_id` = `cases`.`id`))) 
                        JOIN `tasks_cstm` ON((`tasks_cstm`.`id_c` = `tasks`.`id`))) 
                        LEFT JOIN `cstm_list_app` `subcat` ON(((`cases_cstm`.`sub_category_c` = `subcat`.`value_code`) 
                        AND (`subcat`.`type_code` = 'cdc_sub_category_list') 
                        AND (TRIM(`subcat`.`value_code`) <> '') 
                        AND (`subcat`.`value_code` NOT IN ('10712_15034','10714_15842','10713_15534'))))) 
                        WHERE ((`tasks_cstm`.`sla_task_flag_c` = '15') 
                        AND (cases_cstm.`incident_service_type_c` = 'service_it')
                        AND (tasks.`deleted` = 0)
                        AND (`cases`.`deleted` = 0) 
                        AND (cases.`date_entered` >= '2019-09-01')
                        AND (`cases`.`status` IN ('Pending_User_Verification',  'Open_Resolved', 'Closed_Approved', 'Closed_Cancelled_Eaduan', 'Closed_Closed', 'Closed_Rejected', 'Closed_Rejected_Eaduan', 'Closed_Verified_Eaduan'))
                        AND (STR_TO_DATE(CONVERT_TZ(`cases`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') <= ?)
                        AND (TRIM(`subcat`.`value_code`) <> ''))";
     
            $results = DB::connection('mysql_crm')->select($result, array($processDate));
                       
            if (is_array($results) || is_object($results)) {
                $counter = 0;
                foreach ($results as $data) {
                    $count = DB::connection('mysql')->table('sla_itserv')
                            ->where('case_number', $data->case_number)
                            ->count();

                    $insertedCaseNum = DB::connection('mysql_crm')->table('cases')
                            ->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                            ->join('tasks', 'tasks.parent_id', '=', 'cases_cstm.id_c')
                            ->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
                            ->where('cases.case_number', $data->case_number)
                            ->where('cases_cstm.incident_service_type_c', 'service_it')
                            ->where('tasks_cstm.sla_task_flag_c', 15)
                            ->where('tasks_cstm.is_sent', 1)
                            ->count();                    

                    if ($count == 0 && $insertedCaseNum == 0) {
                        
                    $insertDataDate = Carbon::now();
                    
                        DB::connection('mysql')
                        ->insert('insert into sla_itserv 
                            (case_number, itserv_case_created, itserv_name, itserv_task_number, itserv_incident_type, itserv_task_status, itserv_task_flag, itserv_task_date_start, itserv_task_date_due, itserv_task_sugarfield_datestart, itserv_task_sugarfield_dateend, itserv_available_duration, itserv_actual_duration, itserv_insert_data_datetime) 
                            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', 
                            [   
                                $data->case_number, 
                                $data->case_date_created,
                                $data->itserv_name,
                                $data->task_number,
                                $data->incident_type,
                                $data->task_status,
                                $data->sla_task_flag,
                                $data->task_date_start, 
                                $data->task_date_due,
                                $data->task_sugarfield_datestart,
                                $data->task_sugarfield_dateend,
                                $data->itserv_available_duration,
                                $data->itserv_actual_duration,
                                $insertDataDate
                                ]);
                        
                         DB::connection('mysql_crm')
                                ->table('cases')
                                ->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                                ->join('tasks', 'tasks.parent_id', '=', 'cases_cstm.id_c')
                                ->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
                                ->where('cases.case_number', $data->case_number)
                                ->where('cases_cstm.incident_service_type_c', 'service_it')
                                ->where ('tasks_cstm.sla_task_flag_c', 15)
                                ->update([
                                    'tasks_cstm.is_sent' => 1,
                                    'tasks_cstm.poms_inserted_date' => $insertDataDate
                        ]);
                         
                        $counter++;
                        if ($counter == 20) {
                            sleep(1);
                            $counter = 0;
                        }
                        
                        $logsdata = self::class . ' Successfully insert data for SLA IT Service => Date Start : ' . $dtStartTime . ' -> '
                                . 'Query Date End : ' . Carbon::now() . ' Case Number : ' . $data->case_number .' Task Number : '.$data->task_number . ' , Completed --- Taken Time : ' .
                                json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

                        Log::info($logsdata);
                        dump($logsdata);
                    }
                }
            }           

            return $results;
             
         }catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }     
        return null;
    }

}
