<?php

/*
 * Function : Insert data from table in db crm into table sla_internal_fac in db poms
 * Date : 16 Oct 2019
 * Author : Nur Asmaa
 */

namespace App\crm\sla;

use Carbon\Carbon;
use DateTime;
use Log;
use DB;
use Ramsey\Uuid\Uuid;
use App\Nagios\MigrateUtils;
use Config;

class insertInternalFactorSlaData {  
    
    public static function runCheckingSlaInternalFactorData($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting ... Checking SLA IT Specialist Data ', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);

        self::insertInternalFactorSlaData($dateStart, $dateEnd);
    }

    /* Read data from Database crm copy into database cdc_poms */

    private static function insertInternalFactorSlaData($dateStart, $dateEnd) {

        Log::info(self::class . ' Start : ' . __FUNCTION__ . '     ->> Date Start: ' . $dateStart . ', Date End: ' . $dateEnd);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);
        $dtStartTime = Carbon::now();
        
        $yesterdayData = Carbon::yesterday();
        $getCurr = strtotime($yesterdayData);
        $processDate = date('Y-m-d', $getCurr);
        
         try{
             
             $result = "SELECT cases.id AS caseId,
                        CONVERT_TZ(cases.date_entered,'+00:00','+08:00') AS case_created,
                        cases.case_number AS case_number, 
                        cases_cstm.incident_service_type_c AS incident_type,
                        cases.name AS case_name, 
                        listappx.value_name AS incident_factor,
                        tasks_cstm.task_number_c AS task_number,
                        tasks_cstm.sla_task_flag_c AS task_flag,
                        tasks.`date_modified` AS action_date_by_top,
                        tasks.status AS task_status,
                        tasks.`task_top_datestart` AS top_verify_datestart,
                        tasks.`task_top_datedue` AS top_verify_dateend,
                        cases.`confirm_by_top` as comfirm_bytop,
                        cases.`is_sent_internal`
                        FROM cases
                        LEFT JOIN cases_cstm ON cases.`id`=cases_cstm.`id_c`
                        LEFT JOIN tasks ON tasks.`parent_id`=cases_cstm.`id_c`
                        LEFT JOIN `tasks_cstm` ON tasks_cstm.`id_c`=tasks.`id`
                        LEFT JOIN cstm_list_app listappx ON (listappx.value_code = cases.case_combine_sub_category) 
                        LEFT JOIN cstm_list_app listapp ON (listapp.value_code = tasks_cstm.tasks_combine_c) 
                        LEFT JOIN cstm_list_app listapps ON(((cases_cstm.sub_category_c = listapps.value_code) 
                        AND (listapps.type_code = 'cdc_sub_category_list') 
                        AND (TRIM(listapps.value_code) <> '') 
                        AND (listapps.value_code NOT IN ('10712_15034','10714_15842','10713_15534'))))
                        WHERE (cases.`status` IN ('Pending_User_Verification', 'Open_Resolved', 'Closed_Approved', 'Closed_Cancelled_Eaduan', 'Closed_Closed', 'Closed_Rejected', 'Closed_Rejected_Eaduan', 'Closed_Verified_Eaduan'))
                        AND (TRIM(listapps.value_code) <> '')
                        AND cases.`case_combine_sub_category` = 'internal_factor_12113'
                        AND cases.case_factor = 'internal_factor'
                        AND tasks.`name` = 'Verification From Top Management'
                        AND tasks.status = 'Completed'
                        AND cases.`confirm_by_top` = 'yes'
                        AND (STR_TO_DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00'),'%Y-%m-%d') = ?)
                        ORDER BY cases.`date_entered` DESC";
                             
            $results = DB::connection('mysql_crm')->select($result, array($processDate));            
                 
            if (is_array($results) || is_object($results)) {
                $counter = 0;
                foreach ($results as $data) {
                    $count = DB::connection('mysql')->table('sla_internal_fac')
                            ->where('case_number', $data->case_number)
                            ->count();

                    $insertedTaskNum = DB::connection('mysql_crm')->table('cases')
                            ->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                            ->join('tasks', 'tasks.parent_id', '=', 'cases_cstm.id_c')
                            ->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
                            ->where('cases.case_number', $data->case_number)
                            ->where('cases_cstm.incident_service_type_c', 'incident_it')
                            ->where('cases.case_combine_sub_category','internal_factor_12113')
                            ->where('cases.is_sent_internal', 1)
                            ->count();

                    if ($count == 0 && $insertedTaskNum == 0) {
                        
                        $insertDataDate = Carbon::now();

                        DB::connection('mysql')
                                ->insert('insert into sla_internal_fac 
                            (case_created, case_number, incident_type, subject, task_number, incident_factor, task_flag, action_date_by_top, task_status, top_verify_datestart, top_verify_dateend, comfirm_bytop, internal_insert_data_datetime) 
                            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                                    $data->case_created,
                                    $data->case_number,
                                    $data->incident_type,
                                    $data->case_name,
                                    $data->task_number,
                                    $data->incident_factor,
                                    $data->task_flag,
                                    $data->action_date_by_top,
                                    $data->task_status,
                                    $data->top_verify_datestart,
                                    $data->top_verify_dateend,
                                    $data->comfirm_bytop,
                                    $insertDataDate
                        ]);
                        
                        DB::connection('mysql_crm')
                                ->table('cases')
                                ->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                                ->join('tasks', 'tasks.parent_id', '=', 'cases_cstm.id_c')
                                ->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
                                ->where('cases.case_number', $data->case_number)
                                ->where('cases_cstm.incident_service_type_c', 'incident_it')                                
                                ->where('cases.case_combine_sub_category','internal_factor_12113')
                                ->update([
                                    'cases.is_sent_internal' => 1
                        ]);


                        $counter++;
                        if ($counter == 20) {
                            sleep(1);
                            $counter = 0;
                        }

                        $logsdata = self::class . ' Successfully insert data for SLA Internal Factor => Date Start : ' . $dtStartTime . ' -> '
                                . 'Query Date End : ' . Carbon::now() . ' Case Number : ' . $data->case_number . ' , Completed --- Taken Time : ' .
                                json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

                        Log::info($logsdata);
                        dump($logsdata);
                    }
                }
            }

            return $results;
             
         }catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }     
        return null;
    }
    
}
