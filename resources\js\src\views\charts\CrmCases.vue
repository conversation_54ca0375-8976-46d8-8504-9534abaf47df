<template>
    <div class="vx-col w-full">
        <vx-card id="card-crm-performance" title="CRM Status Performance" class="vs-con-loading__container">
            <div class="mt-4" slot="no-body">
                <div>
                    <vs-table :data="incidents" stripe>
                    <template slot="thead">
                        <vs-th width="50%"></vs-th>
                        <vs-th>In Progress</vs-th>
                        <vs-th>Within SLA</vs-th>
                        <vs-th>Exceed SLA</vs-th>
                    </template>

                    <template slot-scope="{data}">
                        <vs-tr :key="indextr" v-for="(tr, indextr) in data.data">
                            <vs-td>
                                <span class="text-sm">{{ tr.name }}</span>
                            </vs-td>
                            <vs-td>
                                <vs-button color="primary" size="small" @click="openDetailPopup(tr.name, tr.code, 'in_progress')" type="filled" :disabled="!tr.in_progress">
                                    {{tr.in_progress || 0}}
                                </vs-button>
                            </vs-td>
                            <vs-td>
                                <vs-button color="success" size="small" @click="openDetailPopup(tr.name, tr.code, 'within_sla')" type="filled" :disabled="!tr.within_sla">
                                    {{tr.within_sla || 0}}
                                </vs-button>
                            </vs-td>
                            <vs-td>
                                <vs-button color="danger" size="small" @click="openDetailPopup(tr.name, tr.code, 'exceed_sla')" type="filled" :disabled="!tr.exceed_sla">
                                    {{tr.exceed_sla || 0}}
                                </vs-button>
                            </vs-td>
                        </vs-tr>
                    </template>
                    </vs-table>
                </div>



                <vs-popup fullscreen classContent="popup-example" :title="'List: ' + popup_title" :active.sync="popupActive">
                    <vs-table max-items="10" pagination search :data="filteredCases" stripe>

                        <template slot="thead">
                            <vs-th sort-key="case_no">Case Number</vs-th>
                            <vs-th>Case Name</vs-th>
                            <vs-th>Task Name</vs-th>
                            <vs-th>Status</vs-th>
                            <vs-th sort-key="date_start">SLA Start</vs-th>
                            <vs-th sort-key="date_due">SLA End</vs-th>
                            <vs-th sort-key="available_time" v-if="showAvailableTime">Available Time</vs-th>
                        </template>

                        <template slot-scope="{data}">
                            <vs-tr :key="indextr" v-for="(tr, indextr) in data">

                                <vs-td :data="tr.case_no">
                                    {{ tr.case_number }}
                                </vs-td>

                                <vs-td :data="tr.case_name">
                                    {{ tr.case_name }}
                                </vs-td>

                                <vs-td :data="tr.task_name">
                                    {{ tr.task_name }}
                                </vs-td>

                                <vs-td :data="tr.status">
                                    {{ tr.task_status }}
                                </vs-td>

                                <vs-td :data="tr.date_start">
                                    {{ formatDate(tr.date_start) }}
                                </vs-td>

                                <vs-td :data="tr.date_due">
                                    {{ formatDate(tr.date_due) }}
                                </vs-td>

                                <vs-td :data="tr.date_due" v-if="showAvailableTime">
                                    <span :class="{'text-danger': isDateDuePassed(tr.date_due), 'text-success': !isDateDuePassed(tr.date_due)}">
                                        {{ availableTime(tr.date_due) }}
                                    </span>
                                </vs-td>

                            </vs-tr>
                        </template>
                    </vs-table>
                </vs-popup>
            </div>
        </vx-card>
    </div>
</template>

<style>
    .vs-con-table .vs-con-tbody {
        border: none;
    }
</style>

<script>
    export default {
        data() {
            return {
                incidents: [],
                popupActive: false,
                popup_title: '',
                status: '',
                filteredCases: [],
                actionTimeText: '',
                isExceed: false,
                showAvailableTime: false,
            };
        },
        components: {},
        mounted() {
            const vm = this;
            vm.fetchIncident();

            setInterval(() => {
                vm.fetchIncident();
            }, 300000) // update interval 5min
        },
        computed: {
            formatDate() {
                return function(date) {
                    const d = new Date(date);
                    const day = d.getDate().toString().padStart(2, '0');
                    const month = (d.getMonth() + 1).toString().padStart(2, '0');
                    const year = d.getFullYear();
                    let hours = d.getHours() % 12 || 12;
                    const minutes = d.getMinutes().toString().padStart(2, '0');
                    const seconds = d.getSeconds().toString().padStart(2, '0');
                    const ampm = d.getHours() >= 12 ? 'PM' : 'AM';

                    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds} ${ampm}`;
                }
            },
            availableTime() {
                return function(date_due) {
                    let diffTime = Math.abs(new Date(date_due) - new Date());
                    let diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
                    diffTime -= diffDays * (1000 * 60 * 60 * 24);

                    let diffHours = Math.floor(diffTime / (1000 * 60 * 60));
                    diffTime -= diffHours * (1000 * 60 * 60);

                    let diffMinutes = Math.floor(diffTime / (1000 * 60));
                    diffTime -= diffMinutes * (1000 * 60);

                    let diffSeconds = Math.floor(diffTime / (1000));

                    // Pad hours, minutes and seconds with leading zeros if needed
                    diffHours = diffHours.toString().padStart(2, '0');
                    diffMinutes = diffMinutes.toString().padStart(2, '0');
                    diffSeconds = diffSeconds.toString().padStart(2, '0');

                    // If diffDays is greater than 0, show days, else show only time
                    return diffDays > 0 ? `${diffDays} Days ${diffHours}:${diffMinutes}:${diffSeconds}` : `${diffHours}:${diffMinutes}:${diffSeconds}`;
                }
            },
            isDateDuePassed() {
                return function(date_due) {
                    return new Date(date_due) < new Date();
                }
            }
        },
        methods: {
            fetchIncident: function () {
                let url = '/api/crm/incident-dashboard';
                this.$vs.loading({
                    container: '#card-crm-performance',
                    scale: 0.6
                });
                fetch(url)
                    .then(res => res.json())
                    .then(res => {
                        this.incidents = res;
                        this.$vs.loading.close('#card-crm-performance > .con-vs-loading');
                    })
                    .catch(err => console.log(err));

            },

            async openDetailPopup(title, code, status) {
                // Format status text: 'in_progress' -> 'In Progress', 'within_sla' -> 'Within SLA', etc.
                const formatStatus = (status) => {
                    return status
                        .split('_')
                        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                        .join(' ');
                };
                
                this.popup_title = `${title} - ${formatStatus(status)}`;
                await this.fetchCrmData(code, status);
                this.popupActive = true;
                this.showAvailableTime = status === 'in_progress';
            },

            fetchCrmData: function (code, status) {
                return new Promise((resolve, reject) => {
                    let api_url = 'api/crm/incident-list';
                    this.$vs.loading({
                        container: '#card-crm-performance',
                        scale: 0.6
                    });
                    fetch(api_url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ code, status }),
                    })
                    .then(res => res.json())
                    .then(res => {
                        this.$vs.loading.close('#card-crm-performance > .con-vs-loading');
                        this.filteredCases = res
                        resolve();
                    })
                    .catch(err => {
                        console.log(err);
                        this.$vs.loading.close('#card-crm-performance > .con-vs-loading');
                        reject(err);
                    });
                });
            },

        },
    };
</script>

