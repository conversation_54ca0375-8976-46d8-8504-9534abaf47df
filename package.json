{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@chenfengyuan/vue-countdown": "^1.1.2", "@fullhuman/postcss-purgecss": "^1.2.0", "@vue/cli-plugin-babel": "^3.7.0", "@vue/cli-plugin-eslint": "^3.7.0", "@vue/cli-service": "^3.7.0", "ag-grid-community": "^21.0.1", "ag-grid-vue": "^21.0.1", "algoliasearch": "^3.33.0", "apexcharts": "^3.6.12", "auth0-js": "^9.10.4", "axios": "^0.19.0", "axios-mock-adapter": "^1.17.0", "chart.js": "^2.8.0", "core-js": "2.6.5", "cross-env": "^5.1", "echarts": "^4.2.1", "file-saver": "2.0.1", "firebase": "^6.0.4", "instantsearch.css": "^7.3.1", "jsonwebtoken": "^8.5.1", "laravel-mix": "^4.0.7", "lodash": "^4.17.20", "material-icons": "^0.3.1", "node-sass": "^4.14.1", "perfect-scrollbar": "^1.4.0", "postcss-rtl": "^1.5.0", "prismjs": "^1.16.0", "purgecss": "^1.3.0", "resolve-url-loader": "^2.3.1", "sass": "^1.15.2", "sass-loader": "^7.1.0", "script-loader": "0.7.2", "tailwindcss": "^1.0.1", "vee-validate": "^2.2.8", "vue": "^2.6.10", "vue-acl": "4.0.7", "vue-apexcharts": "^1.3.5", "vue-awesome-swiper": "^3.1.3", "vue-backtotop": "^1.6.1", "vue-clipboard2": "^0.3.0", "vue-context": "^4.0.0", "vue-echarts": "^4.0.3", "vue-feather-icons": "^5.0.0", "vue-flatpickr-component": "^8.1.2", "vue-form-wizard": "^0.8.4", "vue-fullcalendar": "^1.0.9", "vue-i18n": "^8.11.2", "vue-instantsearch": "^2.2.1", "vue-perfect-scrollbar": "^0.1.0", "vue-prism-component": "^1.1.1", "vue-property-decorator": "^8.1.1", "vue-quill-editor": "^3.0.6", "vue-router": "^3.0.6", "vue-select": "^3.1.0", "vue-simple-calendar": "^4.2.2", "vue-simple-suggest": "^1.9.5", "vue-star-rating": "^1.6.1", "vue-template-compiler": "^2.6.10", "vue-tour": "^1.1.0", "vue-tree-halower": "^1.8.0", "vue-video-player": "^5.0.2", "vue2-google-maps": "^0.10.6", "vue2-hammer": "^2.1.2", "vuecode.js": "0.0.27", "vuedraggable": "^2.21.0", "vuejs-datepicker": "^1.5.4", "vuepress": "0.14.11", "vuesax": "3.11.1", "vuex": "^3.1.1", "xlsx": "^0.15.0"}, "dependencies": {"@websanova/vue-auth": "^2.21.14-beta", "jspdf": "^1.5.3", "jspdf-autotable": "^3.2.11", "vue-axios": "^2.1.4", "vue-date-fns": "^1.1.0", "vue-excel-editor": "^1.3.79", "vue-moment": "^4.1.0"}}