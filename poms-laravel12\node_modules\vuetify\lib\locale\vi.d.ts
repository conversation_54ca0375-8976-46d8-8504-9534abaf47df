declare const _default: {
    badge: string;
    open: string;
    close: string;
    dismiss: string;
    confirmEdit: {
        ok: string;
        cancel: string;
    };
    dataIterator: {
        noResultsText: string;
        loadingText: string;
    };
    dataTable: {
        itemsPerPageText: string;
        ariaLabel: {
            sortDescending: string;
            sortAscending: string;
            sortNone: string;
            activateNone: string;
            activateDescending: string;
            activateAscending: string;
        };
        sortBy: string;
    };
    dataFooter: {
        itemsPerPageText: string;
        itemsPerPageAll: string;
        nextPage: string;
        prevPage: string;
        firstPage: string;
        lastPage: string;
        pageText: string;
    };
    dateRangeInput: {
        divider: string;
    };
    datePicker: {
        itemsSelected: string;
        range: {
            title: string;
            header: string;
        };
        title: string;
        header: string;
        input: {
            placeholder: string;
        };
    };
    noDataText: string;
    carousel: {
        prev: string;
        next: string;
        ariaLabel: {
            delimiter: string;
        };
    };
    calendar: {
        moreEvents: string;
        today: string;
    };
    input: {
        clear: string;
        prependAction: string;
        appendAction: string;
        otp: string;
    };
    fileInput: {
        counter: string;
        counterSize: string;
    };
    fileUpload: {
        title: string;
        divider: string;
        browse: string;
    };
    timePicker: {
        am: string;
        pm: string;
        title: string;
    };
    pagination: {
        ariaLabel: {
            root: string;
            next: string;
            previous: string;
            page: string;
            currentPage: string;
            first: string;
            last: string;
        };
    };
    stepper: {
        next: string;
        prev: string;
    };
    rating: {
        ariaLabel: {
            item: string;
        };
    };
    loading: string;
    infiniteScroll: {
        loadMore: string;
        empty: string;
    };
    rules: {
        required: string;
        email: string;
        number: string;
        integer: string;
        capital: string;
        maxLength: string;
        minLength: string;
        strictLength: string;
        exclude: string;
        notEmpty: string;
        pattern: string;
    };
};
export default _default;
