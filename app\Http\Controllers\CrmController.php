<?php

namespace App\Http\Controllers;

use App\Http\Resources\CrmPerformanceResources;
use App\Services\CRMService;
use Carbon\Carbon;
use DateTime;
use GuzzleHttp\Client;
use Goutte\Client as GClient;

class CrmController extends Controller
{

    public static function crmService()
    {
        return new CRMService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getIncidentStats()
    {
        return $this->dashboardCRMIncident();
    }

    public function dashboardCRMIncident()
    {

        $countItIncidentPendAck = 0;
        $countItIncidentAck = 0;
        $countItSpecPendingAck = 0;
        $countItSpecAck = 0;
        $countApprover = 0;
        $countSpecAfterApprover = 0;

        $countItIncidentPendAckExceed = 0;
        $countItIncidentAckExceed = 0;
        $countItSpecPendingAckExceed = 0;
        $countItSpecAckExceed = 0;
        $countApproverExceed = 0;
        $countSpecAfterApproverExceed = 0;

        $current = Carbon::now();

        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $INITIAL_TASK = array('Initial Task', 'Initial Task - IT Incident', 'Initial Task : IT Incident');
        $APPROVER_TASK = 'Assigned to Approver';
        $FLAG_SPECIALIST = array(2, 3);
        $FLAG_SPECIALIS_AFTER_APPROVER = 4;
        $LIST_GROUP_SPECIALIST = array('Assigned to Archisoft Team',
            'Assigned to Archissoft Build Team',
            'Assigned to Group Archisoft',
            'Assigned to Group Archisoft Build Team',
            'Assigned to Group IT Specialist(Database Admin)',
            'Assigned to Group IT Specialist(Database Management)',
            'Assigned to Group IT Specialist(Developer)',
            'Assigned to Group IT Specialist(Network Admin)',
            'Assigned to Group IT Specialist(Production Support)',
            'Assigned to Group IT Specialist(Security)',
            'Assigned to Group IT Specialist(Server Admin)',
            'Assigned to Group Middleware');

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Status</th>
                        <th>Not Exceed</th>
                        <th>Exceed</th>
                    </tr>
                </thead>
                <tbody>";

        $listTasks = self::crmService()->getDashboardCRM();

        foreach ($listTasks as $tasks) {

            $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if (in_array($tasks->taskname, $INITIAL_TASK)) {

                if ($tasks->status == $PENDING_ACK && $tasks->acknowledgetime == '') {
                    if ($datedue > $current) {
                        $countItIncidentPendAck++;
                    } else {
                        $countItIncidentPendAckExceed++;
                    }
                }
                if ($tasks->status == $ACKNOWLEDGE && $tasks->acknowledgetime != '') {
                    if ($acknowledgetime <= $datedue) {
                        $countItIncidentAck++;
                    } else {
                        $countItIncidentAckExceed++;
                    }
                }
            }

//
            if ($tasks->taskname == $APPROVER_TASK) {
                if ($datedue > $current) {
                    $countApprover++;
                } else {
                    $countApproverExceed++;
                }
            }
            if ($tasks->flag == $FLAG_SPECIALIS_AFTER_APPROVER) {
                if ($datedue > $current) {
                    $countSpecAfterApprover++;
                } else {
                    $countSpecAfterApproverExceed++;
                }
            }
        }

        $listTasksSpecialist = self::crmService()->getDashboardCRMSpecialist();

        foreach ($listTasksSpecialist as $tasks) {
            $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if (in_array($tasks->taskname, $LIST_GROUP_SPECIALIST) && in_array($tasks->flag, $FLAG_SPECIALIST)) {
                if ($tasks->status == $PENDING_ACK) {
                    if ($datedue > $current) {
                        $countItSpecPendingAck++;
                    } else {
                        $countItSpecPendingAckExceed++;
                    }

                } else {
                    if ($datedue > $current) {
                        $countItSpecAck++;
                    } else {
                        $countItSpecAckExceed++;
                    }
                }
            }
        }

        return response()->json(
            array(
                0 =>
                    array(
                        'incidentType' => 'Pending Acknowledgement IT Coordinator',
                        'notExceed' => $countItIncidentPendAck,
                        'exceed' => $countItIncidentPendAckExceed,
                        'data-url' => '',
                    ),
                1 =>
                    array(
                        'incidentType' => 'Acknowledged for IT Coordinator',
                        'notExceed' => $countItIncidentAck,
                        'exceed' => $countItIncidentAckExceed,
                        'data-url' => '',
                    ),
                2 =>
                    array(
                        'incidentType' => 'Pending Acknowledgement for IT Specialist',
                        'notExceed' => $countItSpecPendingAck,
                        'exceed' => $countItSpecPendingAckExceed,
                        'data-url' => '',
                    ),
                3 =>
                    array(
                        'incidentType' => 'S1',
                        'notExceed' => 0,
                        'exceed' => 0,
                        'data-url' => '',
                    ),
                4 =>
                    array(
                        'incidentType' => 'S2',
                        'notExceed' => 0,
                        'exceed' => 0,
                        'data-url' => '',
                    ),
                5 =>
                    array(
                        'incidentType' => 'S3',
                        'notExceed' => 0,
                        'exceed' => 0,
                        'data-url' => '',
                    ),
            )
        );
    }

    /* START CS SLA COUNT */
    protected function csPerformanceDashboard()
    {
        /* Disable Phone in Dashboard 2025-06-13 */

        $countPortal = 0;
        $countEmail = 0;
        // $countFax = 0;
        // $countPhone = 0;
        $countLetter = 0;

        $countBreachPortal = 0;
        $countBreachEmail = 0;
        // $countBreachFax = 0;
        // $countBreachPhone = 0;
        $countBreachLetter = 0;

        /* Set threshold SLA time in milliseconds */
        $thresholdSlaPortal = 900;
        $thresholdSlaEmail = 900;
        // $thresholdSlaFax = 900;
        // $thresholdSlaPhone = 900;
        $thresholdSlaLetter = 900;

        $countDate = Carbon::now()->format('Y-m-d');

        $listCsPerformance = self::crmService()->getCsPerformanceSlaList($countDate);
        foreach ($listCsPerformance as $data) {
            if ($data->contact_mode == 'Open Portal') {
                $countPortal++;
                if ($data->cs_actual_duration > $thresholdSlaPortal) $countBreachPortal++;
            }
            if ($data->contact_mode == 'Email') {
                $countEmail++;
                if ($data->cs_actual_duration > $thresholdSlaEmail) $countBreachEmail++;
            }
            // if ($data->contact_mode == 'Fax') {
            //     $countFax++;
            //     if ($data->cs_actual_duration > $thresholdSlaFax) $countBreachFax++;
            // }
            // if ($data->contact_mode == 'Call-in') {
            //     $countPhone++;
            //     if ($data->cs_actual_duration > $thresholdSlaPhone) $countBreachPhone++;
            // }
            if ($data->contact_mode == 'Letter Correspondence') {
                $countLetter++;
                if ($data->cs_actual_duration > $thresholdSlaLetter) $countBreachLetter++;
            }
        }

        $percentagePortal = 100;
        $percentageEmail = 100;
        // $percentageFax = 100;
        $percentagePhone = 100;
        $percentageLetter = 100;

        if ($countPortal > 0) {
            $percentagePortal = round((($countPortal - $countBreachPortal) / $countPortal) * 100, 2);
        }
        if ($countEmail > 0) {
            $percentageEmail = round((($countEmail - $countBreachEmail) / $countEmail) * 100, 2);
        }
        // if ($countFax > 0) {
        //     $percentageFax = round((($countFax - $countBreachFax) / $countFax) * 100, 2);
        // }
        // if ($countPhone > 0) {
        //     $percentagePhone = round((($countPhone - $countBreachPhone) / $countPhone) * 100, 2);
        // }
        if ($countLetter > 0) {
            $percentageLetter = round((($countLetter - $countBreachLetter) / $countLetter) * 100, 2);
        }

        return response()->json(
            array(
                'data' => [
                    'portal' => array(
                        'name' => 'Online (eAduan)',
                        'total' => $countPortal,
                        'breach' => $countBreachPortal,
                        'ratio' => $percentagePortal
                    ),
                    'email' => array(
                        'name' => 'Email',
                        'total' => $countEmail,
                        'breach' => $countBreachEmail,
                        'ratio' => $percentageEmail
                    ),
                    // 'fax' => array(
                    //     'name' => 'Fax',
                    //     'total' => $countFax,
                    //     'breach' => $countBreachFax,
                    //     'ratio' => $percentageFax
                    // ),
                    // 'phone' => array(
                    //     'name' => 'Phone',
                    //     'total' => $countPhone,
                    //     'breach' => $countBreachPhone,
                    //     'ratio' => $percentagePhone
                    // ),
                    'letter' => array(
                        'name' => 'Letter',
                        'total' => $countLetter,
                        'breach' => $countBreachLetter,
                        'ratio' => $percentageLetter
                    ),
                ]
            )
        );

        //return CrmPerformanceResources::collection($parameters);

    }

    public function getITCoordinatorCasesList() {
        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $INITIAL_TASK = array('Initial Task', 'Initial Task - IT Incident', 'Initial Task : IT Incident');

        $list = self::crmService()->getDashboardCRM();

        $cases = array();
        $allITCoordTaskList = array();

        $countPending = 0;
        $countPendingExceed = 0;
        $countAcknowledge = 0;
        $countAcknowledgeExceed = 0;

        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();
            $dateDiff = $current->diff(new DateTime($datedue));
            $timeTaken = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            $caseObj = (object)array();

            if(in_array($value->taskname, $INITIAL_TASK) && ($value->status == $PENDING_ACK || $value->status == $ACKNOWLEDGE)) {
                $caseObj->case_no = $value->caseno;
                $caseObj->status = $value->status;
                $caseObj->date_start = $datestart;
                $caseObj->date_due = $datedue;
                $caseObj->time_remaining = $timeTaken;

                if ($datedue > $current && $value->acknowledgetime == '') {
                    $caseObj->case_exceed = 'NO';
                    $countPending++;
                }

                if ($value->acknowledgetime == '' && $datedue <= $current) {
                    $caseObj->case_exceed = 'YES';
                    $countPendingExceed++;
                }

                if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '') {
                    $caseObj->case_exceed = 'NO';
                    $countAcknowledge++;
                }

                if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue) {
                    $caseObj->case_exceed = 'YES';
                    $countAcknowledgeExceed++;
                }

                array_push($allITCoordTaskList, $caseObj);
            }
        }

        array_push($cases, array(
                'total_pending' => $countPending,
                'total_pending_exceed' => $countPendingExceed,
                'total_acknowledge' => $countAcknowledge,
                'total_acknowledge_exceed' => $countAcknowledgeExceed,
                'all_cases' => $allITCoordTaskList
            )
        );

        return response()->json(
            array(
                'data' => $cases
            )
        );

    }

    public function getITSpecialistCasesList() {
        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $INITIAL_TASK = array('Initial Task', 'Initial Task - IT Incident', 'Initial Task : IT Incident');
        $FLAG_SPECIALIST = array(2, 3);
        $LIST_GROUP_SPECIALIST = array('Assigned to Archisoft Team',
            'Assigned to Archissoft Build Team',
            'Assigned to Group Archisoft',
            'Assigned to Group Archisoft Build Team',
            'Assigned to Group IT Specialist(Database Admin)',
            'Assigned to Group IT Specialist(Database Management)',
            'Assigned to Group IT Specialist(Developer)',
            'Assigned to Group IT Specialist(Network Admin)',
            'Assigned to Group IT Specialist(Production Support)',
            'Assigned to Group IT Specialist(Security)',
            'Assigned to Group IT Specialist(Server Admin)',
            'Assigned to Group Middleware');

        $list = self::crmService()->getDashboardCRMSpecialist();

        $cases = array();
        $pendingAckITSpecList = array();
        $pendingAckITSpecListExceed = array();

        foreach ($list as $value) {
            $current = Carbon::now();
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $taskdue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            $caseObj = (object)array();

            if ($taskdue > $current && $value->status == $PENDING_ACK && in_array($value->taskname, $LIST_GROUP_SPECIALIST) && in_array($value->flag, $FLAG_SPECIALIST)) {
                $caseObj->case_no = $value->caseno;
                $caseObj->status = $value->status;
                $caseObj->date_start = $datestart;
                $caseObj->date_due = $datedue;
                $caseObj->acknowledge_time = $acknowledgeTime;

                array_push($pendingAckITSpecList, $caseObj);
            }

            if ($taskdue < $current && $value->status == $PENDING_ACK && in_array($value->taskname, $LIST_GROUP_SPECIALIST) && in_array($value->flag, $FLAG_SPECIALIST)) {
                $caseObj->case_no = $value->caseno;
                $caseObj->status = $value->status;
                $caseObj->date_start = $datestart;
                $caseObj->date_due = $datedue;
                $caseObj->acknowledge_time = $acknowledgeTime;

                array_push($pendingAckITSpecListExceed, $caseObj);
            }
        }

        array_push($cases, array(
                'pending_acknowledge_itspec' => $pendingAckITSpecList,
                'pending_acknowledge_itspec_exceed' => $pendingAckITSpecListExceed
            )
        );

        return response()->json(
            array(
                'data' => $cases
            )
        );
    }
}
