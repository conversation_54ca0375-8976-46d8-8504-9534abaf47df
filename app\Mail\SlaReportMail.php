<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\SlaReport;

class SlaReportMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $report;
    public $month;
    public $year;

    /**
     * Create a new message instance.
     * 
     * @param SlaReport $report
     * @param int $year
     * @param int $month
     * @return void
     */
    public function __construct(SlaReport $report, $year, $month)
    {
        $this->report = $report;
        $this->year = $year;
        $this->month = $month;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $monthName = date('F', mktime(0, 0, 0, $this->month, 1));
        $subject = "POMS: SLA Report for {$monthName} {$this->year}";
        
        $pdfPath = storage_path("report/{$this->report->file_name}.pdf");
        
        // Ensure the view path is correct and case-sensitive
        $viewPath = 'emails.sla-report';
        
        if (!view()->exists($viewPath)) {
            throw new \RuntimeException("View [{$viewPath}] not found.");
        }
        
        return $this->from('<EMAIL>', 'POMS Administrator')
                    ->subject($subject)
                    ->view($viewPath)
                    ->attach($pdfPath, [
                        'as' => "SLA_Report_{$monthName}_{$this->year}_[AUTO].pdf",
                        'mime' => 'application/pdf',
                    ]);
    }
}
