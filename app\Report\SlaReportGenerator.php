<?php


namespace App\Report;

use App\Nagios\MigrateUtils;
use App\SlaReport;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB as DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class SlaReportGenerator
{

    public static function runCheckReportQueue()
    {
        $queueDateTime = Carbon::now();

        $logsStart = self::class . ' Start check report queue at ' . $queueDateTime;
        Log::debug($logsStart);
        dump($logsStart);

        $reportQueueList = SlaReport::where('deleted', 0)
            ->where('status', 'queue')
            ->orderBy('created_at', 'desc')->get();

        if ($reportQueueList) {
            foreach ($reportQueueList as $report) {
                dump($report->id);
                $param = json_decode($report->parameter);
                dump($param);
                self::generateSlaReport($param, $report);
            }
        }

        $logsEnd = self::class . ' Process completed. Start: ' . $queueDateTime . ' -> ' . ' End: ' . Carbon::now() . ', Completed --- Taken Time: ' .
            json_encode(['Time' => MigrateUtils::getTakenTime($queueDateTime)]);
        Log::info($logsEnd);
        dump($logsEnd);
    }

    private static function generateSlaReport($param, $report)
    {
        $startProcessTime = Carbon::now();

        $report->file_name = (string)Str::uuid();
        $report->status = 'processing';
        $report->updated_at = Carbon::now();
        $report->save();

        // EXECUTE REPORT PROCESS

        try {
            self::getSLAReportData($param, $report);

            $logs = json_encode([
                'Start' => $startProcessTime,
                'End' => Carbon::now(),
                'Time' => MigrateUtils::getTakenTime($startProcessTime)
            ]);

            $report->log = $logs;
            $report->status = 'completed';
            $report->updated_at = Carbon::now();
            $report->save();
        } catch (Throwable $e) {
            $report->status = 'failed';
            $report->log = $e->getMessage() . ' - ' . $e->getLine() . ' - ' . $e->getFile() . ' - ' . $e->getTraceAsString();
            $report->updated_at = Carbon::now();
            $report->save();
        }
    }

    private static function storePDF($param, $report)
    {
        dump($report);
        Log::info('START creating PDF... ' . Carbon::now());

        $pdf = \PDF::loadView('reports.slaReportPdf', compact('param'));
        $path = storage_path('report');
        $fileName =  $report->file_name . '.' . 'pdf';
        $pdf->save($path . '/' . $fileName);

        Log::info('END creating PDF... ' . Carbon::now());
    }

    private static function getItServiceReport($pYear, $pMonth)
    {
        $query = "SELECT CONCAT(ticket_number, ' - ' , request_subject) AS title,
        expected_duration AS sla,
        actual_duration AS actual,
        10.00 AS pen_rate,
        (ROUND(actual_duration - expected_duration, 2)) AS exceed_hour
        FROM sla_itservice_request
        WHERE YEAR(expected_completed_date) = $pYear
        AND MONTH(expected_completed_date) = $pMonth
        AND actual_duration > expected_duration";

        $querySummary = "SELECT COUNT(*) AS total,
        (SELECT COUNT(*) FROM sla_itservice_request
        WHERE YEAR(expected_completed_date) = $pYear
        AND MONTH(expected_completed_date) = $pMonth
        AND actual_duration > expected_duration) AS exceed
        FROM sla_itservice_request
        WHERE YEAR(expected_completed_date) = $pYear
        AND MONTH(expected_completed_date) = $pMonth";

        $exceedList = DB::connection('mysql')->select($query);
        $summary = DB::connection('mysql')->select($querySummary)[0];

        $percentWithinSLA = 100;
        $taskPenaltyAmount = 0;
        $totalPenaltyAmount = 0;
        $totalNonComplyHour = 0;

        $resItServiceSLA = (object)array();
        $resItServiceSLA->exceed_task_list = null;
        $resItServiceSLA->total_penalty_amount = null;
        $resItServiceSLA->total_non_comply = null;

        if ($exceedList) {
            foreach ($exceedList as $exceed) {
                if ($exceed->exceed_hour > 24) {
                    $normal_rate = 240;
                    $subsequent_day = $exceed->exceed_hour - 24;
                    $total_day = $subsequent_day / 24;
                    $subsequent_rate = ceil($total_day) * 50;
                    $taskPenaltyAmount = $normal_rate + $subsequent_rate;
                } else {
                    $taskPenaltyAmount = $exceed->exceed_hour * 10;
                }

                $totalPenaltyAmount += $taskPenaltyAmount;
                $totalNonComplyHour += $exceed->exceed_hour;

                $exceed->pen_amount = $taskPenaltyAmount;
            }
            $resItServiceSLA->exceed_task_list = $exceedList;
            $resItServiceSLA->total_penalty_amount = $totalPenaltyAmount;
            $resItServiceSLA->total_non_comply = $totalNonComplyHour;
        }

        if ($summary) {
            if ($summary->total > 0) $percentWithinSLA = (($summary->total - $summary->exceed) / $summary->total) * 100;
        }

        $resItServiceSLA->within_sla_percent = $percentWithinSLA;

        return $resItServiceSLA;
    }

    private static function getSLAReportData($param, $report)
    {
        $currentDate = Carbon::now()->format('d/m/Y');
        $p_year = $param->year;
        $p_month = $param->month;

        $it_ack_duration = 900;
        $cs_ack_duration = 900;
        $itspec_rit_duration = 14400;
        $performance_proxies_limit = 3000;
        $cs_call_in_duration = 900;
        $cs_letter_duration = 172800;
        $cs_online_duration = 900;
        $cs_email_duration = 900;
        $treshold_sla_proxies = 100;
        $treshold_sla_call_answer_rate = 80;
        $treshold_sla_call_abandoned_rate = 10;
        $r_system_performance_proxies_penality = 1000;
        $r_itcoord_penality = 10.00;
        $r_itspec_penality = 10.00;
        $r_csm_penality = 100;
        $r_sec_dataintg_penality = 10000;
        $r_penality_10 = 10.00;
        // $r_sys_avalability=1000;
        $r_sys_avalability = 10;
        // service availibility
        $s_portal = 18;
        $s_bpm = 20;
        $s_database = 7;
        $s_web = 10;
        $s_network = 1;
        $s_sso = 14;
        $s_solr = 2;
        $nagios_interval = 10;
        $s1_exceed_sla = 86400;
        $s2_exceed_sla = 259200;
        $s3_exceed_sla = 432000;

        $monthName = date('F', mktime(0, 0, 0, $p_month, 10)); // March

        //------- Total It_Coord
        $queryITCoordWithinSLA = "SELECT COUNT(1) AS count_itcoord_within_sla FROM sla_itcoord
            WHERE MONTH(itcoord_case_created)=$p_month AND YEAR(itcoord_case_created)=$p_year
            AND DATE(itcoord_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itcoord_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itcoord_actual_duration <= 900";
        $queryITCoordExceedSLA = "SELECT COUNT(1) AS count_itcoord_exceed_sla FROM sla_itcoord
            WHERE MONTH(itcoord_case_created)=$p_month AND YEAR(itcoord_case_created)=$p_year
            AND DATE(itcoord_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itcoord_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itcoord_actual_duration > $it_ack_duration";

        //details integrity
        $queryIntegrityExceedDetails = "SELECT CONCAT(case_number, ' - ' , SUBJECT) AS integrity_name,
            (SELECT '100') AS integrity_threshold,
            (SELECT '0') AS integrity_score,
            (SELECT '100') AS integrity_diff,
            (SELECT '10000.00') AS pen_rate,
            (SELECT '10000.00') AS pen_amt
            FROM sla_internal_fac WHERE MONTH(case_created)=$p_month  AND YEAR(case_created)=$p_year";

        //details itcoord
        $queryITCoordExceedDetails = "SELECT CONCAT(case_number, ' - ' , itcoord_name) AS itcoord_name,  ROUND((SELECT itcoord_available_duration/60),2) AS itcoord_available_duration, ROUND((SELECT itcoord_actual_duration/60),2) AS itcoord_actual_duration,
            ROUND(((SELECT  itcoord_actual_duration - itcoord_available_duration)/60),2) AS itcoord_diff, (SELECT '10.00') AS pen_rate, ROUND((SELECT itcoord_diff/60) * 10,2) AS pen_amt
            FROM sla_itcoord WHERE MONTH(itcoord_case_created)=$p_month  AND YEAR(itcoord_case_created)=$p_year
            AND DATE(itcoord_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itcoord_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itcoord_actual_duration >$it_ack_duration";

        //DATA INTEGRITY TOTAL
        $queryIntegrityTotHr = "SELECT COUNT(case_number) AS total_cases
            FROM sla_internal_fac WHERE MONTH(case_created)=$p_month  AND YEAR(case_created)=$p_year";

        //It Coord Total Hr & PenAmt
        $queryITCoordTotHr = "SELECT (SELECT ROUND((SUM(itcoord_actual_duration - itcoord_available_duration)/60/60),3) FROM sla_itcoord
            WHERE MONTH(itcoord_case_created) = $p_month AND  YEAR(itcoord_case_created)=$p_year
            AND DATE(itcoord_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itcoord_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itcoord_actual_duration > $it_ack_duration)
            AS ITCoordTotalHr";
        $queryITCoordTotPenAmt = "SELECT (SELECT ROUND((SUM(itcoord_actual_duration - itcoord_available_duration)/60/60) * 10,2) FROM sla_itcoord
            WHERE MONTH(itcoord_case_created) = $p_month AND YEAR(itcoord_case_created)=$p_year
            AND DATE(itcoord_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itcoord_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itcoord_actual_duration > $it_ack_duration)
            AS ITCoordTotalPenAmt";
        $queryITCoordTotalDuration = "SELECT SUM(itcoord_actual_duration) AS itcoord_total_time FROM sla_itcoord
            WHERE MONTH(itcoord_case_created)=$p_month AND YEAR(itcoord_case_created)=$p_year
            AND DATE(itcoord_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itcoord_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))";

        //--------Total It_Service
        $queryITServExceedSLA = "SELECT
            (SELECT COUNT(*) FROM sla_itserv WHERE MONTH(itserv_insert_data_datetime) = $p_month AND YEAR(itserv_insert_data_datetime)=$p_year AND itserv_actual_duration > itserv_available_duration)
            AS ITServExceedSLA;";
        //-------details IT Service
        $queryITServExceedDetails = "SELECT CONCAT(case_number, ' - ' , itserv_name) AS itserv_name, itserv_available_duration, itserv_actual_duration,
            (SELECT  itserv_actual_duration - itserv_available_duration) AS itserv_diff, (SELECT '10.00') AS PEN_RATE, (SELECT itserv_diff * 10) AS pen_amt
             FROM sla_itserv WHERE (SELECT  itserv_available_duration - itserv_actual_duration) < 0 AND MONTH(itserv_insert_data_datetime) = $p_month AND YEAR(itserv_insert_data_datetime)=$p_year;";

        //details security_integrity
        $querySecurityIntegrityDetails = "SELECT CONCAT(case_number, ' - ' , SUBJECT) AS itcoord_name,
            (SELECT '0') AS SLA, (SELECT '0') AS SCORE, (SELECT '0') AS DIFF, (SELECT '1') AS SLA, (SELECT $r_system_performance_proxies_penality) AS PEN_AMT
            FROM sla_internal_fac
            WHERE MONTH(action_date_by_top) = $p_month  AND YEAR(action_date_by_top) = $p_year;";

        // summary S1

        $queryITSpecS1SLA = "SELECT
            (SELECT COUNT(*) FROM sla_itseverity WHERE MONTH(itseverity_case_created) = $p_month  AND YEAR(itseverity_case_created) = $p_year
            AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itseverity_sla_flag = 's1' AND itseverity_available_duration  > itseverity_actual_duration ) AS c_s1_within_sla,
            (SELECT COUNT(*) FROM sla_itseverity WHERE MONTH(itseverity_case_created) = $p_month AND YEAR(itseverity_case_created) = $p_year
            AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itseverity_sla_flag = 's1' AND itseverity_available_duration  < itseverity_actual_duration ) AS c_s1_exceed_sla,
            (SELECT (c_s1_within_sla) / (c_s1_within_sla + c_s1_exceed_sla) * 100) AS p_s1_within_sla,
            (SELECT (100 - p_s1_within_sla)) AS p_s1_exceed_sla";

        // detail S1

        $queryS1ExceedDetails = "SELECT CONCAT(case_number, ' - ' , itseverity_name) AS itseverity_name,
            1 AS itseverity_available_durations,
            DATEDIFF(itseverity_completed_datetime, itseverity_actual_start_datetime) AS itseverity_actual_durations,
            (SELECT itseverity_actual_durations - itseverity_available_durations) AS itseverity_diff,
            (SELECT itseverity_diff * 10 * 24) AS pen_amt
            FROM sla_itseverity WHERE MONTH(itseverity_case_created)=$p_month  AND YEAR(itseverity_case_created)=$p_year
            AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itseverity_sla_flag = 's1'
            AND DATEDIFF(itseverity_completed_datetime, itseverity_actual_start_datetime) > 1";

        //S1 Total Hr & PenAmt
        $queryS1TotHr = "SELECT (SELECT ROUND((SUM(itseverity_actual_duration - itseverity_available_duration)/60/60),2) FROM sla_itseverity
            WHERE MONTH(itseverity_case_created) = $p_month AND YEAR(itseverity_case_created) = $p_year
            AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itseverity_sla_flag = 's1'
            AND itseverity_actual_duration > $s1_exceed_sla) AS S1TotalHr";
        $queryS1TotPenAmt = "SELECT (SELECT ROUND((SUM(itseverity_actual_duration - itseverity_available_duration)/60/60) * 10,2) FROM sla_itseverity
            WHERE MONTH(itseverity_case_created) = $p_month AND  YEAR(itseverity_case_created)=$p_year
            AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itseverity_sla_flag = 's1'
            AND itseverity_actual_duration > $s1_exceed_sla) AS S1TotalPenAmt";

        // summary S2

        $queryITSpecS2SLA = "SELECT
            (SELECT COUNT(*) FROM sla_itseverity WHERE MONTH(itseverity_case_created) = $p_month  AND YEAR(itseverity_case_created) = $p_year
            AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itseverity_sla_flag = 's2' AND itseverity_available_duration  > itseverity_actual_duration ) AS c_s2_within_sla,
            (SELECT COUNT(*) FROM sla_itseverity WHERE MONTH(itseverity_case_created) = $p_month AND YEAR(itseverity_case_created) = $p_year
            AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itseverity_sla_flag = 's2' AND itseverity_available_duration  < itseverity_actual_duration ) AS c_s2_exceed_sla,
            (SELECT (c_s2_within_sla) / (c_s2_within_sla + c_s2_exceed_sla) * 100) AS p_s2_within_sla,
            (SELECT (100 - p_s2_within_sla)) AS p_s2_exceed_sla";

        //detail s2

        $queryS2ExceedDetails = "SELECT CONCAT(case_number, ' - ' , itseverity_name) AS itseverity_name,
            3 AS itseverity_available_durations,
            DATEDIFF(itseverity_completed_datetime, itseverity_actual_start_datetime) AS itseverity_actual_durations,
            (SELECT itseverity_actual_durations - itseverity_available_durations) AS itseverity_diff,
            (SELECT itseverity_diff * 10 * 24) AS pen_amt
            FROM sla_itseverity
            WHERE MONTH(itseverity_case_created)=$p_month AND YEAR(itseverity_case_created)=$p_year
            AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itseverity_sla_flag = 's2'
            AND DATEDIFF(itseverity_completed_datetime, itseverity_actual_start_datetime) > 3";

        //S2 Total Hr & PenAmt
        $queryS2TotHr = "SELECT (SELECT ROUND((SUM(itseverity_actual_duration - itseverity_available_duration)/60/60),2) FROM sla_itseverity
            WHERE MONTH(itseverity_case_created) = $p_month AND YEAR(itseverity_case_created)=$p_year
            AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itseverity_sla_flag = 's2'
            AND itseverity_actual_duration > $s2_exceed_sla) AS S2TotalHr";
        $queryS2TotPenAmt = "SELECT (SELECT ROUND((SUM(itseverity_actual_duration - itseverity_available_duration)/60/60) * 10,2) FROM sla_itseverity
            WHERE MONTH(itseverity_case_created) = $p_month AND YEAR(itseverity_case_created)=$p_year
            AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itseverity_sla_flag = 's2'
            AND itseverity_actual_duration > $s2_exceed_sla) AS S2TotalPenAmt";

        // summary S3
        $queryITSpecS3SLA = "SELECT (SELECT COUNT(*)
            FROM   sla_itseverity
            WHERE  MONTH(itseverity_case_created) = $p_month
               AND YEAR(itseverity_case_created) = $p_year
               AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
               AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
               AND itseverity_sla_flag = 's3'
               AND DATEDIFF(itseverity_completed_datetime,
                   itseverity_actual_start_datetime) <= 5) AS c_s3_within_sla,
            (SELECT COUNT(*)
            FROM   sla_itseverity
            WHERE  MONTH(itseverity_case_created) = $p_month
               AND YEAR(itseverity_case_created) = $p_year
               AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
               AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
               AND itseverity_sla_flag = 's3')             AS c_s3_total,
            (SELECT c_s3_total - c_s3_within_sla)               AS c_s3_exceed_sla,
            (SELECT CASE WHEN c_s3_total = 0 THEN 1 ELSE c_s3_within_sla / c_s3_total END  * 100)         AS p_s3_within_sla,
            (SELECT ( 100 - p_s3_within_sla ))                  AS p_s3_exceed_sla ";

        // detail S3
        $queryS3ExceedDetails = "SELECT CONCAT(case_number, ' - ' , itseverity_name) AS itseverity_name,
            5 AS itseverity_available_durations,
            DATEDIFF(itseverity_completed_datetime, itseverity_actual_start_datetime) AS itseverity_actual_durations,
            (SELECT itseverity_actual_durations - itseverity_available_durations) AS itseverity_diff,
            (SELECT itseverity_diff * 10 * 24) AS pen_amt
            FROM sla_itseverity
            WHERE MONTH(itseverity_case_created)=$p_month AND YEAR(itseverity_case_created)=$p_year
            AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itseverity_sla_flag = 's3'
            AND DATEDIFF(itseverity_completed_datetime, itseverity_actual_start_datetime) > 5";

        //S3 Total Hr & PenAmt
        $queryS3TotHr = "SELECT (SELECT ROUND((SUM(itseverity_actual_duration - itseverity_available_duration)/60/60),2) FROM sla_itseverity
            WHERE MONTH(itseverity_case_created) = $p_month AND YEAR(itseverity_case_created)=$p_year
            AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itseverity_sla_flag = 's3'
            AND itseverity_actual_duration > $s3_exceed_sla) AS S3TotalHr";
        $queryS3TotPenAmt = "SELECT (SELECT ROUND((SUM(itseverity_actual_duration - itseverity_available_duration)/60/60) * 10,2) FROM sla_itseverity
            WHERE MONTH(itseverity_case_created) = $p_month AND YEAR(itseverity_case_created)=$p_year
            AND DATE(itseverity_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itseverity_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itseverity_sla_flag = 's3'
            AND itseverity_actual_duration > $s3_exceed_sla) AS S3TotalPenAmt";



        //------- Total It_Specialist
        $queryITSpecWithinSLA = "SELECT COUNT(1) AS count_itspec_within_sla FROM sla_itspec
            WHERE MONTH(itspec_case_created)=$p_month AND YEAR(itspec_case_created)=$p_year
            AND DATE(itspec_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itcoord_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itspec_actual_duration <= $itspec_rit_duration";
        $queryITSpecExceedSLA = "SELECT COUNT(1) AS count_itspec_exceed_sla FROM sla_itspec
            WHERE MONTH(itspec_case_created)=$p_month AND YEAR(itspec_case_created)=$p_year
            AND DATE(itspec_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itcoord_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itspec_actual_duration > $itspec_rit_duration";

        //details It_Specialist
        $queryITSpecExceedDetails = "SELECT CONCAT(case_number, ' - ' , itspec_name) AS itspec_name,
            ROUND((SELECT itspec_available_duration/60/60),2) AS itspec_available_duration,
            ROUND((SELECT itspec_actual_duration/60/60),2) AS itspec_actual_duration,
            (SELECT '10.00') AS pen_rate,
            ROUND((SELECT  itspec_actual_duration/60/60 - itspec_available_duration/60/60),2) AS itspec_diff,
            ROUND((SELECT itspec_diff * 10.00),2) AS pen_amt
            FROM sla_itspec
            WHERE itspec_actual_duration > $itspec_rit_duration
            AND MONTH(itspec_case_created) = $p_month AND YEAR(itspec_case_created) = $p_year
            AND DATE(itspec_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itcoord_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))";

        //It Spec Total Hr & PenAmt
        $queryITSpecTotHr = "SELECT (SELECT ROUND((SUM(itspec_actual_duration - itspec_available_duration)/60/60),3) FROM sla_itspec
            WHERE MONTH(itspec_case_created) = $p_month AND  YEAR(itspec_case_created)=$p_year
            AND DATE(itspec_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itcoord_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itspec_actual_duration > $itspec_rit_duration) AS ITSpecTotalHr";
        $queryITSpecTotPenAmt = "SELECT (SELECT ROUND((SUM(itspec_actual_duration - itspec_available_duration)/60/60) * 10,2) FROM sla_itspec
            WHERE MONTH(itspec_case_created) = $p_month AND  YEAR(itspec_case_created)=$p_year
            AND DATE(itspec_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itcoord_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND itspec_actual_duration > $itspec_rit_duration) AS ITSpecTotalPenAmt";
        $queryITSpecTotDuration = "SELECT SUM(itspec_actual_duration) AS itspec_total_time FROM sla_itspec
            WHERE MONTH(itspec_case_created)=$p_month AND YEAR(itspec_case_created)=$p_year
            AND DATE(itspec_completed_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))
            AND DATE(itcoord_insert_data_datetime) <= LAST_DAY(CONCAT($p_year,'-',$p_month,'-01'))";


        //S4 IT Spec details
        $queryByApproverSLA = "SELECT CONCAT(case_number, ' - ' , itapprover_name) AS itapprover_name,
            (SELECT DATEDIFF(itapprover_due_datetime, itapprover_start_datetime)) AS itapprover_available_duration,
            (SELECT DATEDIFF(itapprover_completed_datetime, itapprover_actual_start_datetime)) AS itapprover_actual_duration,
            (SELECT '10.00') AS pen_rate,
            (SELECT DATEDIFF(itapprover_completed_datetime, itapprover_actual_start_datetime) - DATEDIFF(itapprover_due_datetime, itapprover_start_datetime)) AS itapprover_diff,
            (SELECT itapprover_diff * 10 * 24) AS pen_amt
            FROM sla_byapprover
            WHERE DATEDIFF(itapprover_completed_datetime, itapprover_actual_start_datetime) > DATEDIFF(itapprover_due_datetime, itapprover_start_datetime)
            AND (redmine_child_parent NOT IN ('CHILD') OR redmine_child_parent IS NULL)
            AND deleted = 0
            AND MONTH(itapprover_completed_datetime) = $p_month
            AND YEAR(itapprover_completed_datetime) = $p_year";

        //S4 average SLA
        $queryByApproverPercentage = " SELECT (SELECT COUNT(*)
            FROM   sla_byapprover
            WHERE  MONTH(itapprover_completed_datetime) = $p_month
                   AND YEAR(itapprover_completed_datetime) = $p_year
                   AND itapprover_sla_flag = '4'
                   AND deleted = 0
                   AND DATEDIFF(itapprover_completed_datetime,
                       itapprover_actual_start_datetime) <=
                       itapprover_available_duration
                   AND ( redmine_child_parent NOT IN ( 'CHILD' )
                          OR redmine_child_parent IS NULL )) AS c_s4_within_sla,
           (SELECT COUNT(*)
            FROM   sla_byapprover
            WHERE  MONTH(itapprover_completed_datetime) = $p_month
                   AND YEAR(itapprover_completed_datetime) = $p_year
                   AND itapprover_actual_start_datetime IS NOT NULL
                   AND itapprover_sla_flag = '4'
                   AND deleted = 0
                   AND ( redmine_child_parent NOT IN ( 'CHILD' )
                          OR redmine_child_parent IS NULL )) AS c_s4_total,
           (SELECT c_s4_total - c_s4_within_sla)             AS c_s4_exceed_sla,
           (SELECT CASE
                     WHEN c_s4_total = 0 THEN 1
                     ELSE c_s4_within_sla / c_s4_total
                   END * 100)                                AS p_s4_within_sla,
           (SELECT ( 100 - p_s4_within_sla ))                AS p_s4_exceed_sla";

        //------- Total CS

        // Letter Correspondence
        $queryCSLetterSLA = "SELECT
            COUNT(CASE WHEN cs_actual_duration < $cs_letter_duration THEN 1 END) AS c_cs_w_letter_sla,
            COUNT(CASE WHEN cs_actual_duration >= $cs_letter_duration THEN 1 END) AS c_cs_x_letter_sla,
            (COUNT(CASE WHEN cs_actual_duration < $cs_letter_duration THEN 1 END) * 100.0 / COUNT(*)) AS p_cs_w_letter_sla,
            (COUNT(CASE WHEN cs_actual_duration >= $cs_letter_duration THEN 1 END) * 100.0 / COUNT(*)) AS p_cs_x_letter_sla
            FROM sla_cs
            WHERE
            MONTH(created_date) = $p_month
            AND YEAR(created_date) = $p_year
            AND contact_mode = 'Letter Correspondence'";

        // Call-In (Telephone)
        $queryCSCallInSLA = "SELECT
            COUNT(CASE WHEN cs_actual_duration < $cs_call_in_duration THEN 1 END) AS c_cs_w_call_in_sla,
            COUNT(CASE WHEN cs_actual_duration >= $cs_call_in_duration THEN 1 END) AS c_cs_x_call_in_sla,
            (COUNT(CASE WHEN cs_actual_duration < $cs_call_in_duration THEN 1 END) * 100.0 / COUNT(*)) AS p_cs_w_call_in_sla,
            (COUNT(CASE WHEN cs_actual_duration >= $cs_call_in_duration THEN 1 END) * 100.0 / COUNT(*)) AS p_cs_x_call_in_sla
            FROM sla_cs
            WHERE
            MONTH(created_date) = $p_month
            AND YEAR(created_date) = $p_year
            AND contact_mode = 'Call-in'";

        // 'Open Portal'
        $queryCSOnLineSLA = "SELECT
            COUNT(CASE WHEN cs_actual_duration < $cs_online_duration THEN 1 END) AS c_cs_w_online_sla,
            COUNT(CASE WHEN cs_actual_duration >= $cs_online_duration THEN 1 END) AS c_cs_x_online_sla,
            (COUNT(CASE WHEN cs_actual_duration < $cs_online_duration THEN 1 END) * 100.0 / COUNT(*)) AS p_cs_w_online_sla,
            (COUNT(CASE WHEN cs_actual_duration >= $cs_online_duration THEN 1 END) * 100.0 / COUNT(*)) AS p_cs_x_online_sla
            FROM sla_cs
            WHERE
            MONTH(created_date) = $p_month
            AND YEAR(created_date) = $p_year
            AND contact_mode = 'Open Portal'";

        // 'Email'
        $queryCSEmailSLA = "SELECT
            COUNT(CASE WHEN cs_actual_duration < $cs_email_duration THEN 1 END) AS c_cs_w_email_sla,
            COUNT(CASE WHEN cs_actual_duration >= $cs_email_duration THEN 1 END) AS c_cs_x_email_sla,
            (COUNT(CASE WHEN cs_actual_duration < $cs_email_duration THEN 1 END) * 100.0 / COUNT(*)) AS p_cs_w_email_sla,
            (COUNT(CASE WHEN cs_actual_duration >= $cs_email_duration THEN 1 END) * 100.0 / COUNT(*)) AS p_cs_x_email_sla
            FROM sla_cs
            WHERE
            MONTH(created_date) = $p_month
            AND YEAR(created_date) = $p_year
            AND contact_mode = 'Email'";

        // System Security Data Integrity (SLA Internal Factor)

        $queryInternalFacSLA = "SELECT
            (SELECT COUNT(*) FROM sla_internal_fac WHERE MONTH(action_date_by_top) = $p_month AND YEAR(action_date_by_top) = $p_year) AS total_sla_internal_fac,
            (SELECT 0 - total_sla_internal_fac) AS diff_sla_internal_fac,
            (SELECT (total_sla_internal_fac * $r_sec_dataintg_penality)) AS pen_amt_sla_internal_fac";

        /* new 10-05-2024 */
        $queryMitelwithinSLA = "SELECT ((CAST(SUM(call_WI_lvl) AS FLOAT) + CAST(SUM(call_abandon_short) AS FLOAT)) / (CAST(SUM(acd_call_offer) AS FLOAT))) * 100 AS service_level
            FROM sla_mitel
            WHERE MONTH(date_call) = $p_month
            AND YEAR(date_call) = $p_year";

        /* new 10-05-2024 */
        $queryMitelAbandonCall = "SELECT (CAST(SUM(call_abandon_long) AS FLOAT) + CAST(SUM(call_abandon_short) AS FLOAT)) / (CAST(SUM(acd_call_offer) AS FLOAT)) * 100 AS service_level
            FROM sla_mitel
            WHERE MONTH(date_call)=$p_month AND YEAR(date_call)=$p_year";

        /* 2023-10-01 - ADD TALKDESK */
        $queryTalkdesk = "SELECT * FROM sla_talkdesk_monthly
            WHERE MONTH(date_call) = $p_month AND YEAR(date_call) = $p_year";


        $nagios_table = 'sla_nagios';
        // if ($p_year < Carbon::now()->year) {
        if ($p_year < 2025) {
            $nagios_table .= '_' . $p_year;
        }

        // Service Availibility
        $min_in_month = 60 * 24 * (cal_days_in_month(CAL_GREGORIAN, $p_month, $p_year)); // calculate minutes in month for sla calculation
        $querySADown = "SELECT COUNT(DISTINCT downtime) AS num_of_sa_down
            FROM (
                SELECT a.*,
                (CASE
                    WHEN a.host_group = 'portal' AND a.counter >= $s_portal THEN 'YES'
                    WHEN a.host_group = 'bpm' AND a.counter >= $s_bpm THEN 'YES'
                    WHEN a.host_group = 'database' AND a.counter >= $s_database THEN 'YES'
                    WHEN a.host_group = 'web' AND a.counter >= $s_web THEN 'YES'
                    WHEN a.host_group = 'network' AND a.counter >= $s_network THEN 'YES'
                    WHEN a.host_group = 'sso' AND a.counter >= $s_sso THEN 'YES'
                    WHEN a.host_group = 'solr' AND a.counter >= $s_solr THEN 'YES'
                    ELSE 'NO'
                END) AS all_down_flag
                FROM (
                    SELECT host_group,
                    DATE_FORMAT(updated_at, '%Y-%m-%d %H:%i') AS downtime,
                    COUNT(1) AS counter
                    FROM cdc_poms.$nagios_table
                    WHERE host_group IN ('sso', 'portal', 'database', 'bpm', 'solr', 'web', 'network')
                    AND is_exclude = 0
                    AND service_status <> '2'
                    AND MONTH(updated_at) = $p_month
                    AND YEAR (updated_at) = $p_year
                    AND (
                    DAYOFWEEK(updated_at) NOT IN (6, 7)
                    OR ( DAYOFWEEK(updated_at) IN (6)
                        AND TIME(updated_at) NOT BETWEEN TIME('22:00:00') AND TIME('23:59:00')
                    )
                    OR ( DAYOFWEEK(updated_at) IN (7)
                        AND TIME(updated_at) NOT BETWEEN TIME('00:00:00') AND TIME('06:00:00')
                    )
                    )
                    GROUP BY host_group, DATE_FORMAT(updated_at, '%Y-%m-%d %H:%i')
                ) a
            ) b
            WHERE all_down_flag = 'YES'
            ORDER BY downtime DESC";

        //-------- Performance Proxies

        /* MOCK */

        /* MOCK - Letter */
        // $queryCSLetterSLA ="SELECT 1425 AS c_cs_w_letter_sla,
        // 586 AS c_cs_x_letter_sla,
        // (SELECT (c_cs_w_letter_sla) / (c_cs_w_letter_sla + c_cs_x_letter_sla) * 100) AS p_cs_w_letter_sla,
        // (SELECT (100 - p_cs_w_letter_sla)) AS p_cs_x_letter_sla";


        /* MOCK - Open Portal */
        // $queryCSOnLineSLA ="SELECT 1425 AS c_cs_w_online_sla,
        // 586 AS c_cs_x_online_sla,
        // (SELECT (c_cs_w_online_sla) / (c_cs_w_online_sla + c_cs_x_online_sla) * 100) AS p_cs_w_online_sla,
        // (SELECT (100 - p_cs_w_online_sla)) AS p_cs_x_online_sla";


        /* MOCK - EMAIL */
        // $queryCSEmailSLA ="SELECT 1425 AS c_cs_w_email_sla,
        // 586 AS c_cs_x_email_sla,
        // (SELECT (c_cs_w_email_sla) / (c_cs_w_email_sla + c_cs_x_email_sla) * 100) AS p_cs_w_email_sla,
        // (SELECT (100 - p_cs_w_email_sla)) AS p_cs_x_email_sla";

        // SLA-PORTAL-LOGIN     my.ep.web.portal.login.LoginPortlet.executedTime

        $proxies_table = 'performance_proxies';
        // if ($p_year < Carbon::now()->year) {
        if ($p_year < 2025) {
            $proxies_table .= '_' . $p_year;
        }

        /* DUMMY DATA  FOR TEST PURPOSE ONLY - COMMENT THIS */
        // $queryProxies_SLA_PORTAL_LOGIN = self::getMockProxyQuery(1581569, 66342); // 1
        // $queryProxies_SLA_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = self::getMockProxyQuery(4914, 139); // 2
        // $queryProxies_SLA_SC_PN_SPDetailBackingBean_submitSupplierProposal = self::getMockProxyQuery(39569, 3195); // 3
        // $queryProxies_RequestNoteSO_submitRNForApproval = self::getMockProxyQuery(66835, 3232); // 4
        // $queryProxies_SLA_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank = self::getMockProxyQuery(145, 5); // 5
        // $queryProxies_ContractSO_saveContractVer = self::getMockProxyQuery(1452, 161); // 6
        // $queryProxies_SLA_CT_AC_AgreementSO_saveAgreement = self::getMockProxyQuery(3374, 136); // 7
        // $queryProxies_ReceivedNoteSO_saveReceivedNote = self::getMockProxyQuery(125070, 5220); // 8
        // $queryProxies_FulfilmentRequestSO_saveFulfillmentRequest = self::getMockProxyQuery(75485, 3148); // 9
        // $queryProxies_InvoiceSO_saveInvoice = self::getMockProxyQuery(118653, 7397); // 10
        // $queryProxies_MofRegistrationSO_initiateSmApplicationTask = self::getMockProxyQuery(10173, 1128); // 11
        // $queryProxies_SLA_CM_TrackingDiaryBackingBean = self::getMockProxyQuery(1285, 90); // 12
        // $queryProxies_SLA_SC_SE_SpecificationBackingBean_pageChangeListener = self::getMockProxyQuery(23641, 3020); //13
        // $queryProxies_VirtualCertBackingBean_viewVirtualCert = self::getMockProxyQuery(15476, 1414); // 14
        // $queryProxies_SLA_SM_VA_CommonApplBackingBean_PreRenderView = self::getMockProxyQuery(14386, 732); // 15
        // $queryProxies_SLA_SM_SR_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = self::getMockProxyQuery(14342, 952); // 16

        /* REAL DATA */
        $queryProxies_SLA_PORTAL_LOGIN = self::getProxyQuery($p_month, $p_year, 'my.ep.web.portal.login.LoginPortlet.executedTime', ['SLA-PORTAL-LOGIN']); // 1
        $queryProxies_SLA_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = self::getProxyQuery($p_month, $p_year, 'ProcurementPlanBackingBean.savePlan.executedTime', ['SLA-PM-PS']); // 2
        $queryProxies_SLA_SC_PN_SPDetailBackingBean_submitSupplierProposal = self::getProxyQuery($p_month, $p_year, 'SPDetailBackingBean.submitSupplierProposal.executedTime', ['SLA-SC-PN']); // 3
        $queryProxies_RequestNoteSO_submitRNForApproval = self::getProxyQuery($p_month, $p_year, 'RequestNoteSO.submitRNForApproval.executedTime'); // 4
        $queryProxies_SLA_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank = self::getProxyQuery($p_month, $p_year, 'OnlineBiddingBackingBean.updateBidPriceAndCalculateRank.executedTime', ['SLA-SC-BD']); // 5
        $queryProxies_ContractSO_saveContractVer = self::getProxyQuery($p_month, $p_year, 'ContractSO.saveContractVer.executedTime'); // 6
        $queryProxies_SLA_CT_AC_AgreementSO_saveAgreement = self::getProxyQuery($p_month, $p_year, 'AgreementSO.saveAgreement.executedTime', ['SLA-CT-AC', 'SLA-CT-SA', 'SLA-CT-FD']); // 7
        $queryProxies_ReceivedNoteSO_saveReceivedNote = self::getProxyQuery($p_month, $p_year, 'ReceivedNoteSO.saveReceivedNote.executedTime'); // 8
        $queryProxies_FulfilmentRequestSO_saveFulfillmentRequest = self::getProxyQuery($p_month, $p_year, 'FulfilmentRequestSO.saveFulfillmentRequest.executedTime'); // 9
        $queryProxies_InvoiceSO_saveInvoice = self::getProxyQuery($p_month, $p_year, 'InvoiceSO.saveInvoice.executedTime'); // 10
        $queryProxies_MofRegistrationSO_initiateSmApplicationTask = self::getProxyQuery($p_month, $p_year, 'MofRegistrationSO.initiateSmApplicationTask.executedTime'); // 11
        $queryProxies_SLA_CM_TrackingDiaryBackingBean = self::getProxyQuery($p_month, $p_year, 'CmTrackingDiaryBackingBean.search.executedTime', ['SLA-CM-SC']); // 12
        $queryProxies_SLA_SC_SE_SpecificationBackingBean_pageChangeListener = self::getProxyQuery($p_month, $p_year, 'SpecificationBackingBean.pageChangeListener.executedTime', ['SLA-SC-SE']); // 13
        $queryProxies_VirtualCertBackingBean_viewVirtualCert = self::getProxyQuery($p_month, $p_year, 'VirtualCertBackingBean.viewVirtualCert.executedTime'); // 14
        $queryProxies_SLA_SM_VA_CommonApplBackingBean_PreRenderView = self::getProxyQuery($p_month, $p_year, 'CommonApplBackingBean.preRenderView.executedTime', ['SLA-SM-VA']); // 15
        $queryProxies_SLA_SM_SR_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = self::getProxyQuery($p_month, $p_year, 'ViewSuppProfileBackingBean.preRenderViewSupplierProfile.executedTime', ['SLA-SM-SP']); // 16

        //summary security_integrity
        $querySecurityIntegritySummary = "SELECT COUNT(1) AS count_sla_internal_fac FROM sla_internal_fac WHERE MONTH(case_created)=$p_month AND YEAR(case_created)=$p_year";

        //-------Start DB Connection ----------------
        $resSLAReportData = (object)array();
        //details
        $resIntegrityDetails = DB::connection('mysql')->select($queryIntegrityExceedDetails);
        $resITCoordDetails = DB::connection('mysql')->select($queryITCoordExceedDetails);
        $resITSpecDetails = DB::connection('mysql')->select($queryITSpecExceedDetails);
        $resSecurityIntegrityDetails = DB::connection('mysql')->select($querySecurityIntegrityDetails);

        //LOGIN
        // queryProxies_SLA_PORTAL_LOGIN
        $resPerfProxiesSLA_PORTAL_LOGIN = DB::connection('mysql')->select($queryProxies_SLA_PORTAL_LOGIN)[0];

        $resByApproverSLA = DB::connection('mysql')->select($queryByApproverSLA);
        $resByApproverPctSLA = DB::connection('mysql')->select($queryByApproverPercentage)[0];

        //security data integrity internal fac
        $resInternalFacSLA = DB::connection('mysql')->select($queryInternalFacSLA)[0];
        $resIntegrityTotHr = DB::connection('mysql')->select($queryIntegrityTotHr)[0];

        //itcoord
        //$resITCoordPenAmt = DB::connection('mysql')->select($queryITCoordPenAmt)[0];
        $resITCoordWithinData = DB::connection('mysql')->select($queryITCoordWithinSLA)[0];
        $resITCoordExceedData = DB::connection('mysql')->select($queryITCoordExceedSLA)[0];
        $resITCoordTotHr = DB::connection('mysql')->select($queryITCoordTotHr)[0];
        $resITCoordTotalDuration = DB::connection('mysql')->select($queryITCoordTotalDuration)[0];
        $resITCoordTotPenAmt = DB::connection('mysql')->select($queryITCoordTotPenAmt)[0];
        //itserv
        $resITServExceedSLA = DB::connection('mysql')->select($queryITServExceedSLA)[0];
        $resITServExceedDetails = DB::connection('mysql')->select($queryITServExceedDetails);

        //S1
        $resITSpecS1SLA = DB::connection('mysql')->select($queryITSpecS1SLA)[0];
        $resS1ExceedDetails = DB::connection('mysql')->select($queryS1ExceedDetails);
        $resS1TotHr = DB::connection('mysql')->select($queryS1TotHr)[0];
        $resS1TotPenAmt = DB::connection('mysql')->select($queryS1TotPenAmt)[0];

        //S2
        $resITSpecS2SLA = DB::connection('mysql')->select($queryITSpecS2SLA)[0];
        $resS2ExceedDetails = DB::connection('mysql')->select($queryS2ExceedDetails);
        $resS2TotHr = DB::connection('mysql')->select($queryS2TotHr)[0];
        $resS2TotPenAmt = DB::connection('mysql')->select($queryS2TotPenAmt)[0];

        //S3
        $resITSpecS3SLA = DB::connection('mysql')->select($queryITSpecS3SLA)[0];
        $resS3ExceedDetails = DB::connection('mysql')->select($queryS3ExceedDetails);
        $resS3TotHr = DB::connection('mysql')->select($queryS3TotHr)[0];
        $resS3TotPenAmt = DB::connection('mysql')->select($queryS3TotPenAmt)[0];

        $resMitelwithinSLA = DB::connection('mysql')->select($queryMitelwithinSLA)[0];
        $resMitelExceedSLA = DB::connection('mysql')->select($queryMitelAbandonCall)[0]; //need to change to $resMitelAbandonSLA more meaningful
        $resTalkdeskSLA = ($resTalkdeskSLA = DB::connection('mysql')->select($queryTalkdesk)) ? $resTalkdeskSLA[0] : null;
        $resCSCallIn = DB::connection('mysql')->select($queryCSCallInSLA)[0];
        $resCSLetter = DB::connection('mysql')->select($queryCSLetterSLA)[0];
        $resCSOnLineSLA = DB::connection('mysql')->select($queryCSOnLineSLA)[0];
        $resCSEmailSLA = DB::connection('mysql')->select($queryCSEmailSLA)[0];

        //Service  Availibility
        $resSADown = DB::connection('mysql')->select($querySADown)[0];

        //dd($resCSEmailSLA);
        $resITSpecwithinSLA = DB::connection('mysql')->select($queryITSpecWithinSLA)[0];
        $resITSpecExceedSLA = DB::connection('mysql')->select($queryITSpecExceedSLA)[0];
        $resITSpecTotHr = DB::connection('mysql')->select($queryITSpecTotHr)[0];
        $resITSpecTotDuration = DB::connection('mysql')->select($queryITSpecTotDuration)[0];
        $resITSpecTotPenAmt = DB::connection('mysql')->select($queryITSpecTotPenAmt)[0];

        $resSecurityIntegritySummary = DB::connection('mysql')->select($querySecurityIntegritySummary)[0];

        $resPerfProxiesSLA_RequestNoteSO_submitRNForApproval = DB::connection('mysql')->select($queryProxies_RequestNoteSO_submitRNForApproval)[0];
        $resPerfProxiesSLA_ContractSO_saveContractVer = DB::connection('mysql')->select($queryProxies_ContractSO_saveContractVer)[0];
        $resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote = DB::connection('mysql')->select($queryProxies_ReceivedNoteSO_saveReceivedNote)[0];
        $resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest = DB::connection('mysql')->select($queryProxies_FulfilmentRequestSO_saveFulfillmentRequest)[0];
        //InvoiceSO.saveInvoice
        $resPerfProxiesSLA_InvoiceSO_saveInvoice = DB::connection('mysql')->select($queryProxies_InvoiceSO_saveInvoice)[0];
        $resPerfProxiesSLA_MofRegistrationSO_initiateSmApplicationTask = DB::connection('mysql')->select($queryProxies_MofRegistrationSO_initiateSmApplicationTask)[0];
        $resPerfProxiesSLA_VirtualCertBackingBean_viewVirtualCert = DB::connection('mysql')->select($queryProxies_VirtualCertBackingBean_viewVirtualCert)[0];

        //SM
        $resPerfProxiesSLA_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = DB::connection('mysql')->select($queryProxies_SLA_SM_SR_ViewSuppProfileBackingBean_preRenderViewSupplierProfile)[0];
        $resPerfProxiesSLA_SM_VA_CommonApplBackingBean_PreRenderView = DB::connection('mysql')->select($queryProxies_SLA_SM_VA_CommonApplBackingBean_PreRenderView)[0];
        ///$resPerfProxiesSLA_CT_SA_AgreementSO_saveAgreement
        //QT
        $resPerfProxiesSLA_SC_PN_SPDetailBackingBean_submitSupplierProposal = DB::connection('mysql')->select($queryProxies_SLA_SC_PN_SPDetailBackingBean_submitSupplierProposal)[0];
        //QT Catalogue Search
        $resPerfProxiesSLA_SC_SE_SpecificationBackingBean_pageChangeListener = DB::connection('mysql')->select($queryProxies_SLA_SC_SE_SpecificationBackingBean_pageChangeListener)[0];
        $resPerfProxiesSLA_SLA_CM_TrackingDiaryBackingBean = DB::connection('mysql')->select($queryProxies_SLA_CM_TrackingDiaryBackingBean)[0];
        //CT
        $resPerfProxiesSLA_CT_AC_AgreementSO_saveAgreement = DB::connection('mysql')->select($queryProxies_SLA_CT_AC_AgreementSO_saveAgreement)[0];
        //queryProxies_SLA_CT_SA_AgreementSO_saveAgreement
        //PP
        $resPerfProxiesSLA_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = DB::connection('mysql')->select($queryProxies_SLA_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile)[0];
        //Online Bidding
        $resPerfProxiesSLA_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank = DB::connection('mysql')->select($queryProxies_SLA_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank)[0];

        //-------End DB Connection ----------------

        //-------------ITCoord percentage
        $p_ITCoord_SLA = 0;
        $per_itcoord_sla = 0;

        if ($resITCoordWithinData && $resITCoordWithinData->count_itcoord_within_sla > 0) {
            $per_itcoord_sla = $p_ITCoord_SLA = $resITCoordWithinData->count_itcoord_within_sla /
                ($resITCoordWithinData->count_itcoord_within_sla + $resITCoordExceedData->count_itcoord_exceed_sla) * 100;
            $per_itcoord_sla = round($per_itcoord_sla, 2);
            // request by kak lili // IQBAL | 20200629
            $p_ITCoord_SLA = round($resITCoordTotHr->ITCoordTotalHr, 3);
        }
        //dd($resITCoordWithinData);

        //-------------ITSpec percentage
        $p_ITSpec_SLA = 0;
        $per_itspec_sla = 0;
        if ($resITSpecwithinSLA  && $resITSpecwithinSLA->count_itspec_within_sla > 0) {
            $per_itspec_sla = $p_ITSpec_SLA = $resITSpecwithinSLA->count_itspec_within_sla /
                ($resITSpecwithinSLA->count_itspec_within_sla + $resITSpecExceedSLA->count_itspec_exceed_sla) * 100;
            $per_itspec_sla = round($per_itspec_sla, 2);
            // request by kak lili // IQBAL | 20200629
            $p_ITSpec_SLA = round($resITSpecTotHr->ITSpecTotalHr, 3);
        }

        if (null != $resITCoordWithinData && null != $resITCoordExceedData) {
            $resSLAReportData->count_itcoord_within_sla = $resITCoordWithinData->count_itcoord_within_sla;
            $resSLAReportData->count_itcoord_exceed_sla = $resITCoordExceedData->count_itcoord_exceed_sla;
            $resSLAReportData->p_itcoord_within_sla = round(($resITCoordTotalDuration->itcoord_total_time - $p_ITCoord_SLA) / 60 / 60, 2);
            $resSLAReportData->p_itcoord_sla = round($p_ITCoord_SLA, 2);
            $resSLAReportData->per_itcoord_sla = $per_itcoord_sla;;
            //mitel answered call
            $resSLAReportData->p_mitel_sla = round($resMitelwithinSLA->service_level, 2);
            $resSLAReportData->p_mitel_non_comply = round(100 - $resMitelwithinSLA->service_level, 2);
            if ($resTalkdeskSLA) {
                $call_WI_lvl = intval(str_replace(',', '', $resTalkdeskSLA->call_WI_lvl));
                $call_abandon_short = intval(str_replace(',', '', $resTalkdeskSLA->call_abandon_short));
                $acd_call_offer = intval(str_replace(',', '', $resTalkdeskSLA->acd_call_offer));

                $talkdesk_sla = (($call_WI_lvl + $call_abandon_short) / $acd_call_offer) * 100;
                $resSLAReportData->talkdesk_sla = $talkdesk_sla;
                $resSLAReportData->talkdesk_non_comply = 100 - $talkdesk_sla;
                $resSLAReportData->talkdesk_non_comply_pen_diff = round(80 - $talkdesk_sla, 2);
                $resSLAReportData->talkdesk_non_comply_pen_amt = round(80 - $talkdesk_sla, 2) * $r_csm_penality;
            } else {
                $resSLAReportData->talkdesk_sla = null;
                $resSLAReportData->talkdesk_non_comply = null;
                $resSLAReportData->talkdesk_non_comply_pen_diff = null;
                $resSLAReportData->talkdesk_non_comply_pen_amt = null;
            }

            $resSLAReportData->treshold_sla_mitel = $treshold_sla_call_answer_rate;
            $resSLAReportData->diff_sla_mitel = round(round($resMitelwithinSLA->service_level, 2) - $treshold_sla_call_answer_rate, 2);
            if (round(round($resMitelwithinSLA->service_level, 2) - $treshold_sla_call_answer_rate, 2) < 0) {
                $resSLAReportData->pen_amt_sla_mitel = round(round($resMitelwithinSLA->service_level, 2) - $treshold_sla_call_answer_rate, 2) * $r_csm_penality;
            } else {
                $resSLAReportData->pen_amt_sla_mitel = 0;
            }

            $resSLAReportData->reportmonth = $monthName;
            $resSLAReportData->reportyear = $p_year;
            //mitel abandoned call
            $resSLAReportData->p_mitel_abandon_call = round($resMitelExceedSLA->service_level, 2);
            $resSLAReportData->p_mitel_abandon_call_within_sla = 100 - $resSLAReportData->p_mitel_abandon_call;
            $resSLAReportData->treshold_sla_call_abandoned_rate = $treshold_sla_call_abandoned_rate;
            if ($resTalkdeskSLA) {
                $call_abandon_long = intval(str_replace(',', '', $resTalkdeskSLA->call_abandon_long));
                $call_abandon_short = intval(str_replace(',', '', $resTalkdeskSLA->call_abandon_short));
                $acd_call_offer = intval(str_replace(',', '', $resTalkdeskSLA->acd_call_offer));

                $talkdesk_abandon_call = (($call_abandon_long + $call_abandon_short) / $acd_call_offer) * 100;
                $resSLAReportData->talkdesk_abandon_call = $talkdesk_abandon_call;
                $resSLAReportData->talkdesk_call_within_sla = 100 - $talkdesk_abandon_call;
                $resSLAReportData->talkdesk_abandon_call_pen_diff = ($talkdesk_abandon_call > 10) ? $talkdesk_abandon_call - 10 : 0;
                $resSLAReportData->talkdesk_abandon_call_pen_amt = ($talkdesk_abandon_call > 10) ? ($talkdesk_abandon_call - 10) * $r_csm_penality : 0;
            } else {
                $resSLAReportData->talkdesk_abandon_call = null;
                $resSLAReportData->talkdesk_call_within_sla = null;
                $resSLAReportData->talkdesk_abandon_call_pen_diff = null;
                $resSLAReportData->talkdesk_abandon_call_pen_amt = null;
            }

            $resSLAReportData->p_sum_telephone_penality = ($resSLAReportData->talkdesk_non_comply_pen_amt ?? 0) + ($resSLAReportData->talkdesk_abandon_call_pen_amt ?? 0);

            //Data Integrity
            $resSLAReportData->integrity_details =   $resIntegrityDetails;
            $resSLAReportData->integrity_tothr = round($resIntegrityTotHr->total_cases, 3);
            $resSLAReportData->integrity_totpenamt = round($resIntegrityTotHr->total_cases * $r_sec_dataintg_penality, 2);

            if ($resSLAReportData->integrity_tothr > 0) {
                $resSLAReportData->integrity_tothr = '100% (' .  $resSLAReportData->integrity_tothr . ' case)';
            }

            //IT Coord dean
            $resSLAReportData->itcoord_details =   $resITCoordDetails;
            $resSLAReportData->itcoord_tothr = round($resITCoordTotHr->ITCoordTotalHr, 3);
            $resSLAReportData->itcoord_totpenamt = round($resITCoordTotPenAmt->ITCoordTotalPenAmt, 2);

            //IT Spec
            $resSLAReportData->itspec_details =   $resITSpecDetails;
            $resSLAReportData->itspec_tothr = round($resITSpecTotHr->ITSpecTotalHr, 2);
            // $resSLAReportData->itspec_totpenamt = round($resITSpecTotPenAmt->ITSpecTotalPenAmt, 2);
            $resSLAReportData->itspec_totpenamt = floatval($resSLAReportData->itspec_tothr) * 10;

            //IT Service
            $resSLAReportData->itserv_exceed_sla = $resITServExceedSLA->ITServExceedSLA;
            $resSLAReportData->itserv_exceed_details = $resITServExceedDetails;

            //S1 details
            $resSLAReportData->per_s1_within_sla = round($resITSpecS1SLA->p_s1_within_sla, 2); // IQBAL | 20200629
            // $resSLAReportData->p_s1_within_sla = round($resS1TotHr->S1TotalHr,2);

            $totalS1Diff = 0;
            $totalS1Amt = 0;
            foreach ($resS1ExceedDetails as $s1data) {
                $totalS1Diff = $totalS1Diff + $s1data->itseverity_diff;
                $totalS1Amt = $totalS1Amt + $s1data->pen_amt;
            }
            // $resSLAReportData->s1_tothr=round($resS1TotHr->S1TotalHr,2);
            $resSLAReportData->s1_tothr = $totalS1Diff;
            // $resSLAReportData->s1_totpenamt=round($resS1TotPenAmt->S1TotalPenAmt,2);
            $resSLAReportData->s1_totpenamt = $totalS1Amt;
            $resSLAReportData->p_s1_within_sla = (self::getSeverityTotalDays($p_month, $p_year, 's1') - $totalS1Diff) * 24;
            $resSLAReportData->p_s1_non_comply = $totalS1Diff * 24;
            $resSLAReportData->s1_exceed_details =   $resS1ExceedDetails;

            $resSLAReportData->per_s2_within_sla = round($resITSpecS2SLA->p_s2_within_sla, 2); // IQBAL | 20200629

            $totalS2Diff = 0;
            $totalS2Amt = 0;
            foreach ($resS2ExceedDetails as $s2data) {
                $totalS2Diff = $totalS2Diff + $s2data->itseverity_diff;
                $totalS2Amt = $totalS2Amt + $s2data->pen_amt;
            }
            $resSLAReportData->s2_tothr = $totalS2Diff;
            $resSLAReportData->s2_totpenamt = $totalS2Amt;
            $resSLAReportData->p_s2_non_comply = $totalS2Diff * 24;
            $resSLAReportData->s2_exceed_details =   $resS2ExceedDetails;

            // S3
            $resSLAReportData->per_s3_within_sla = round($resITSpecS3SLA->p_s3_within_sla, 2); // IQBAL | 20200629
            $totalS3Diff = 0;
            $totalS3Amt = 0;
            foreach ($resS3ExceedDetails as $s3data) {
                $totalS3Diff = $totalS3Diff + $s3data->itseverity_diff;
                $totalS3Amt = $totalS3Amt + $s3data->pen_amt;
            }
            $resSLAReportData->s3_tothr = $totalS3Diff;
            $resSLAReportData->s3_totpenamt = $totalS3Amt;
            $resSLAReportData->p_s3_non_comply = $totalS3Diff * 24;
            $resSLAReportData->s3_exceed_details =   $resS3ExceedDetails;

            $resSLAReportData->r_penality_10 = $r_penality_10;

            //sla_caseapprover_details
            $totalS4Hour = 0;
            $totalS4PenAmt = 0;
            if ($resByApproverSLA) {
                foreach ($resByApproverSLA as $data) {
                    $totalS4Hour += $data->itapprover_diff;
                    $totalS4PenAmt += $data->pen_amt;
                }
            }
            $resSLAReportData->total_s4_diff = $totalS4Hour;
            $resSLAReportData->total_s4_pen_amount = $totalS4PenAmt;
            $resSLAReportData->per_s4_within_sla = round($resByApproverPctSLA->p_s4_within_sla, 2);
            $resSLAReportData->sla_caseapprover_details = $resByApproverSLA;
            $resSLAReportData->p_s4_non_comply = $totalS4Hour * 24;

            //resSecurityIntegrityDetails
            $resSLAReportData->securityintegrity_details =  $resSecurityIntegrityDetails;

            //total_sla_internal_fac,diff_sla_internal_fac, r_sec_dataintg_penality, pen_amt_sla_internal_fac
            $resSLAReportData->total_sla_internal_fac = $resInternalFacSLA->total_sla_internal_fac;
            $resSLAReportData->diff_sla_internal_fac = $resInternalFacSLA->diff_sla_internal_fac;
            $resSLAReportData->r_sec_dataintg_penality = $r_sec_dataintg_penality;
            $resSLAReportData->pen_amt_sla_internal_fac = $resInternalFacSLA->pen_amt_sla_internal_fac;

            //$resCSCallIn
            $resSLAReportData->c_cs_w_call_in_sla = $resCSCallIn->c_cs_w_call_in_sla;
            $resSLAReportData->c_cs_x_call_in_sla = $resCSCallIn->c_cs_x_call_in_sla;
            $resSLAReportData->p_cs_w_call_in_sla = round($resCSCallIn->p_cs_w_call_in_sla, 2);
            $resSLAReportData->p_cs_w_call_in_non_comply = round(100 - $resCSCallIn->p_cs_w_call_in_sla, 2);
            $resSLAReportData->p_cs_x_call_in_sla = round($resCSCallIn->p_cs_x_call_in_sla, 2);

            //$resCSLetter
            $resSLAReportData->c_cs_w_letter_sla = $resCSLetter->c_cs_w_letter_sla;
            $resSLAReportData->c_cs_x_letter_sla = $resCSLetter->c_cs_x_letter_sla;
            $percentageCSLetter = 100;
            if ($resCSLetter->p_cs_w_letter_sla > 0) {
                $percentageCSLetter = round($resCSLetter->p_cs_w_letter_sla, 2);
            }
            $resSLAReportData->p_cs_w_letter_sla = $percentageCSLetter;
            $resSLAReportData->p_cs_w_letter_non_comply = 100 - $percentageCSLetter;
            $resSLAReportData->p_cs_x_letter_sla = round($resCSLetter->p_cs_x_letter_sla, 2);

            //$resCSOnLineSLA
            $resSLAReportData->c_cs_w_online_sla = $resCSOnLineSLA->c_cs_w_online_sla;
            $resSLAReportData->c_cs_x_online_sla = $resCSOnLineSLA->c_cs_x_online_sla;
            $resSLAReportData->p_cs_w_online_sla = round($resCSOnLineSLA->p_cs_w_online_sla, 2);
            $resSLAReportData->p_cs_w_online_non_comply = round(100 - $resCSOnLineSLA->p_cs_w_online_sla, 2);
            if ($resSLAReportData->p_cs_w_online_non_comply > 20) {
                $resSLAReportData->p_cs_w_online_non_comply_pen = $resSLAReportData->p_cs_w_online_non_comply;
            } else {
                $resSLAReportData->p_cs_w_online_non_comply_pen = 0;
            }
            $resSLAReportData->p_cs_x_online_sla = round($resCSOnLineSLA->p_cs_x_online_sla, 2);

            //$resCSEmailSLA
            $resSLAReportData->c_cs_w_email_sla = $resCSEmailSLA->c_cs_w_email_sla;
            $resSLAReportData->c_cs_x_email_sla = $resCSEmailSLA->c_cs_x_email_sla;
            $resSLAReportData->p_cs_w_email_sla = round($resCSEmailSLA->p_cs_w_email_sla, 2);
            $resSLAReportData->p_cs_w_email_non_comply = round(100 - $resCSEmailSLA->p_cs_w_email_sla, 2);
            if ($resSLAReportData->p_cs_w_email_non_comply > 20) {
                $resSLAReportData->p_cs_w_email_non_comply_pen = $resSLAReportData->p_cs_w_email_non_comply;
            } else {
                $resSLAReportData->p_cs_w_email_non_comply_pen = 0;
            }
            $resSLAReportData->p_cs_x_email_sla = round($resCSEmailSLA->p_cs_x_email_sla, 2);

            $resSLAReportData->p_cs_amount_letter = 0;
            $resSLAReportData->p_cs_amount_online = 0;
            $resSLAReportData->p_cs_amount_email = 0;

            if ($resSLAReportData->p_cs_w_letter_sla) {
                self::calculatePenalty($resSLAReportData, 'letter');
            }

            if ($resSLAReportData->p_cs_w_online_sla) {
                self::calculatePenalty($resSLAReportData, 'online');
            }

            if ($resSLAReportData->p_cs_w_email_sla) {
                self::calculatePenalty($resSLAReportData, 'email');
            }
            /* TOTAL MULTI CHANNEL */
            $resSLAReportData->p_cs_total_amount_multi_channel = 0;
            $resSLAReportData->p_cs_total_amount_multi_channel = $resSLAReportData->p_cs_amount_letter + $resSLAReportData->p_cs_amount_online + $resSLAReportData->p_cs_amount_email;

            //Service Availibility
            $min_in_month = $min_in_month - self::getScheduleDowntime($p_month, $p_year);
            $resSLAReportData->c_sa_sla = round((($min_in_month - (($resSADown->num_of_sa_down) * $nagios_interval)) / $min_in_month) * 100, 2);
            $sysAvailNonComply = (99.6 - $resSLAReportData->c_sa_sla);
            $resSLAReportData->c_sa_non_comply = ($sysAvailNonComply >= 0) ? $sysAvailNonComply : 0;
            $resSLAReportData->c_sa_sla_diff = round(99.6 -  ($resSLAReportData->c_sa_sla), 2);
            $sysAvailNonComplyMinutes = self::getMinutesFromPercentageOfTheMonth($p_month, $p_year, $resSLAReportData->c_sa_non_comply);
            $resSLAReportData->c_sa_non_comply_minutes = number_format($sysAvailNonComplyMinutes, 3);

            $resSLAReportData->count_itspec_within_sla = $resITSpecwithinSLA->count_itspec_within_sla;
            $resSLAReportData->count_itspec_exceed_sla = $resITSpecExceedSLA->count_itspec_exceed_sla;
            $resSLAReportData->p_ITSpec_within_SLA = round(($resITSpecTotDuration->itspec_total_time - $p_ITSpec_SLA) / 60 / 60, 2);
            $resSLAReportData->p_ITSpec_SLA = round($p_ITSpec_SLA, 2);
            $resSLAReportData->per_itspec_sla = $per_itspec_sla;

            //START OF PERFORMANCE PROXIES
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_PORTAL_LOGIN, 'portal_login');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_RequestNoteSO_submitRNForApproval, 'RequestNoteSO_submitRNForApproval');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_ContractSO_saveContractVer, 'resPerfProxiesSLA_ContractSO_saveContractVer');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote, 'resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest, 'resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_InvoiceSO_saveInvoice, 'InvoiceSO_saveInvoice');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_MofRegistrationSO_initiateSmApplicationTask, 'MofRegistrationSO_initiateSmApplicationTask');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_VirtualCertBackingBean_viewVirtualCert, 'VirtualCertBackingBean_viewVirtualCert');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_ViewSuppProfileBackingBean_preRenderViewSupplierProfile, 'ViewSuppProfileBackingBean_preRenderViewSupplierProfile');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_SM_VA_CommonApplBackingBean_PreRenderView, 'SM_VA_CommonApplBackingBean_PreRenderView');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_SC_PN_SPDetailBackingBean_submitSupplierProposal, 'SC_PN_SPDetailBackingBean_submitSupplierProposal');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_SC_SE_SpecificationBackingBean_pageChangeListener, 'SC_SE_SpecificationBackingBean_pageChangeListener');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_SLA_CM_TrackingDiaryBackingBean, 'CM_TrackingDiaryBackingBean');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_CT_AC_AgreementSO_saveAgreement, 'CT_AC_AgreementSO_saveAgreement');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile, 'SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile');
            self::setProxySlaData($resSLAReportData, $resPerfProxiesSLA_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank, 'SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank');
            //END OF PERFORMANCE PROXIES

            $resSLAReportData->securityintegrity_summary = 0;
            $resSLAReportData->securityintegrity_within_sla = 100;
            $resSLAReportData->securityintegrity_act_non_comply = 0;
            $resSLAReportData->total_case = 0;

            if ($resSecurityIntegritySummary->count_sla_internal_fac > 0) {
                $resSLAReportData->securityintegrity_summary = 100;
                $resSLAReportData->securityintegrity_within_sla = 0;
                $resSLAReportData->securityintegrity_act_non_comply = 100;
                $resSLAReportData->total_case = $resSecurityIntegritySummary->count_sla_internal_fac;
            }
            // $resSLAReportData->securityintegrity_within_sla = (100 - $resSecurityIntegritySummary->count_sla_internal_fac);

            //total percentage performance proxies
            $resSLAReportData->p_total_system_performance_proxies = $resSLAReportData->p_exceed_sla_portal_login
                + $resSLAReportData->p_exceed_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile
                + $resSLAReportData->p_exceed_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal
                + $resSLAReportData->p_exceed_sla_RequestNoteSO_submitRNForApproval
                + $resSLAReportData->p_exceed_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank
                + $resSLAReportData->p_exceed_sla_resPerfProxiesSLA_ContractSO_saveContractVer
                + $resSLAReportData->p_exceed_sla_CT_AC_AgreementSO_saveAgreement
                + $resSLAReportData->p_exceed_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote
                + $resSLAReportData->p_exceed_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest
                + $resSLAReportData->p_exceed_sla_InvoiceSO_saveInvoice
                + $resSLAReportData->p_exceed_sla_MofRegistrationSO_initiateSmApplicationTask
                + $resSLAReportData->p_exceed_sla_CM_TrackingDiaryBackingBean
                + $resSLAReportData->p_exceed_sla_SC_SE_SpecificationBackingBean_pageChangeListener
                + $resSLAReportData->p_exceed_sla_VirtualCertBackingBean_viewVirtualCert
                + $resSLAReportData->p_exceed_sla_SM_VA_CommonApplBackingBean_PreRenderView
                + $resSLAReportData->p_exceed_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile;

            // total system proxies divide by 16
            $resSLAReportData->p_total_system_performance_proxies = round($resSLAReportData->p_total_system_performance_proxies / 16, 2);

            $resSLAReportData->p_total_system_performance_proxies_within_sla = 100 - $resSLAReportData->p_total_system_performance_proxies;

            if ($resSLAReportData->p_total_system_performance_proxies > 5) {
                $resSLAReportData->p_total_system_performance_proxies_pen = $resSLAReportData->p_total_system_performance_proxies;
                $resSLAReportData->p_total_system_performance_proxies_pen_minus = $resSLAReportData->p_total_system_performance_proxies - 5;
                $resSLAReportData->p_sum_system_performance_proxies_penality =
                    (($resSLAReportData->p_total_system_performance_proxies - 5) * $r_system_performance_proxies_penality);
            } else {
                $resSLAReportData->p_sum_system_performance_proxies_penality = 0;
                $resSLAReportData->p_total_system_performance_proxies_pen = 0;
                $resSLAReportData->p_total_system_performance_proxies_pen_minus = 0;
            }
        }

        //Service IT Request
        $itRequestData = self::getItServiceReport($p_year, $p_month);
        if ($itRequestData) {
            $resSLAReportData->it_request_list = $itRequestData->exceed_task_list;
            $resSLAReportData->service_non_comply = $itRequestData->total_non_comply;
            $resSLAReportData->service_within_sla = $itRequestData->within_sla_percent;
        }


        $resPenaltyRate = (object)array();
        $resPenaltyRate->system_availability = $r_sys_avalability;
        $resPenaltyRate->system_security = $r_sec_dataintg_penality;
        $resPenaltyRate->system_performance = $r_system_performance_proxies_penality;
        $resPenaltyRate->csm = $r_csm_penality;
        $resPenaltyRate->incident = $r_penality_10;
        $resPenaltyRate->service = $r_penality_10;

        $resSLAReportData->penalty_rate = $resPenaltyRate;

        // penalty calculator
        $resPenaltyTotal = (object)array();
        $resPenaltyTotal->system_availability = $sysAvailNonComplyMinutes * $resPenaltyRate->system_availability;
        $resPenaltyTotal->system_security = $resSecurityIntegritySummary->count_sla_internal_fac * $resPenaltyRate->system_security;

        $resPenaltyTotal->system_performance = $resSLAReportData->p_sum_system_performance_proxies_penality;

        $totalAnsweredPenalty = 0;
        if ($resSLAReportData->p_mitel_non_comply > 20) {
            $totalAnsweredPenalty = ($resSLAReportData->p_mitel_non_comply - 20) * $resPenaltyRate->csm;
            $resSLAReportData->p_mitel_non_comply_pen = $resSLAReportData->p_mitel_non_comply - 20;
        } else {
            $resSLAReportData->p_mitel_non_comply_pen = 0;
        }
        $resPenaltyTotal->mitel_answered = $totalAnsweredPenalty;

        $totalAbandonedPenalty = 0;
        if ($resSLAReportData->p_mitel_abandon_call > 10) {
            $totalAbandonedPenalty = ($resSLAReportData->p_mitel_abandon_call - 10) * $resPenaltyRate->csm;
            $resSLAReportData->p_mitel_abandon_call_pen = $resSLAReportData->p_mitel_abandon_call - 10;
        } else {
            $resSLAReportData->p_mitel_abandon_call_pen = 0;
        }
        $resPenaltyTotal->mitel_abandoned = $totalAbandonedPenalty;

        $resPenaltyTotal->total_csm = $resPenaltyTotal->mitel_abandoned + $resPenaltyTotal->mitel_answered;

        $resPenaltyTotal->cs_letter = 0;
        $resPenaltyTotal->cs_fax = 0;
        $resPenaltyTotal->cs_telephone = 0;
        $resPenaltyTotal->cs_online = 0;
        $resPenaltyTotal->cs_email = 0;

        $resPenaltyTotal->total_other_channel = ($resPenaltyTotal->cs_letter +
            $resPenaltyTotal->cs_fax +
            $resPenaltyTotal->cs_telephone +
            $resPenaltyTotal->cs_online  +
            $resPenaltyTotal->cs_email);

        $resPenaltyTotal->integrity = $resSLAReportData->integrity_totpenamt;
        $resPenaltyTotal->it_coord_pen = $resSLAReportData->itcoord_totpenamt;
        $resPenaltyTotal->it_spec_pen = $resSLAReportData->itspec_totpenamt;
        $resPenaltyTotal->s1_pen = $resSLAReportData->s1_totpenamt;
        $resPenaltyTotal->s2_pen = $resSLAReportData->s2_totpenamt;
        $resPenaltyTotal->s3_pen = $resSLAReportData->s3_totpenamt;
        $resPenaltyTotal->s4_pen = $resSLAReportData->total_s4_pen_amount;

        $resPenaltyTotal->total_incident_mgmt = ($resPenaltyTotal->it_coord_pen +
            $resPenaltyTotal->it_spec_pen +
            $resPenaltyTotal->s1_pen +
            $resPenaltyTotal->s2_pen +
            $resPenaltyTotal->s3_pen +
            $resPenaltyTotal->s4_pen);

        if ($itRequestData) {
            $resPenaltyTotal->total_it_request = $itRequestData->total_penalty_amount;
        } else {
            $resPenaltyTotal->total_it_request = 0;
        }

        $resSLAReportData->penalty_amount = $resPenaltyTotal;

        $param->data = $resSLAReportData;

        self::storePDF($param, $report);
    }

    private static function getSeverityTotalDays($p_month, $p_year, $severity)
    {
        $query = "SELECT SUM(IF(DATEDIFF(itseverity_completed_datetime, itseverity_actual_start_datetime) = 0, 1, DATEDIFF(itseverity_completed_datetime, itseverity_actual_start_datetime))) AS severity_total_day
            FROM sla_itseverity
            WHERE MONTH(itseverity_case_created)=$p_month
            AND YEAR(itseverity_case_created)=$p_year AND itseverity_sla_flag = '$severity'";

        return DB::connection('mysql')->select($query)[0]->severity_total_day;
    }

    private static function getMinutesFromPercentageOfTheMonth($month, $year, $percentage)
    {
        $date = Carbon::createFromDate($year, $month, 1);

        $totalDays = $date->daysInMonth;

        $totalMinutes = $totalDays * 24 * 60;

        $totalMinutes = $totalMinutes - self::getScheduleDowntime($month, $year);

        $minutes = ($percentage / 100) * $totalMinutes;

        return $minutes;
    }

    private static function getScheduleDowntime($p_month, $p_year)
    {
        $startDate = Carbon::create($p_year, $p_month, 1, 22, 0, 0); // Start at 10:00 PM on the 1st day
        $totalMinutes = 0;

        // Handle case where 1st of the month is a Saturday
        if ($startDate->isSaturday()) {
            $totalMinutes += 360; // Midnight to 6:00 AM (Saturday morning)
        }

        while ($startDate->month === $p_month) {
            if ($startDate->isFriday()) {
                $nextDay = $startDate->copy()->addDay();

                if ($nextDay->month !== $p_month) {
                    // If Friday is the last day of the month, only count 10:00 PM to midnight
                    $totalMinutes += 120;
                } else {
                    // Regular Friday downtime (10:00 PM - 6:00 AM)
                    $totalMinutes += 480;
                }
            }

            $startDate->addDay();
        }

        return $totalMinutes;
    }

    private static function getProxyQuery($p_month, $p_year, $transaction, $module = null)
    {
        $performance_proxies_limit = 3000;
        $treshold_sla_proxies = 100;

        $proxies_table = 'performance_proxies';
        // if ($p_year < Carbon::now()->year) {
        if ($p_year < 2025) {
            $proxies_table .= '_' . $p_year;
        }

        if (is_array($module)) {
            $module_clause = "AND MODULE IN ('" . implode("', '", $module) . "')";
        } elseif ($module) {
            $module_clause = "AND MODULE = '$module'";
        } else {
            $module_clause = '';
        }

        $query = "SELECT
        COUNT(CASE WHEN duration_result < $performance_proxies_limit THEN 1 END) AS performance_proxies_within_sla,
        COUNT(CASE WHEN duration_result > $performance_proxies_limit THEN 1 END) AS performance_proxies_exceed_sla,
        (COUNT(CASE WHEN duration_result > $performance_proxies_limit THEN 1 END) / COUNT(*)) * 100 AS percentage_proxies_exceed_sla,
        ($treshold_sla_proxies - (COUNT(CASE WHEN duration_result > $performance_proxies_limit THEN 1 END) / COUNT(*)) * 100) AS percentage_proxies_within_sla
        FROM $proxies_table
        WHERE MONTH(date_time) = $p_month
        AND YEAR(date_time) = $p_year
        AND TRANSACTION = '$transaction'
        $module_clause";

        return $query;
    }

    private static function setProxySlaData($resSLAReportData, $resPerfProxies, $prefix)
    {
        $within_sla_key = "count_within_sla_" . $prefix;
        $exceed_sla_key = "count_exceed_sla_" . $prefix;
        $p_within_sla_key = "p_within_sla_" . $prefix;
        $p_exceed_sla_key = "p_exceed_sla_" . $prefix;

        $resSLAReportData->$within_sla_key = $resPerfProxies->performance_proxies_within_sla;
        $resSLAReportData->$exceed_sla_key = $resPerfProxies->performance_proxies_exceed_sla;
        $resSLAReportData->$p_within_sla_key = round($resPerfProxies->percentage_proxies_within_sla, 2);
        $resSLAReportData->$p_exceed_sla_key = 0;
        if ($resPerfProxies->percentage_proxies_exceed_sla > 0) {
            $resSLAReportData->$p_exceed_sla_key = round($resPerfProxies->percentage_proxies_exceed_sla, 2);
        }
    }

    private static function calculatePenalty($resSLAReportData, $type)
    {
        $score = $resSLAReportData->{"p_cs_w_{$type}_sla"};
        $diff = 0;
        $penRate = 0;
        $penAmt = 0;
        if ($score < 80) {
            $diff = 80 - $score;
            $penRate = 100;
            $penAmt = $diff * $penRate;
        }
        $resSLAReportData->{"p_cs_score_{$type}"} = $score;
        $resSLAReportData->{"p_cs_diff_{$type}"} = $diff;
        $resSLAReportData->{"p_cs_rate_{$type}"} = $penRate;
        $resSLAReportData->{"p_cs_amount_{$type}"} = $penAmt;
    }

    /* use this mock to generate faster report */
    private static function getMockProxyQuery($within, $exceed)
    {
        $query = "SELECT
        $within AS performance_proxies_within_sla,
        $exceed AS performance_proxies_exceed_sla,
        (SELECT (performance_proxies_exceed_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla)) * 100 AS percentage_proxies_exceed_sla,
        (SELECT (100 - percentage_proxies_exceed_sla)) AS percentage_proxies_within_sla";

        return $query;
    }
}
