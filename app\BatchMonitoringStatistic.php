<?php

namespace App;

use DB;
use Auth;
use Log;
use Illuminate\Database\Eloquent\Model;

class BatchMonitoringStatistic extends Model
{
    protected $connection= 'mysql_ep_support';
    protected $primaryKey = 'id';
    protected $table = "ep_batch_log";
    
    public static function createBatchMonitoring($projName,$batchName,$status ,$remarks,$dateModified){
        
        $actionLog  = new BatchMonitoringStatistic;
        $actionLog->project_name = $projName;
        $actionLog->batch_name = $batchName;
        $actionLog->status = $status;
        $actionLog->remarks = $remarks;
        $actionLog->date_modified = $dateModified;
        $actionLog->save();
        
        return $actionLog;
    }
    
     public static function updateBatchMonitoring($dataList,$status ,$remarks,$dateModified){
        
        $dataList->status = $status;
        $dataList->remarks = $remarks;
        $dataList->date_modified = $dateModified;
        $dataList->save();
        
    }
    
    public static function getBatchName($batchName){
        
        $results = DB::connection('mysql_ep_support')->select(
                "SELECT batch_name
                 FROM ep_batch_log WHERE batch_name = '$batchName'");
        return $results;
        
    }
}
