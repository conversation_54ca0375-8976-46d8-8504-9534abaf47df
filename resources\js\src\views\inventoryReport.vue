<template>
  <div id="main" class="vx-col w-full mb-base vs-con-loading__container">
    <!-- FORM -->
    <vx-card title="Inventory Report">
      <div class="vx-row mb-6">
        <div class="vx-col sm:w-1/5 w-full">
          <span>Category Name</span>
        </div>
        <div class="vx-col sm:w-2/5 w-full">
          <v-select label='catDesc' :reduce="category => category.catID" :options="categories" @input="getProductModels"/>
        </div>
      </div>
      <div class="vx-row mb-6">
        <div class="vx-col sm:w-1/5 w-full">
          <span>Product Name</span>
        </div>
        <div class="vx-col sm:w-2/5 w-full">
        <v-select label='productName' :options="products" @input="setProduct" v-model="productSelected"/>
        </div>
      </div>
      <div class="vx-row">
        <div class="vx-col w-full">
          <div class="vx-col w-full">
            <vs-button class="mb-2" @click="generateReport"
              >Generate Report</vs-button
            >
          </div>
        </div>
      </div>
    </vx-card>

    <!-- LIST TABLE -->
    <vx-card class="mt-8" title="List of Generate Inventory Report">
      <vs-table
        @search="handleSearch"
        @change-page="handleChangePage"
        @sort="handleSort"
        v-model="selected"
        pagination
        max-items="10"
        search
        :data="reports"
      >
        <template slot="thead">
          <vs-th sort-key="#">#</vs-th>
          <vs-th sort-key="reportname">Report Name</vs-th>
          <vs-th sort-key="filename">Report Name</vs-th>
          <vs-th sort-key="category">Category</vs-th>
          <vs-th sort-key="status">Status</vs-th>
          <vs-th sort-key="created_date">Created Date</vs-th>
          <!-- <vs-th sort-key="created_by">Created By</vs-th> -->
          <vs-th sort-key="action">Action</vs-th>
        </template>

        <template slot-scope="{ data }">
          <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
            <vs-td :data="data[indextr].id">
              {{ indextr + 1 }}
            </vs-td>
            <vs-td :data="data[indextr].name">
              {{ data[indextr].name }}
            </vs-td>
            <vs-td :data="data[indextr].file_name">
              {{ data[indextr].file_name }}
            </vs-td>
            <vs-td :data="data[indextr].category">
              {{ data[indextr].category }}
            </vs-td>
            <vs-td :data="data[indextr].status">
              <vs-chip
                :color="
                  data[indextr].status === 'completed' ? 'success' : 'primary'
                "
                size="small"
              >
                {{ data[indextr].status.toUpperCase() }}
              </vs-chip>
            </vs-td>
            <vs-td :data="data[indextr].created_at">
              {{ data[indextr].created_at_format }}
            </vs-td>
            <!-- <vs-td :data="data[indextr].created_by">
                            {{ data[indextr].created_by }}
                        </vs-td> -->
            <vs-td :data="data[indextr].id">
              <vs-button
                @click="downloadReport(data[indextr].file_name)"
                size="small"
                :disabled="data[indextr].status !== 'completed'"
              >
                Download
              </vs-button>
            </vs-td>
          </vs-tr>
        </template>
      </vs-table>
    </vx-card>
  </div>
</template>


<script>
import jsPDF from "jspdf";
import "jspdf-autotable";
import Datepicker from "vuejs-datepicker";
import { format, compareAsc } from "date-fns";
import axios from "axios";
import vSelect from "vue-select";

export default {
  mounted() {
    console.log("Component mounted.");
    const vm = this;
    vm.fetchReportList();
  },

  data() {
    return {
      category: 0,
      categories: [],
      product: 0,
      products: [],
      log: [],
      selected: [],
      reports: [],
      productSelected: [],
    };
  },

  components: {
    'v-select': vSelect,
  },

  methods: {
    getCategoryProducts: function () {
      axios.get("/api/list/inventory/getCategoryProducts").then(
        function (response) {
          this.categories = response.data;
        }.bind(this)
      );
    },

    getProductModels(val) {
      this.category = val
      this.productSelected = null
      axios
        .get("/api/list/inventory/getProductModels", {
          params: {
            catID: this.category,
          },
        })
        .then(
          function (response) {
            this.products = response.data;
          }.bind(this)
        );
    },

    setProduct(val) {
      this.product = val.productName
    },

    generateReport() {
      const url = "/api/list/inventory/generate";
      this.$vs.loading({
        container: "#main",
        scale: 0.6,
      });
      axios
        .get(url, {
          params: {
            catID: this.category,
            productName: this.product,
          },
        })
        .then((response) => {
          this.$vs.notify({
            color: "success",
            title: "Success!",
            text: "Report successfuly generated.",
            iconPack: "feather",
            icon: "icon-check-circle",
          });
          this.downloadReport(response.data.reportname);
          this.fetchReportList();
          this.$vs.loading.close('#main > .con-vs-loading');
        })
        .catch(console.error);
    },

    downloadReport(filename) {
      const url = "/api/list/inventory/download/" + filename;
      axios
        .get(url, { responseType: "blob" })
        .then((response) => {
          const blob = new Blob([response.data], {
            type:
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8",
          });
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = filename;
          link.click();
          URL.revokeObjectURL(url);
        })
        .catch(console.error);
    },

    handleSearch(searching) {
      console.log(`The user searched for: ${searching}`);
    },
    handleChangePage(page) {
      console.log(`The user changed the page to: ${page}`);
    },
    handleSort(key, active) {
      console.log(`the user ordered: ${key} ${active}`);
    },
    fetchReportList() {
      fetch("api/list/inventory/list")
        .then((res) => res.json())
        .then((res) => {
          this.reports = res.data;
        })
        .catch((err) => console.log(err));
    },
  },
  created: function () {
    this.getCategoryProducts();
  },
};
</script>