<?php

namespace App\Http\Controllers;

use App\EpSupportMonitoringQtEp;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Services\CmsService;

class epKpiProcessController extends Controller
{
    public static function crmService()
    {
        return new CmsService;
    }

    protected function getCodificationData()
    {

        $dataDate = Carbon::yesterday()->format('d/m/Y');

        $queryToday = "SELECT (SELECT d.STATUS_NAME
                FROM pm_status_desc d
                WHERE d.STATUS_ID = cws.STATUS_ID AND d.LANGUAGE_CODE = 'en') status_name, count(*) as total
        FROM cm_request_code crc, cm_workflow_status cws, sm_supplier ss
        WHERE crc.SUPPLIER_ID = ss.SUPPLIER_ID AND crc.REQUEST_ID = cws.doc_id AND
                cws.STATUS_ID IN(15011, 15014, 15025, 15004, 15003) AND cws.IS_CURRENT = 1 AND crc.RECORD_STATUS = 1 AND
                TO_CHAR(crc.CREATED_DATE, 'dd/mm/yyyy') IN (?) --daily
        GROUP BY cws.STATUS_ID";

        $queryUntilToday = "SELECT (SELECT d.STATUS_NAME FROM pm_status_desc d WHERE d.STATUS_ID = cws.STATUS_ID AND d.LANGUAGE_CODE = 'en') status_name, 
            count(*) AS total
        FROM cm_request_code crc, cm_workflow_status cws, sm_supplier ss
        WHERE crc.SUPPLIER_ID = ss.SUPPLIER_ID 
            AND crc.REQUEST_ID = cws.doc_id 
            AND cws.STATUS_ID IN(15011, 15014, 15025, 15004, 15003)
            AND cws.IS_CURRENT = 1 AND crc.RECORD_STATUS = 1
            AND crc.CREATED_DATE < sysdate -1
        GROUP BY cws.STATUS_ID
        ORDER BY 1 ASC";

        $todayData = DB::connection('oracle_nextgen_rpt')->select($queryToday, array($dataDate));
        $untilTodayData = DB::connection('oracle_nextgen_rpt')->select($queryUntilToday);



        $colorArr = ['#7961F9', '#FF9F43', '#EA5455', '#48dbfb', '#1dd1a1'];
        // $seriesColor1 = ['#7961F9', '#FF9F43', '#EA5455', '#48dbfb', '#1dd1a1'];
        // $seriesColor2 = ['#9c8cfc', '#FFC085', '#f29292', '#0abde3', '#10ac84'];
        $seriesColor1 = ['#7961F9', '#FF9F43', '#EA5455', '#0abde3', '#10ac84'];
        $seriesColor2 = ['#9c8cfc', '#FFC085', '#f29292', '#48dbfb', '#1dd1a1'];

        $colorIndex = 0;
        $totalToday = 0;
        $todayList = array();
        $todaySeries = array();
        $todaylabels = array();
        $todayColor1 = array();
        $todayColor2 = array();

        foreach ($todayData as $data) {
            $totalToday = $totalToday + $data->total;
            $todayData = ([
                'status' => $data->status_name,
                'icon' => 'CircleIcon',
                'color' => $colorArr[$colorIndex],
                'totalRequest' => $data->total
            ]);

            array_push($todayList, $todayData);
            array_push($todaySeries, (int)$data->total);
            array_push($todaylabels, $data->status_name);
            array_push($todayColor1, $seriesColor1[$colorIndex]);
            array_push($todayColor2, $seriesColor2[$colorIndex]);
            $colorIndex++;
        }

        $todayStats = ([
            'today_total' => $totalToday,
            'today_list' => $todayList,
            'today_series' => $todaySeries,
            'today_labels' => $todaylabels,
            'today_series_color1' => $todayColor1,
            'today_series_color2' => $todayColor2
        ]);

        $colorIndex2 = 0;
        $totalUntilToday = 0;
        $untilTodayList = array();
        $untilTodaySeries = array();
        $untilTodaylabels = array();
        $untilTodayColor1 = array();
        $untilTodayColor2 = array();

        foreach ($untilTodayData as $data) {
            $totalUntilToday = $totalUntilToday + $data->total;
            $todateData = ([
                'status' => $data->status_name,
                'icon' => 'CircleIcon',
                'color' => $colorArr[$colorIndex2],
                'totalRequest' => $data->total
            ]);

            array_push($untilTodayList, $todateData);
            array_push($untilTodaySeries, (int)$data->total);
            array_push($untilTodaylabels, $data->status_name);
            array_push($untilTodayColor1, $seriesColor1[$colorIndex2]);
            array_push($untilTodayColor2, $seriesColor2[$colorIndex2]);
            $colorIndex2++;
        }

        $todateStats = ([
            'todate_total' => $totalUntilToday,
            'todate_list' => $untilTodayList,
            'todate_series' => $untilTodaySeries,
            'todate_labels' => $untilTodaylabels,
            'todate_series_color1' => $untilTodayColor1,
            'todate_series_color2' => $untilTodayColor2
        ]);

        return response()->json([
            'today' => $todayStats,
            'toDate' => $todateStats,
        ]);
    }

    protected function getQtData()
    {

        $query = "SELECT count(*) as total
                FROM sc_qt qt
                INNER JOIN sc_workflow_status wf ON qt.qt_id = wf.doc_id AND wf.doc_type = 'QT' AND wf.is_current = 1
                WHERE wf.STATUS_ID IN (60007) AND trunc(qt.publish_date) = trunc(sysdate)";
        $totalQtPublished = DB::connection('oracle_nextgen_rpt')->select($query)[0];

        $query = "SELECT count(*) as total
                FROM sc_qt qt
                INNER JOIN sc_workflow_status wf ON qt.qt_id = wf.doc_id AND wf.doc_type = 'QT' AND wf.is_current = 1
                WHERE wf.STATUS_ID IN (60009) AND trunc(qt.closing_date) = trunc(sysdate)";
        $totalQtClosing = DB::connection('oracle_nextgen_rpt')->select($query)[0];

        $query = "SELECT count(*) as total
                FROM sc_qt qt
                INNER JOIN sc_workflow_status wf ON qt.qt_id = wf.doc_id AND wf.doc_type = 'QT' AND wf.is_current = 1
                WHERE wf.STATUS_ID IN (60035) AND trunc(qt.publish_date) = trunc(sysdate)";
        $totalQtPendingReschedulePublication = DB::connection('oracle_nextgen_rpt')->select($query)[0];

        $query = "SELECT count(*) as total
                FROM sc_qt qt
                INNER JOIN sc_workflow_status wf ON qt.qt_id = wf.doc_id AND wf.doc_type = 'QT' AND wf.is_current = 1
                WHERE wf.STATUS_ID IN (60036) AND trunc(qt.closing_date) = trunc(sysdate)";
        $totalQtPendingRescheduleProposalClosingDate = DB::connection('oracle_nextgen_rpt')->select($query)[0];

        $totalAccumulatedQtPublished = EpSupportMonitoringQtEp::where('qt_monitor_type', 'QT_PUBLISHED')
            ->whereDate('qt_date', Carbon::now()->format('Y-m-d'))
            ->count();

        $totalAccumulatedQtClosing = EpSupportMonitoringQtEp::where('qt_monitor_type', 'QT_CLOSING')
            ->whereDate('qt_date', Carbon::now()->format('Y-m-d'))
            ->count();

        /* MONTH TO DATE DATA */
        $totalMonthlyQtPublished = EpSupportMonitoringQtEp::where('qt_monitor_type', 'QT_PUBLISHED')
            ->whereYear('qt_date', Carbon::now()->year)
            ->whereMonth('qt_date', Carbon::now()->month)
            ->count();

        $totalMonthlyQtClosing = EpSupportMonitoringQtEp::where('qt_monitor_type', 'QT_CLOSING')
            ->whereYear('qt_date', Carbon::now()->year)
            ->whereMonth('qt_date', Carbon::now()->month)
            ->count();

        $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
        $endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
        $queryNew = "SELECT wf.STATUS_ID as status, count(*) as total
            FROM sc_qt qt
            INNER JOIN sc_workflow_status wf ON qt.qt_id = wf.doc_id AND wf.doc_type <> 'SQ' AND wf.is_current = 1
            WHERE wf.STATUS_ID IN (62201, 60013)
            AND trunc(qt.closing_date) > '01-apr-2021'
            AND qt.closing_date BETWEEN TO_DATE(?, 'yyyy-mm-dd') AND TO_DATE(?, 'yyyy-mm-dd')
            GROUP BY wf.STATUS_ID";
        $totalEvaluateLoaQtClosing = DB::connection('oracle_nextgen_rpt')->select($queryNew, array($startDate, $endDate));

        $newLoaEvaluateDataArr = array();
        $labelArr = array();
        $seriesArr = array();
        foreach($totalEvaluateLoaQtClosing as $data) {
            if($data->status === '62201') {
                $data->status = 'Evaluate';
                $icon = 'CircleIcon';
                $color = 'primary';
            } elseif($data->status === '60013') {
                $data->status = 'LOA';
                $icon = 'CircleIcon';
                $color = 'warning';
            }

            $formattedData = ([
                'status' => $data->status,
                'icon' => $icon,
                'color' => $color,
                'total' => (int)$data->total
            ]);

            array_push($labelArr, $data->status);
            array_push($seriesArr, (int)$data->total);
            array_push($newLoaEvaluateDataArr, $formattedData);
        }

        $loaEvaluateDataSeries = ([
            'label' => $labelArr,
            'series' => $seriesArr
        ]);

        return response()->json([
            'total_published' => (int)$totalQtPublished->total,
            'total_closing' => (int)$totalQtClosing->total,
            'total_pending_publish' => (int)$totalQtPendingReschedulePublication->total,
            'total_pending_closing' => (int)$totalQtPendingRescheduleProposalClosingDate->total,
            'total_accumulated_published' => $totalAccumulatedQtPublished,
            'total_accumulated_closing' => $totalAccumulatedQtClosing,
            'total_monthly_published' => $totalMonthlyQtPublished,
            'total_monthly_closing' => $totalMonthlyQtClosing,
            'total_loa_evaluate_series' => $loaEvaluateDataSeries,
            'total_loa_evaluate' => $newLoaEvaluateDataArr
        ]);

    }

    protected function getFulfilmentData()
    {
        $date = Carbon::yesterday();
        $yesterdayFlData = self::crmService()->getFulfilmentDataByDate($date);
        $beforeData = self::crmService()->getFulfilmentDataByDate($date->subDays(1));

        $yesterdayData = array();
        $ytdSeriesValue = array();
        $ytdSeriesLabel = array();
        $ytdTotal = 0;
        $colorArr = ['#3498db', '#2ecc71', '#f1c40f', '#8e44ad', '#e74c3c', '#1abc9c', '#B53471', '#006266'];
        $colorIndex = 0;

        foreach($yesterdayFlData as $data) {
            $ytdTotal = $ytdTotal + (int)$data->total;
            $newData = ([
                'status' => $data->fl_latest_status,
                'icon' => 'DiscIcon',
                'color' => $colorArr[$colorIndex],
                'total' => (int)$data->total,

            ]);

            array_push($yesterdayData, $newData);
            array_push($ytdSeriesValue, (int)$data->total);
            array_push($ytdSeriesLabel, $data->fl_latest_status);

            $colorIndex++;
        }

        $beforeTotal = 0;
        foreach($beforeData as $data) {
            $beforeTotal = $beforeTotal + (int)$data->total;
        }

        return response()->json([
            'yesterday_total' => $ytdTotal,
            'before_total' => $beforeTotal,
            'yesterday_data' => $yesterdayData,
            'ytd_series_value' => $ytdSeriesValue,
            'ytd_series_label' => $ytdSeriesLabel
        ]);
    }

    protected function getFulfilmentMonthlyData()
    {
        $nowYear = Carbon::now()->year;
        $nowMonth = Carbon::now()->month;
        $thisMonthlFlData = self::crmService()->getFulfilmentMonthlyData($nowYear, $nowMonth);

        $totalThisMonth = 0;

        $thisMonthTotals = array();
        $thisMonthDays = array();
        foreach($thisMonthlFlData as $data) {
            $totalThisMonth = $totalThisMonth + $data->total;
            array_push($thisMonthTotals, $data->total);
            array_push($thisMonthDays, $data->day);
        }

        $lastYear = Carbon::now()->subMonth()->year;
        $lastMonth = Carbon::now()->subMonth()->month;
        $lastMonthlFlData = self::crmService()->getFulfilmentMonthlyData($lastYear, $lastMonth);

        $totalLastMonth = 0;

        $LastMonthTotals = array();
        $LastMonthDays = array();
        foreach($lastMonthlFlData as $data) {
            $totalLastMonth = $totalLastMonth + $data->total;
            array_push($LastMonthTotals, $data->total);
            array_push($LastMonthDays, $data->day);
        }

        $selectedDays = null;
        $dayDiff = count($thisMonthDays) - count($LastMonthDays);
        if($dayDiff >= 0) {
            $selectedDays = $thisMonthDays;
        } else {
            $selectedDays = $LastMonthDays;
        }

        return response()->json([
            'total_this_month' => $totalThisMonth,
            'month_days' => $selectedDays,
            'this_month_totals' => $thisMonthTotals,
            'last_month_totals' => $LastMonthTotals,
        ]);

    }

    protected function getBiddingMonitoringData()
    {
        $monthlyBiddingData = self::crmService()->getMonthToDateBidding()[0];
        $todayBiddingData = self::crmService()->getTodayBidding()[0];
        $tomorrowBiddingData = self::crmService()->getTomorrowBidding()[0];

        return response()->json([
            'total_month' => (int)$monthlyBiddingData->total,
            'total_today' => (int)$todayBiddingData->total,
            'total_tomorrow' => (int)$tomorrowBiddingData->total,
            'total_no_bid' => 0,
        ]);
    }
}
