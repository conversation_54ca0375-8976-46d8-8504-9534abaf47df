<?php


namespace App\crm\sla;

use App\Nagios\MigrateUtils;
use App\NagiosData;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Support\Str;
use Log;

class insertNagiosHostsStatusRaw
{

    public static function runFetchDataFromNagios()
    {
        $fetchDate = Carbon::now();

        Log::debug(self::class . ' Starting ... Fetch and insert data from nagios api into nagios_data. Fetch Datetime: ' . $fetchDate);

        $hostUri = 'http://nagios-apps01.eperolehan.com.my/nagios/cgi-bin/statusjson.cgi?query=hostlist';
        $username = 'middleware';
        $password = 'cDc@2019';

        $client = new Client();
        $bodyHost = $client->request('GET', $hostUri, [
            'headers' => [
                'Accept' => 'application/json',
                'Content-type' => 'application/json'
            ],
            'auth' => [
                $username,
                $password
            ]
        ])->getBody();

        $obj = json_decode($bodyHost);

        //dd($obj->data->hostlist);

        self::insertNagiosData($fetchDate, $obj);
    }

    private static function insertNagiosData($fetchDate, $obj)
    {
        foreach ($obj->data->hostlist as $hostName => $status) {

            Log::info(self::class . ' Start : ' . __FUNCTION__ . '     ->> Fetch Datetime: ' . $fetchDate);
            $fetchTimeStart = Carbon::now();

            $nData = new NagiosData;
            $nData->id = (string) Str::uuid();
            $nData->host_name = $hostName;
            $nData->status = $status;
            $nData->created_at = Carbon::now();

            if ($nData->save()) {
                $logsdata = self::class . ' Successfully insert data => Fetch Start: ' . $fetchTimeStart . ' -> '
                    . 'Fetch End: ' . Carbon::now() . ' ID: ' . $nData->id . ' , Completed --- Taken Time: ' .
                    json_encode(['Time' => MigrateUtils::getTakenTime($fetchTimeStart)]);

                Log::info($logsdata);
                dump($logsdata);
            }

        }
    }

}
