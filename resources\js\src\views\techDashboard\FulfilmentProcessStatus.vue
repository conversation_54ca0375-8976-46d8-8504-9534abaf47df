<template>
    <div class="vx-row">
        <vx-card>
            <div class="vx-row flex-col-reverse lg:flex-row">

                <!-- LEFT COL -->
                <div id="stats-left" class="vx-col w-full lg:w-1/3 xl:w-1/3 flex flex-col justify-between vs-con-loading__container" v-if="todayPocoBar.analyticsData">
                    <div>

                        <!-- Avg Session -->
                        <h2 class="mb-1 font-bold">{{ todayPocoBar.analyticsData.total_poco.toLocaleString() }}</h2>
                        <span class="font-medium">Yesterday PO/CO</span>

                        <!-- Previous Data comparison -->
                        <p class="mt-2 text-xl font-medium">
                            <span :class="todayPocoBar.analyticsData.comparison.result >= 0 ? 'text-success' : 'text-danger'">
                            <span v-if="todayPocoBar.analyticsData.comparison.result > 0">+</span>
                            <span>{{ todayPocoBar.analyticsData.comparison.result.toLocaleString() }}</span>
                            </span>
                            <span> vs </span>
                            <span>{{ todayPocoBar.analyticsData.comparison.str }}</span>
                        </p>

                    </div>
                    <div v-if="todayPocoBar.series">
                        <!-- CHART -->
                        <div slot="no-body">
                            <vue-apex-charts type="donut" height="340" class="mb-12 mt-4" :options="todayPocoBar.chartOptions" :series="todayPocoBar.series" />
                        </div>
                        <vs-divider class="my-6"></vs-divider>
                            <ul>
                                <li v-for="pocoData in todayPocoBar.analyticsData.statusData" :key="pocoData.status" class="flex mb-3">
                                    <feather-icon :icon="pocoData.icon" :svgClasses="[`h-5 w-5 stroke-current`]" :style="`color: ${pocoData.color}`"></feather-icon>
                                    <span class="ml-2 inline-block font-semibold">{{ pocoData.status }}</span>
                                    <span class="mx-2">-</span>
                                    <div class="ml-auto flex -mr-1">
                                        <span class="mr-1">{{ pocoData.total.toLocaleString() }}</span>
                                    </div>
                                </li>
                            </ul>
                    </div>
                    <!-- <vs-button icon-pack="feather" icon="icon-chevrons-right" icon-after class="shadow-md w-full lg:mt-0 mt-4">View Details</vs-button> -->
                </div>

                <!-- RIGHT COL -->
                <div id="stats-right" class="vx-col w-full lg:w-2/3 xl:w-2/3 flex flex-col lg:mb-0 mb-base vs-con-loading__container">
                    <!-- <change-time-duration-dropdown class="self-end" /> -->
                    <div class="flex" v-if="pocoComparisonBar.analyticsData">
                            <div class="mr-6">
                                <p class="mb-1 font-semibold">Total PO/CO for This Month</p>
                                <p class="text-3xl text-success">{{ pocoComparisonBar.analyticsData.thisMonth.toLocaleString() }}</p>
                            </div>
                        </div>
                    <vue-apex-charts
                          type=line
                          height=266
                          :options="pocoComparisonBar.chartOptions"
                          :series="pocoComparisonBar.series" />
                </div>

            </div>

            <!-- <vs-divider class="my-6"></vs-divider>

            <div class="vx-row">
                <div class="vx-col w-1/2 mb-3">
                    <small>Goal: $100000</small>
                    <vs-progress class="block mt-1" :percent="50" color="primary"></vs-progress>
                </div>
                <div class="vx-col w-1/2 mb-3">
                    <small>Users: 100K</small>
                    <vs-progress class="block mt-1" :percent="60" color="warning"></vs-progress>
                </div>
                <div class="vx-col w-1/2 mb-3">
                    <small>Retention: 90%</small>
                    <vs-progress class="block mt-1" :percent="70" color="danger"></vs-progress>
                </div>
                <div class="vx-col w-1/2 mb-3">
                    <small>Duration: 1yr</small>
                    <vs-progress class="block mt-1" :percent="90" color="success"></vs-progress>
                </div>
            </div> -->
        </vx-card>
    </div>
</template>

<script>
import VueApexCharts from 'vue-apexcharts'
import axios from "axios";

export default {
    data() {
        return {
            todayPocoBar: {
                analyticsData: {
                    total_poco: 0,
                    comparison : {
                        str: "Yesterday",
                        result: 0
                    },
                    statusData: [],

                },
                series: [400, 100],
                chartOptions: {
                    labels: ['Closed', 'Pending Payment'],
                    dataLabels: {
                        enabled: false
                    },
                    legend: { show: false },
                    chart: {
                        offsetY: 30,
                        type: 'donut',
                        toolbar: {
                            show: false
                        }
                    },
                    stroke: { width: 0 },
                    colors: ['#3498db', '#2ecc71', '#f1c40f', '#8e44ad', '#e74c3c', '#1abc9c', '#B53471', '#006266'],
                    // fill: {
                    //     type: 'gradient',
                    //     gradient: {
                    //         gradientToColors: ['#9c8cfc', '#FFC085', '#f29292']
                    //     }
                    // }
                }
            },
            pocoComparisonBar: {
                analyticsData: {
                    thisMonth: 0,
                    lastMonth: 0
                },
                series: [{
                        name: "This Month PO/CO",
                        data: [4500, 4700, 4480, 4750, 4550, 4800, 4650, 4860]
                    },
                    {
                        name: "Last MOnth PO/CO",
                        data: [4600, 4800, 4550, 4660, 4450, 4650, 4500, 4700]
                    }
                ],
                chartOptions: {
                    chart: {
                        toolbar: { show: false },
                        dropShadow: {
                            enabled: true,
                            top: 5,
                            left: 0,
                            blur: 4,
                            opacity: 0.10,
                        },
                    },
                    stroke: {
                        curve: 'smooth',
                        dashArray: [0, 8],
                        width: [4, 2],
                    },
                    grid: {
                        borderColor: '#e7e7e7',
                    },
                    legend: {
                        show: false,
                    },
                    colors: ['#F97794', '#b8c2cc'],
                    fill: {
                        type: 'gradient',
                        gradient: {
                            shade: 'dark',
                            inverseColors: false,
                            gradientToColors: ['#7367F0', '#b8c2cc'],
                            shadeIntensity: 1,
                            type: 'horizontal',
                            opacityFrom: 1,
                            opacityTo: 1,
                            stops: [0, 100, 100, 100]
                        },
                    },
                    markers: {
                        size: 0,
                        hover: {
                            size: 5
                        }
                    },
                    xaxis: {
                        labels: {
                            style: {
                                cssClass: 'text-grey fill-current',
                            }
                        },
                        axisTicks: {
                            show: false,
                        },
                        categories: ['01', '05', '09', '13', '17', '21', '26', '31'],
                        axisBorder: {
                            show: false,
                        },
                    },
                    yaxis: {
                        tickAmount: 5,
                        labels: {
                            style: {
                                cssClass: 'text-grey fill-current',
                            },
                            formatter: function(val) {
                                return val > 999 ? (val / 1000).toFixed(1) + 'k' : val;
                            }
                        }
                    },
                    tooltip: {
                        x: { show: false }
                    }
                }
            },
        }
    },
    components: {
        VueApexCharts,
    },
    methods: {
        getFlMonitoringData: function () {
            const url = "/api/kpi/fl/monitoring"
            this.$vs.loading({
                container: '#stats-left',
                scale: 0.6
            });
            
            axios.get(url).then(response => {
                
                let data = response.data

                let ytdTotal = data.yesterday_total
                let bforeTotal = data.before_total
                let beforeData = ytdTotal - bforeTotal

                this.todayPocoBar.analyticsData = {
                    total_poco: ytdTotal,
                    comparison : {
                        str: "Day before",
                        result: beforeData
                    },
                    statusData: data.yesterday_data
                }

                this.todayPocoBar.series = data.ytd_series_value
                this.todayPocoBar.chartOptions = {
                    labels: data.ytd_series_label,
                }
                
                this.$vs.loading.close('#stats-left > .con-vs-loading');
            })
            .catch(error => {
                err => console.log(error)
            })
        },

        getFlMonthlyMonitoringData: function () {
            const url = "/api/kpi/fl/monitoring-month"

            this.$vs.loading({
                container: '#stats-right',
                scale: 0.6
            });
            
            axios.get(url).then(response => {
                let data = response.data

                this.pocoComparisonBar.analyticsData.thisMonth = data.total_this_month
                // this.pocoComparisonBar.analyticsData.lastMonth = data.total_this_month
                this.pocoComparisonBar.series = [{
                        name: "This Month PO/CO",
                        data: data.this_month_totals
                    },
                    {
                        name: "Last MOnth PO/CO",
                        data: data.last_month_totals
                    }
                ]

                this.pocoComparisonBar.chartOptions = {
                    xaxis: {
                        categories: data.month_days
                    }
                }

                this.$vs.loading.close('#stats-right > .con-vs-loading');
                
            })
            .catch(error => {
                err => console.log(error)
            })
        },
    },
    mounted: function () {
        this.getFlMonitoringData();
        this.getFlMonthlyMonitoringData();
    },
}
</script>