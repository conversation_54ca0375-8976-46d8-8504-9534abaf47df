<template>
    <!-- LINE CHART -->
    <div class="vx-col w-full mb-base">
        <vx-card id="login-widget" title="ePerolehan Daily User Login" class="vs-con-loading__container">

            <template slot="no-body">
                <!-- Add date picker -->
                <div class="vx-row mb-6">
                    <div class="vx-col sm:w-2/3 w-full text-right">
                    </div>
                    <div class="vx-col sm:w-1/3 w-full">
                        <datepicker :format="'dd MMMM yyyy'" v-model="selectedDate" :minimumView="'day'"
                                    :maximumView="'month'" :disabledDates="disabledDates" @input="updateChart"></datepicker>
                    </div>
                </div>
                <div class="pb-0">
                    <vue-apex-charts type=area height=266 :options="lineAreaChartSpline.chartOptions"
                                     :series="lineAreaChartSpline.series"/>
                    <!-- DATA -->
                </div>
            </template>
            <feather-icon icon="ArrowDownIcon" class="text-primary" svgClasses="h-4 w-4" ></feather-icon>
            <span class="text-xs italic suc">{{ loginTimeFrame }}</span>
            <div class="flex justify-between text-center" slot="no-body-bottom">
                <div class="w-1/3 border border-solid d-theme-border-grey-light border-r-0 border-b-0 border-l-0">
                    <p class="mt-4">PTJ</p>
                    <p class="mb-4 text-3xl font-semibold text-primary">{{sumPTJLoggedIn}}</p>
                </div>
                <div class="w-1/3 border border-solid d-theme-border-grey-light border-r-0 border-b-0">
                    <p class="mt-4">Supplier</p>
                    <p class="mb-4 text-3xl font-semibold text-success">{{sumSupplierLoggedIn}}</p>
                </div>
                <div class="w-1/3 border border-solid d-theme-border-grey-light border-r-0 border-b-0">
                    <p class="mt-4">Total</p>
                    <p class="mb-4 text-3xl font-semibold">{{sumUserLoggedIn}}</p>
                </div>
            </div>
        </vx-card>
    </div>
</template>

<script>
    import axios from 'axios'
    import VueApexCharts from 'vue-apexcharts'
    import Datepicker from 'vuejs-datepicker'

    const themeColors = ['#1a73e8', '#28C76F', '#EA5455', '#FF9F43', '#1E1E1E'];

    export default {
        data() {
            return {
                supplierHourlyLoginData: [],
                ptjHourlyLoginData: [],
                dataTimeStat: [],
                sumSupplierLoggedIn: 0,
                sumPTJLoggedIn: 0,
                sumUserLoggedIn: 0,
                loginTimeFrame: "",
                selectedDate: new Date(),
                disabledDates: {},
                lineAreaChartSpline: {
                    series: [{
                        name: 'PTJ',
                        data: []
                    }, {
                        name: 'Supplier',
                        data: []
                    }],
                    chartOptions: {
                        dataLabels: {
                            enabled: false
                        },
                        stroke: {
                            curve: 'smooth'
                        },
                        colors: themeColors,
                        xaxis: {
                            type: 'datetime',
                            labels: {
                                datetimeFormatter: {
                                    year: 'none',
                                    month: 'none',
                                    day: 'none',
                                    hour: 'HH:mm',
                                    minute: 'none',
                                    second: 'none'
                                }
                            }
                        },
                        tooltip: {
                            x: {
                                format: 'dd/MM/yy HH:mm'
                            },

                        }
                    }
                },
            }
        },
        mounted() {
            this.getUserLogin();

            const vm = this;
            setInterval(() => {
                vm.getUserLogin();
            }, 300000) // update interval 5min
        },
        components: {
            VueApexCharts,
            Datepicker
        },
        methods: {
            async updateChart() {
                this.getUserLogin(this.selectedDate);
            },

            getUserLogin: function (date) {
                date = date ? new Date(date).toISOString().split('T')[0] : this.selectedDate.toISOString().split('T')[0];
                let url = '/api/epss/login-stats';
                this.$vs.loading({
                    container: '#login-widget',
                    scale: 0.6
                });
                axios.post(url, { date: date })
                    .then(res => {
                        this.supplierHourlyLoginData = res.data.dataSupplierHourlyStat;
                        this.ptjHourlyLoginData = res.data.dataPTJHourlyStat;
                        this.dataTimeStat = res.data.dataTimeStat;
                        this.sumSupplierLoggedIn = res.data.sumSupp;
                        this.sumPTJLoggedIn = res.data.sumPTJ;
                        this.sumUserLoggedIn = res.data.sumUserLogin;
                        this.loginTimeFrame = res.data.resultTime;

                        this.updateChartData();
                        this.$vs.loading.close('#login-widget > .con-vs-loading');
                    })
                    .catch(err => console.log(err));
            },

            updateChartData: function () {
                this.lineAreaChartSpline.chartOptions = {
                    xaxis: {
                        categories: this.dataTimeStat
                    }
                };

                this.lineAreaChartSpline.series = [{
                    data: this.ptjHourlyLoginData
                }, {
                    data: this.supplierHourlyLoginData
                }];
            }
        }

    }
</script>

