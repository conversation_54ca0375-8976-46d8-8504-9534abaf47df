<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\crm\sla\InsertTalkdeskData;
use Carbon\Carbon;
use App\Nagios\MigrateUtils;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log as Log;
use Illuminate\Support\Facades\Mail as Mail;

class ScheduleInsertTalkdeskData extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'insert-data-talkdesk';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will checking to insert the data from talkdesk api into table talkdesk_call_data in db poms ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info(self::class . ' starting ..', ['Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();

        $yesterdayDate = Carbon::yesterday();

        $dateStart  = $yesterdayDate;
        $dateEnd    = $yesterdayDate;

        try {
            InsertTalkdeskData::runInsertTalkdeskData($dateStart);
            $logsdata = self::class . ' Query Date Start : ' . $dateStart . ' , Query Date End : ' . $dateEnd . ' , Completed --- Taken Time : ' .  json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            Log::info($logsdata);
            //            $this->sendSuccessEmail($logsdata);
        } catch (\Exception $exc) {

            Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            Log::error(self::class . '>> error happen!! ' . json_encode($err));
            Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
            echo $exc->getTraceAsString();
        }
    }

    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error)
    {
        $data = array(
            "to" => ['<EMAIL>', '<EMAIL>'],
            "subject" => get_class($this) . ':: Server (' . env('APP_ENV') . ') - Error Insert Data (MITEL)'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function ($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                    //->cc($data["cc"])
                    ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
}
