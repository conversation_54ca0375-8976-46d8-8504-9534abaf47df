<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class CrmSlaService
{
    public function getDashboardCRMITCoord()
    {
        $query = DB::connection('mysql_crm')->table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.date_entered', '>=', '2019-01-01');
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.incident_service_type_c', 'incident_it');
        $query->where('tasks.assigned_user_id', 'bd305f97-902e-e186-f506-58997eeecc12');
        $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        $query->whereIn('tasks.name', ['Initial Task', 'Initial Task - IT Incident', 'Initial Task : IT Incident']);
        $query->select(
            'tasks_cstm.task_number_c as taskno',
            'cases.case_number as caseNumber',
            'tasks.status as taskStatus',
            'tasks.name as taskname',
            'tasks_cstm.sla_task_flag_c as flag',
            'tasks.date_start as datestart',
            'tasks.date_due as datedue',
            'tasks_cstm.acknowledge_time_c as acknowledgetime',
            'tasks_cstm.task_duration_c as taskduration',
            'tasks_cstm.assign_group_c as assignedGroup',
            'cases_cstm.sub_category_desc_c as subCategory',
            'cases.name as caseName'
        );
        $query->orderBy('tasks.date_start', 'desc');
        $data = $query->get();
        return $data;
    }

    public function getDashboardCRMITSpec()
    {
        $query = DB::connection('mysql_crm')->table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.date_entered', '>=', '2019-01-01');
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.incident_service_type_c', 'incident_it');
        $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        $query->where('tasks.assigned_user_id', 'd3bf216c-122b-4ce5-9410-899317762b60');
        $query->select(
            'tasks_cstm.task_number_c as taskno',
            'cases.case_number as caseNumber',
            'tasks.status as taskStatus',
            'tasks.name as taskname',
            'tasks_cstm.sla_task_flag_c as taskFlag',
            'tasks.date_start as datestart',
            'tasks.date_due as datedue',
            'tasks_cstm.acknowledge_time_c as acknowledgetime',
            'tasks_cstm.task_duration_c as taskduration',
            'tasks.task_severity as taskSeverity',
            'tasks_cstm.assign_group_c as assignedGroupSpec',
            'cases_cstm.sub_category_desc_c as subCategory',
            'cases.name as caseName'
        );
        $query->orderBy('tasks.date_start', 'desc');
        $data = $query->get();
        return $data;
    }
}
