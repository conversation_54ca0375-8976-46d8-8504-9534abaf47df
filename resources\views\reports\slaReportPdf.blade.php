<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>POMS</title>

	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Roboto&display=swap" rel="stylesheet">

    <style>
    .invoice-box {
        /* max-width: 800px; */
        margin: auto;
        /* padding: 20px; */
        /* border: 1px solid #eee; */
        /* box-shadow: 0 0 10px rgba(0, 0, 0, .15); */
        font-size: 12px;
        line-height: 24px;
		font-family: 'Roboto', sans-serif;
        /* font-family: Arial, 'Helvetica Neue', 'Helvetica', Helvetica, sans-serif; */
        color: #555;
    }

    .invoice-box table {
        width: 100%;
        line-height: inherit;
        /* text-align: left; */
    }

    .invoice-box table td {
        padding: 0 8px 0 8px;
        /* vertical-align: top; */
    }

    .invoice-box table tr td:nth-child(3), td:nth-child(4), td:nth-child(5) {
        text-align: center;
    }

    .invoice-box table tr.top table td {
        padding-bottom: 20px;
    }

    .invoice-box table tr.top table td.title {
        font-size: 45px;
        line-height: 45px;
        color: #333;
		padding-left: 0;
    }

    .invoice-box table tr.information table td {
        padding-bottom: 20px;
		font-size: 18pt;
		padding-left: 0;
    }

    .invoice-box table tr.heading td {
        background: #484848;
		color: #fff;
        border-bottom: 1px solid #ddd;
        font-weight: bold;
        line-height: 1.5em;
        white-space: nowrap;
        padding: 8px;
    }

	.invoice-box table tr.header td {
        font-weight: bold;
		text-align: center;
    }

    .invoice-box table tr.details td {
        /* padding-bottom: 20px; */
		border: 1px solid #eee;
    }

	.invoice-box table tr.spacing td {
        padding-bottom: 20px;
    }

    .invoice-box table tr.item td{
        border-bottom: 1px solid #eee;
		border-right: 1px solid #eee;
		text-align: center;
    }

	.invoice-box table tr.item td:nth-child(2) {
		text-align: left;
    }

	.invoice-box table tr.item td:nth-child(1) {
		text-align: left;
    }

	.invoice-box table tr.item td.total {
		text-align: right;
    }

	.invoice-box table tr.item td:nth-child(1) {
		border-left: 1px solid #eee;
    }

	.invoice-box table tr.item-primary td{
        background: #e8e8e8;
        font-weight: bold;
        line-height: 1.5em;
        white-space: nowrap;
        padding: 5px;
    }

	.invoice-box table tr.item-secondary td{
        background: #f2f2f2;
        font-weight: bold;
    }

    .invoice-box table tr.item-secondary td:nth-child(6) {
		text-align: center;
    }

	.invoice-box table tr.item td.amount {
		text-align: right;
    }

    .invoice-box table tr.item.last td {
        border-bottom: none;
    }

    .invoice-box table tr.total td:nth-child(2) {
        border-top: 2px solid #eee;
        font-weight: bold;
		text-align: right;
    }

    @media only screen and (max-width: 600px) {
        .invoice-box table tr.top table td {
            width: 100%;
            display: block;
            text-align: center;
        }

        .invoice-box table tr.information table td {
            width: 100%;
            display: block;
            text-align: center;
        }
    }

    /** RTL **/
    .rtl {
        direction: rtl;
        font-family: Tahoma, 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif;
    }

    .rtl table {
        text-align: right;
    }

    .rtl table tr td:nth-child(2) {
        /* text-align: left; */
    }
    </style>
</head>

<body>
    <div class="invoice-box">
        <table cellpadding="0" cellspacing="0">
            <tr class="top">
                <td colspan="2">
                    <table>
                        <tr>
                            <td class="title">
                                <img src="http://192.168.66.63/images/report-logo.png" style="height: 50px;">
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr class="information">
                <td colspan="2">
                    <table>
                        <tr>
                            <td>
                                Service Level Agreement (SLA)
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr class="header details">
                <td colspan="4">
                    SLA Between Ministry of Finance <br>
					& Commerce Dot Com Sdn Bhd
                </td>

                <td style="text-align: center" colspan="3">
                    Reporting Month <br>
					{{ date("F", mktime(0, 0, 0, $param->month, 1)) }} {{ $param->year }}
                </td>
            </tr>

            <tr class="details">
                <td colspan="4">CONTRACT</td>
				<td colspan="3"></td>
            </tr>
			<tr class="details" style="text-align: right">
                <td colspan="4">IT Department</td>
				<td colspan="3"></td>
            </tr>
			<tr class="details" style="text-align: right">
                <td colspan="4">Operation Department</td>
				<td colspan="3"></td>
            </tr>
			<tr class="details">
                <td colspan="4">APPROVALS</td>
				<td colspan="3"></td>
            </tr>
			<tr class="details" style="text-align: right">
                <td colspan="4">IT Department</td>
				<td colspan="3"></td>
            </tr>
			<tr class="details" style="text-align: right">
                <td colspan="4">Operation Department</td>
				<td colspan="3"></td>
            </tr>
			<tr class="details" style="text-align: right">
                <td colspan="4">Date Generated</td>
				<td colspan="3" style="text-align: center">{{ date("d M Y") }}</td>
            </tr>

			<tr class="spacing"><td colspan="7">&nbsp;</td></tr>

			{{-- SUMMARY START--}}

			<tr class="heading">
                <td colspan="7">Monthly Performance Summary</td>
            </tr>
			<tr class="item">
                <td colspan="7">Remarks:<br>&nbsp;<br>&nbsp;</td>
            </tr>

            <tr class="heading">
                <td width="5%">No.</td>
				<td colspan="3">SLA Item</td>
				<td>SLA <br>%</td>
				<td style="text-align: center">Score <br>(%)</td>
				<td style="text-align: center">Non Compliance <br>(%)</td>
            </tr>

            <tr class="item item-primary">
                <td>1</td>
				<td colspan="3">ePerolehan Overall Availability</td>
				<td></td>
				<td></td>
				<td></td>
            </tr>

			<tr class="item item-secondary">
                <td>1.1</td>
				<td colspan="3">System Availability (Infra)</td>
				<td style="white-space: nowrap;">&gt;= 99.6%</td>
                <td>{{ $param->data->c_sa_sla != 100 && $param->data->c_sa_sla != 0 ? number_format($param->data->c_sa_sla, 2) : $param->data->c_sa_sla }}%</td>
				<td>{{ $param->data->c_sa_sla != 100 && $param->data->c_sa_sla != 0 ? number_format(100 - $param->data->c_sa_sla, 2) : 100 - $param->data->c_sa_sla }}%</td>
            </tr>

			<tr class="item item-primary">
                <td>2</td>
				<td colspan="3">Data & System Integrity</td>
				<td>100%</td>
				<td>{{$param->data->securityintegrity_within_sla}}%</td>
				<td>{{$param->data->securityintegrity_summary}}%</td>
            </tr>
			<tr class="item item-primary">
                <td>3</td>
				<td colspan="3">System Performance (Response Time - max 3 sec)</td>
				<td>&gt; 95%</td>
				<td>{{number_format($param->data->p_total_system_performance_proxies_within_sla,2)}}%</td>
				<td>{{number_format($param->data->p_total_system_performance_proxies,2)}}%</td>
            </tr>
			<tr class="item item-primary">
                <td>4</td>
				<td colspan="3">Customer Service Performance</td>
				<td></td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item item-secondary">
                <td>4.1</td>
				<td colspan="3">(Telephone)</td>
				<td></td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="3">Answered Call (&lt;10 second)</td>
				<td>&gt;= 80%</td>
                @if($param->year > 2023 || ($param->year == 2023 && $param->month >= 9))
                    <td>{{number_format($param->data->talkdesk_sla,2)}}%</td>
                    <td>{{number_format($param->data->talkdesk_non_comply,2)}}%</td>
                @else
                    <td>{{number_format($param->data->p_mitel_sla,2)}}%</td>
                    <td>{{number_format($param->data->p_mitel_non_comply,2)}}%</td>
                @endif
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="3">Non-abandoned Call</td>
				<td>&gt;= 90%</td>
				@if($param->year > 2023 || ($param->year == 2023 && $param->month >= 9))
                    <td>{{number_format($param->data->talkdesk_call_within_sla,2)}}%</td>
                    <td>{{number_format($param->data->talkdesk_abandon_call,2)}}%</td>
                @else
                    <td>{{number_format($param->data->p_mitel_abandon_call_within_sla,2)}}%</td>
				    <td>{{number_format($param->data->p_mitel_abandon_call,2)}}%</td>
                @endif
            </tr>
			<tr class="item item-secondary">
                <td>4.2</td>
				<td colspan="3">Multiple Channel for Log Case</td>
				<td></td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="3">Letters (&lt;2 working days)</td>
				<td>&gt;= 80%</td>
				<td>{{$param->data->p_cs_w_letter_sla}}%</td>
				<td>{{$param->data->p_cs_w_letter_non_comply}}%</td>
            </tr>
			{{-- <tr class="item">
                <td></td>
				<td colspan="3">Facsimile (2 working days)</td>
				<td>&gt;= 80%</td>
				<td>100%</td>
				<td>0%</td>
            </tr> --}}
			{{-- <tr class="item">
                <td></td>
				<td colspan="3">Telephone (&lt;15 minutes)</td>
				<td>&gt;= 80%</td>
				<td>{{$param->data->p_cs_w_call_in_sla}}%</td>
				<td>{{$param->data->p_cs_w_call_in_non_comply}}%</td>
            </tr> --}}
			<tr class="item">
                <td></td>
				<td colspan="3">Online (&lt;15 minutes)</td>
				<td>&gt;= 80%</td>
				<td>{{$param->data->p_cs_w_online_sla}}%</td>
				<td>{{$param->data->p_cs_w_online_non_comply}}%</td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="3">Emails (&lt;15 minutes)</td>
				<td>&gt;= 80%</td>
				<td>{{$param->data->p_cs_w_email_sla}}%</td>
				<td>{{$param->data->p_cs_w_email_non_comply}}%</td>
            </tr>
			<tr class="item item-primary">
                <td>5</td>
				<td colspan="3">Incident Management</td>
				<td></td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="3">Case Acknowledgement (&lt;=15 minutes)</td>
				<td>100%</td>
				<td>{{$param->data->per_itcoord_sla}}%</td>
				<td>{{round(100 - $param->data->per_itcoord_sla,2)}}%</td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="3">Resolution Identification Time (RIT) (&lt;=4 hours)</td>
				<td>100%</td>
				<td>{{$param->data->per_itspec_sla}}%</td>
				<td>{{round(100 - $param->data->per_itspec_sla,2)}}%</td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="3">Severity 1 Case (&lt;=1 day)</td>
				<td>100%</td>
				<td>{{$param->data->per_s1_within_sla}}%</td>
				<td>{{round(100 - $param->data->per_s1_within_sla,2)}}%</td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="3">Severity 2 Case (&lt;=3 days)</td>
				<td>100%</td>
				<td>{{$param->data->per_s2_within_sla}}%</td>
				<td>{{round(100 - $param->data->per_s2_within_sla,2)}}%</td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="3">Severity 3 Case (&lt;=5 days)</td>
				<td>100%</td>
				<td>{{$param->data->per_s3_within_sla}}%</td>
				<td>{{round(100 - $param->data->per_s3_within_sla,2)}}%</td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="3">Severity 4 Case (Mutually agreed between both parties) A</td>
				<td>100%</td>
				<td>{{$param->data->per_s4_within_sla}}%</td>
				<td>{{round(100 - $param->data->per_s4_within_sla,2)}}%</td>
            </tr>
			<tr class="item item-primary">
                <td>6</td>
				<td colspan="3">Service Request</td>
				<td></td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="3">Service Request (agreed by both parties)</td>
				<td>100%</td>
				<td>{{round($param->data->service_within_sla,2)}}%</td>
				<td>{{round(100 - $param->data->service_within_sla,2)}}%</td>
            </tr>

			<tr class="spacing"><td colspan="7">&nbsp;</td></tr>
			<tr class="spacing"><td colspan="7">&nbsp;</td></tr>
			<tr class="spacing"><td colspan="7">&nbsp;</td></tr>
            <tr class="spacing"><td colspan="7">&nbsp;</td></tr>
			{{-- SUMMARY END--}}

			{{-- KENAAN START--}}

			<tr class="heading" style="page-break-before: always;">
                <td colspan="7">Penalty Report</td>
            </tr>
			<tr class="item">
                <td colspan="7">Remarks:<br>&nbsp;<br>&nbsp;</td>
            </tr>

            <tr class="heading">
                <td width="5%">No.</td>
				<td colspan="2">SLA Item</td>
				<td>Threshold <br>(%)</td>
				<td>Actual Non <br>Compliance <br>(%)</td>
				<td>Rate <br>(RM)</td>
				<td>Amount <br>(RM)</td>
            </tr>

            <tr class="item item-primary">
                <td>1</td>
				<td colspan="2">ePerolehan Overall Availability (Minute)</td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
            </tr>

			<tr class="item item-secondary">
                <td>1.1</td>
				<td colspan="2">System Availability (Infra)</td>
				<td>0.4%</td>
				<td>
                    @if($param->data->c_sa_non_comply > 0)
                        {{ number_format($param->data->c_sa_non_comply, 2) }}% ({{ $param->data->c_sa_non_comply_minutes }} minutes)
                    @else
                        {{ $param->data->c_sa_non_comply }}%
                    @endif
                </td>
				<td class="amount">{{number_format($param->data->penalty_rate->system_availability,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->system_availability,2)}}</td>
            </tr>

			<tr class="item item-primary">
                <td>2</td>
				<td colspan="2">Data & System Integrity (Case)</td>
				<td></td>
                <td>{{$param->data->securityintegrity_act_non_comply}}% @if($param->data->total_case > 0) ({{$param->data->total_case}} case) @endif</td>
				<td class="amount">{{number_format($param->data->penalty_rate->system_security,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->system_security,2)}}</td>
            </tr>
			<tr class="item item-primary">
                <td>3</td>
				<td colspan="2">System Performance (Response Time - max 3 sec) (Percentum)</td>
				<td>5%</td>
				<td>{{$param->data->p_total_system_performance_proxies_pen_minus}}%</td>
				<td class="amount">{{number_format($param->data->penalty_rate->system_performance,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->system_performance,2)}}</td>
            </tr>
			<tr class="item item-primary">
                <td>4</td>
				<td colspan="2">Customer Service Performance (Percentum)</td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item item-secondary">
                <td>4.1</td>
				<td colspan="2">(Telephone)</td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="2">Answered Call (&lt;10 second)</td>
				<td>20%</td>
				<td>{{$param->data->p_mitel_non_comply_pen}}%</td>
				<td class="amount">{{number_format($param->data->penalty_rate->csm,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->mitel_answered,2)}}</td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="2">Abandoned Call</td>
				<td>&lt;= 10%</td>
				<td>{{$param->data->p_mitel_abandon_call_pen}}%</td>
				<td class="amount">{{number_format($param->data->penalty_rate->csm,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->mitel_abandoned,2)}}</td>
			</tr>
			<tr class="item">
				<td colspan="6" style="text-align: right">Total :</td>
				<td class="total"><strong>{{ number_format($param->data->penalty_amount->total_csm,2) }}</strong></td>
            </tr>
			<tr class="item item-secondary">
                <td>4.2</td>
				<td colspan="2">Multiple Channel for Log Case</td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="2">Letters (&lt;2 working days)</td>
				<td>20%</td>
				<td>{{$param->data->p_cs_diff_letter}}%</td>
				<td class="amount">{{number_format($param->data->p_cs_rate_letter,2)}}</td>
				<td class="amount">{{number_format($param->data->p_cs_amount_letter,2)}}</td>
            </tr>
			{{-- <tr class="item">
                <td></td>
				<td colspan="2">Facsimile (2 working days)</td>
				<td>20%</td>
				<td>0%</td>
				<td class="amount">{{number_format($param->data->penalty_rate->csm,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->cs_fax,2)}}</td>
            </tr> --}}
			{{-- <tr class="item">
                <td></td>
				<td colspan="2">Telephone (&lt;15 minutes)</td>
				<td>20%</td>
				<td>{{$param->data->p_cs_w_call_in_non_comply}}%</td>
				<td class="amount">{{number_format($param->data->penalty_rate->csm,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->cs_telephone,2)}}</td>
            </tr> --}}
			<tr class="item">
                <td></td>
				<td colspan="2">Online (&lt;15 minutes)</td>
				<td>20%</td>
				<td>{{$param->data->p_cs_diff_online}}%</td>
				<td class="amount">{{number_format($param->data->p_cs_rate_online,2)}}</td>
				<td class="amount">{{number_format($param->data->p_cs_amount_online,2)}}</td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="2">Emails (&lt;15 minutes)</td>
				<td>20%</td>
				<td>{{$param->data->p_cs_diff_email}}%</td>
				<td class="amount">{{number_format($param->data->p_cs_rate_email,2)}}</td>
				<td class="amount">{{number_format($param->data->p_cs_amount_email,2)}}</td>
			</tr>
			<tr class="item">
				<td colspan="6" style="text-align: right">Total :</td>
				<td class="total"><strong>{{ number_format($param->data->p_cs_total_amount_multi_channel, 2) }}</strong></td>
            </tr>
			<tr class="item item-primary">
                <td>5</td>
				<td colspan="2">Incident Management</td>
				<td></td>
				<td>(Hour)</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="2">Case Acknowledgement (&lt;=15 minutes)</td>
				<td>0%</td>
				<td>{{$param->data->p_itcoord_sla}}</td>
				<td class="amount">{{number_format($param->data->penalty_rate->incident,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->it_coord_pen,2)}}</td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="2">Resolution Identification Time (RIT) (&lt;=4 hours)</td>
				<td>0%</td>
				<td>{{$param->data->p_ITSpec_SLA}}</td>
				<td class="amount">{{number_format($param->data->penalty_rate->incident,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->it_spec_pen,2)}}</td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="2">Severity 1 Case (&lt;=1 day)</td>
				<td>0%</td>
				<td>{{$param->data->p_s1_non_comply}}</td>
				<td class="amount">{{number_format($param->data->penalty_rate->incident,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->s1_pen,2)}}</td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="2">Severity 2 Case (&lt;=3 days)</td>
				<td>0%</td>
				<td>{{$param->data->p_s2_non_comply}}</td>
				<td class="amount">{{number_format($param->data->penalty_rate->incident,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->s2_pen,2)}}</td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="2">Severity 3 Case (&lt;=5 days)</td>
				<td>0%</td>
				<td>{{$param->data->p_s3_non_comply}}</td>
				<td class="amount">{{number_format($param->data->penalty_rate->incident,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->s3_pen,2)}}</td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="2">Severity 4 Case (Mutually agreed between both parties)</td>
				<td>0%</td>
				<td>{{$param->data->p_s4_non_comply}}</td>
				<td class="amount">{{number_format($param->data->penalty_rate->incident,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->s4_pen,2)}}</td>
			</tr>
			<tr class="item">
				<td colspan="6" style="text-align: right">Total :</td>
				<td class="total"><strong>{{ number_format($param->data->penalty_amount->total_incident_mgmt,2) }}</strong></td>
            </tr>
			<tr class="item item-primary">
                <td>6</td>
				<td colspan="2">Service Request</td>
				<td></td>
				<td>(Hour)</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td colspan="2">Service Request (agreed by both parties)</td>
				<td>0%</td>
				<td>{{ $param->data->service_non_comply ?? 0 }}</td>
				{{-- <td class="amount">{{number_format($param->data->penalty_rate->service,2)}}</td> --}}
                <td class="amount">10.00 / 50.00</td>
				<td class="amount">{{number_format($param->data->penalty_amount->total_it_request,2)}}</td>
            </tr>
			<tr class="item">
				<td colspan="6" style="text-align: right">Total :</td>
				<td class="total"><strong>{{ number_format($param->data->penalty_amount->total_it_request,2) }}</strong></td>
            </tr>

			{{-- ADD EMPTY ROW --}}
			@for ($i = 0; $i < 11; $i++)
				<tr class="spacing"><td colspan="7">&nbsp;</td></tr>
			@endfor

			{{-- KENAAN END--}}

			{{-- DETAIL START --}}

			<tr class="heading" style="page-break-before: always;">
                <td colspan="7">Detail Report</td>
            </tr>
			<tr class="item">
                <td colspan="7">Note: Following report detail contains one month accumulated problem cases.</td>
            </tr>
			<tr class="heading">
                <td>1.</td>
				<td colspan="6">ePerolehan Overall Availability (Minute)</td>
            </tr>
			<tr class="heading">
                <td>1.1</td>
				<td colspan="6">System Availability (Infra)</td>
            </tr>
			<tr class="item item-primary">
                <td></td>
				<td>Summary</td>
				<td>SLA <br>(%)</td>
				<td>SCORE <br>(%)</td>
				<td>DIFF <br>(%)</td>
				<td>PENALTY RATE <br>(RM)</td>
				<td>PENALTY AMOUNT <br>(RM)</td>
            </tr>
			<tr class="item">
                <td></td>
				<td>Total Downtime Duration (minutes)</td>
				<td style="white-space: nowrap;">&gt;= 99.6%</td>
				<td>{{$param->data->c_sa_sla}}</td>
				<td>
                    @if($param->data->c_sa_non_comply > 0)
                        {{ round($param->data->c_sa_non_comply, 2) }}% ({{ $param->data->c_sa_non_comply_minutes }} minutes)
                    @else
                        {{ round($param->data->c_sa_non_comply, 2) }}%
                    @endif
                </td>
				<td class="amount">{{number_format($param->data->penalty_rate->system_availability,2)}}</td>
				<td class="amount">{{number_format($param->data->penalty_amount->system_availability,2)}}</td>
            </tr>
            <tr class="total">
                <td colspan="4"></td>
                <td colspan="3">
                    &nbsp;
                   {{-- Grand Total: {{number_format($param->data->penalty_amount->system_availability,2)}} --}}
                </td>
            </tr>
			<tr class="heading">
                <td>2.</td>
				<td colspan="6">Data & System Integrity (Case)</td>
            </tr>
			<tr class="item item-primary">
                <td></td>
				<td>Summary</td>
				<td>SLA <br>(%)</td>
				<td>SCORE <br>(%)</td>
				<td>DIFF <br>(%)</td>
				<td>PENALTY RATE <br>(RM)</td>
				<td>PENALTY AMOUNT <br>(RM)</td>
            </tr>
            @if($param->data->integrity_details)
			@foreach($param->data->integrity_details as $key=>$value)
			<tr class="item">
                <td></td>
				<td><div style="display: table-cell">{{ ++$key }}.&nbsp;&nbsp;</div><div style="display: table-cell">{{ $value->integrity_name }}</div></td>
				<td>{{ $value->integrity_threshold }}</td>
				<td>{{ $value->integrity_score }}</td>
				<td>{{ $value->integrity_diff }}</td>
				<td class="amount">{{number_format($value->pen_rate,2) }}</td>
				<td class="amount">{{number_format($value->pen_amt,2) }}</td>
			</tr>
			@endforeach
			@endif
			{{-- <tr class="item">
                <td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
            </tr> --}}
            <tr class="total">
                <td colspan="4"></td>
                <td colspan="3">
                    &nbsp;
                   {{-- Grand Total: {{number_format($param->data->integrity_totpenamt ?? 0, 2) }} --}}
                </td>
            </tr>
			<tr class="heading">
                <td>3.</td>
				<td colspan="6">System Performance (Response Time - max 3 sec) (Percentum)</td>
            </tr>
			<tr class="item item-primary">
                <td></td>
				<td>Summary</td>
				<td>SLA <br>(%)</td>
				<td>SCORE <br>(%)</td>
				<td>NON COMPLIANCE <br>(%)</td>
				<td>PENALTY RATE <br>(RM)</td>
				<td>PENALTY AMOUNT <br>(RM)</td>
            </tr>
			<tr class="item">
                <td></td>
				<td>1. Login</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_portal_login}}%</td>
				<td>{{$param->data->p_exceed_sla_portal_login}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>2. Procurement Plan Submission</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile}}%</td>
				<td>{{$param->data->p_exceed_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>3. Proposal Submission</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal}}%</td>
				<td>{{$param->data->p_exceed_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>4. Request Note Submission</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_RequestNoteSO_submitRNForApproval}}%</td>
				<td>{{$param->data->p_exceed_sla_RequestNoteSO_submitRNForApproval}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>5. Online Bidding</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank}}%</td>
				<td>{{$param->data->p_exceed_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>6. Fulfilment Details (FD) Submission</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_resPerfProxiesSLA_ContractSO_saveContractVer}}%</td>
				<td>{{$param->data->p_exceed_sla_resPerfProxiesSLA_ContractSO_saveContractVer}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>7. To View The Final Agreement
				</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_CT_AC_AgreementSO_saveAgreement}}%</td>
				<td>{{$param->data->p_exceed_sla_CT_AC_AgreementSO_saveAgreement}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>8. Purchase Request (PR) Submission</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote}}%</td>
				<td>{{$param->data->p_exceed_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>9. Fulfilment Received Notes (FRN) Creation</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest}}%</td>
				<td>{{$param->data->p_exceed_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>10. Invoice Creation</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_InvoiceSO_saveInvoice}}%</td>
				<td>{{$param->data->p_exceed_sla_InvoiceSO_saveInvoice}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>11. Submission Of Ministry Of Finance Account Application </td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_MofRegistrationSO_initiateSmApplicationTask}}%</td>
				<td>{{$param->data->p_exceed_sla_MofRegistrationSO_initiateSmApplicationTask}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>12. Catalogue Search</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_CM_TrackingDiaryBackingBean}}%</td>
				<td>{{$param->data->p_exceed_sla_CM_TrackingDiaryBackingBean}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>13. Search For Template</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_SC_SE_SpecificationBackingBean_pageChangeListener}}%</td>
				<td>{{$param->data->p_exceed_sla_SC_SE_SpecificationBackingBean_pageChangeListener}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>14. Detail Supplier Registration Application From Task</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_VirtualCertBackingBean_viewVirtualCert}}%</td>
				<td>{{$param->data->p_exceed_sla_VirtualCertBackingBean_viewVirtualCert}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>15. Load Virtual Certificate</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_SM_VA_CommonApplBackingBean_PreRenderView}}%</td>
				<td>{{$param->data->p_exceed_sla_SM_VA_CommonApplBackingBean_PreRenderView}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
                <td></td>
				<td>16. View The Company Profile Report</td>
				<td>100%</td>
				<td>{{$param->data->p_within_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile}}%</td>
				<td>{{$param->data->p_exceed_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile}}%</td>
				<td></td>
				<td></td>
            </tr>
			<tr class="item">
				<td colspan="4" style="text-align: right">Total Failure</td>
				<td style="text-align: center;">{{$param->data->p_total_system_performance_proxies}}</td>
				<td class="amount">1,000.00</td>
				<td class="amount">{{number_format($param->data->p_sum_system_performance_proxies_penality,2)}}</td>
            </tr>

			<tr class="heading">
                <td>4.</td>
				<td colspan="6">Customer Service Performance (Percentum)</td>
            </tr>
			<tr class="item item-primary">
                <td></td>
				<td>(Telephone)</td>
				<td>SLA <br>(%)</td>
				<td>SCORE <br>(%)</td>
				<td>DIFF <br>(%)</td>
				<td>PENALTY RATE <br>(RM)</td>
				<td>PENALTY AMOUNT <br>(RM)</td>
            </tr>
			<tr class="item">
                <td></td>
				<td>Call Answered Rate</td>
				<td>80%</td>
                @if($param->year > 2023 || ($param->year == 2023 && $param->month >= 9))
                    <td>{{number_format($param->data->talkdesk_sla, 2)}}%</td>
					<td>{{$param->data->talkdesk_non_comply_pen_diff}}%</td>
					<td class="amount">100.00</td>
					<td class="amount">{{number_format($param->data->talkdesk_non_comply_pen_amt,2)}}</td>
                @else
                    <td>{{number_format($param->data->p_mitel_sla, 2)}}%</td>
					<td>0%</td>
					<td class="amount">100.00</td>
					<td class="amount">0.00</td>
                @endif
            </tr>
			<tr class="item">
                <td></td>
				<td>Call Abandoned Rate</td>
				<td>10%</td>
                @if($param->year > 2023 || ($param->year == 2023 && $param->month >= 9))
                    <td>{{number_format($param->data->talkdesk_abandon_call, 2)}}%</td>
					<td>{{$param->data->talkdesk_abandon_call_pen_diff}}%</td>
					<td class="amount">100.00</td>
					<td class="amount">{{number_format($param->data->talkdesk_abandon_call_pen_amt,2)}}</td>
                @else
                    <td>{{number_format($param->data->p_mitel_abandon_call, 2)}}%</td>
					<td>0%</td>
					<td class="amount">100.00</td>
					<td class="amount">0.00</td>
                @endif
            </tr>
			<tr class="total">
                <td colspan="4"></td>
                <td colspan="3">
                   (Telephone) Total: {{number_format($param->data->p_sum_telephone_penality,2)}}
                </td>
            </tr>
			<tr class="item item-primary">
                <td></td>
				<td>Multiple Channel for Log Case</td>
				<td>SLA <br>(%)</td>
				<td>SCORE <br>(%)</td>
				<td>DIFF <br>(%)</td>
				<td>PENALTY RATE <br>(RM)</td>
				<td>PENALTY AMOUNT <br>(RM)</td>
            </tr>
			<tr class="item">
                <td></td>
				<td>Letters (&lt;2 working days)</td>
				<td>80%</td>
				<td>{{$param->data->p_cs_w_letter_sla}}%</td>
				<td>{{$param->data->p_cs_diff_letter}}%</td>
				<td class="amount">{{number_format($param->data->p_cs_rate_letter,2)}}</td>
				<td class="amount">{{number_format($param->data->p_cs_amount_letter,2)}}</td>
            </tr>
			{{-- <tr class="item">
                <td></td>
				<td>Facsimile (2 working days)</td>
				<td>80%</td>
				<td>0%</td>
				<td>0%</td>
				<td class="amount">0.0</td>
				<td class="amount">0.0</td>
            </tr> --}}
			{{-- <tr class="item">
                <td></td>
				<td>Telephone (&lt;15 minutes)</td>
				<td>80%</td>
				<td>{{$param->data->p_cs_w_call_in_sla}}</td>
				<td>0%</td>
				<td class="amount">0.0</td>
				<td class="amount">0.0</td>
            </tr> --}}
			<tr class="item">
                <td></td>
				<td>Online (&lt;15 minutes)</td>
				<td>80%</td>
				<td>{{$param->data->p_cs_w_online_sla}}%</td>
				<td>{{$param->data->p_cs_diff_online}}%</td>
				<td class="amount">{{number_format($param->data->p_cs_rate_online,2)}}</td>
				<td class="amount">{{number_format($param->data->p_cs_amount_online,2)}}</td>
            </tr>
			<tr class="item">
                <td></td>
				<td>Emails (&lt; 15 minutes)</td>
				<td>80%</td>
				<td>{{$param->data->p_cs_w_email_sla}}%</td>
				<td>{{$param->data->p_cs_diff_email}}%</td>
				<td class="amount">{{number_format($param->data->p_cs_rate_email,2)}}</td>
				<td class="amount">{{number_format($param->data->p_cs_amount_email,2)}}</td>
            </tr>
			<tr class="total">
                <td colspan="4"></td>
                <td colspan="3">
                   Multiple Channel Total: {{number_format($param->data->p_cs_total_amount_multi_channel, 2)}}
                </td>
            </tr>
			<tr class="total">
                <td colspan="4"></td>
                <td colspan="3">
                    &nbsp;
                   {{-- Grand Total: 0.00 --}}
                </td>
            </tr>

            <tr class="spacing"><td colspan="7">&nbsp;</td></tr>
            <tr class="spacing"><td colspan="7">&nbsp;</td></tr>

			<tr class="heading">
                <td>5.</td>
				<td colspan="6">Incident Management</td>
            </tr>
			<tr class="item item-primary">
                <td></td>
				<td>Case Acknowledgement (&lt;=15 minutes)</td>
				<td>SLA <br>(Min)</td>
				<td>ACTUAL <br>(Min)</td>
				<td>DIFF <br>(Min)</td>
				<td>PENALTY RATE <br>(RM)</td>
				<td>PENALTY AMOUNT <br>(RM)</td>
            </tr>
			<!-- loop here IT Coordinator -->
			@if($param->data->itcoord_details)
			@foreach($param->data->itcoord_details as $key=>$value)
			<tr class="item">
                <td></td>
				<td><div style="display: table-cell">{{ ++$key }}.&nbsp;&nbsp;</div><div style="display: table-cell">{{ $value->itcoord_name }}</div></td>
				<td>{{ $value->itcoord_available_duration }}</td>
				<td>{{ $value->itcoord_actual_duration }}</td>
				<td>{{ $value->itcoord_diff }}</td>
				<td class="amount">{{number_format($value->pen_rate,2) }}</td>
				<td class="amount">{{number_format($value->pen_amt,2) }}</td>
			</tr>
			@endforeach
			@endif
			<tr class="item">
				<td colspan="4" style="text-align: right">IT - Coordinator Total in Hour :</td>
				<td style="text-align: center;">{{ $param->data->itcoord_tothr }}</td>
				<td class="amount">10.00</td>
				<td class="amount">{{number_format($param->data->itcoord_totpenamt,2) }}</td>
            </tr>
			<tr class="item item-primary">
                <td></td>
				<td>Resolution Identification Time (RIT) (&lt;=4 hours)</td>
				<td>SLA <br>(Hour)</td>
				<td>ACTUAL <br>(Hour)</td>
				<td>DIFF <br>(Hour)</td>
				<td>PENALTY RATE <br>(RM)</td>
				<td>PENALTY AMOUNT <br>(RM)</td>
            </tr>
			<!-- loop here (IT Specialist) -->
			@if($param->data->itspec_details)
			@foreach($param->data->itspec_details as $key=>$value)
			<tr class="item">
                <td></td>
				<td><div style="display: table-cell">{{ ++$key }}.&nbsp;&nbsp;</div><div style="display: table-cell">{{ $value->itspec_name }}</div></td>
				<td>{{ $value->itspec_available_duration }}</td>
				<td>{{ $value->itspec_actual_duration }}</td>
				<td>{{ $value->itspec_diff }}</td>
				<td class="amount">{{number_format($value->pen_rate,2) }}</td>
				<td class="amount">{{number_format($value->pen_amt,2) }}</td>
			</tr>
			@endforeach
			@endif
			<tr class="item">
				<td colspan="4" style="text-align: right">IT - Specialist Total in Hour :</td>
				<td style="text-align: center;">{{ $param->data->itspec_tothr }}</td>
				<td class="amount">10.00</td>
				<td class="amount">{{number_format($param->data->itspec_totpenamt,2) }}</td>
			</tr>

			<!-- temp start (S1) ------------------------ -->

			<tr class="item item-primary">
			<td></td>
				<td>Severity 1 Case (&lt;=1 day)</td>
				<td>SLA <br>(Day)</td>
				<td>ACTUAL <br>(Day)</td>
				<td>DIFF <br>(Day)</td>
				<td>PENALTY RATE <br>(RM)</td>
				<td>PENALTY AMOUNT <br>(RM)</td>
            </tr>
			<!-- loop here IT S1 -->
			@if($param->data->s1_exceed_details)
			@foreach($param->data->s1_exceed_details as $key=>$value)
			<tr class="item">
                <td></td>
				<td><div style="display: table-cell">{{ ++$key }}.&nbsp;&nbsp;</div><div style="display: table-cell">{{ $value->itseverity_name }}</div></td>
				<td>{{ $value->itseverity_available_durations }}</td>
				<td>{{ $value->itseverity_actual_durations }}</td>
				<td>{{ $value->itseverity_diff }}</td>
				<td class="amount">10.00</td>
				<td class="amount">{{number_format($value->pen_amt,2) }}</td>
			</tr>
			@endforeach
			@endif
			<tr class="item">
				<td colspan="4" style="text-align: right">Severity 1 Case (&lt;=1 day) - Total in Days :</td>
				<td style="text-align: center;">{{ $param->data->s1_tothr }}</td>
				<td class="amount">10.00</td>
				<td class="amount">{{number_format($param->data->s1_totpenamt,2) }}</td>
			</tr>

			<!-- temp end (S1) ------------------------ -->

			<!-- temp start (S2) ------------------------ -->

			<tr class="item item-primary">
			<td></td>
				<td>Severity 2 Case (&lt;=3 days)</td>
				<td>SLA <br>(Day)</td>
				<td>ACTUAL <br>(Day)</td>
				<td>DIFF <br>(Day)</td>
				<td>PENALTY RATE <br>(RM)</td>
				<td>PENALTY AMOUNT <br>(RM)</td>
            </tr>
			<!-- loop here IT S2 -->
			@if($param->data->s2_exceed_details)
			@foreach($param->data->s2_exceed_details as $key=>$value)
			<tr class="item">
                <td></td>
				<td><div style="display: table-cell">{{ ++$key }}.&nbsp;&nbsp;</div><div style="display: table-cell">{{ $value->itseverity_name }}</div></td>
				<td>{{ $value->itseverity_available_durations }}</td>
				<td>{{ $value->itseverity_actual_durations }}</td>
				<td>{{ $value->itseverity_diff }}</td>
				<td class="amount">10.00</td>
				<td class="amount">{{number_format($value->pen_amt,2) }}</td>
			</tr>
			@endforeach
			@endif
			<tr class="item">
				<td colspan="4" style="text-align: right">Severity 2 Case (&lt;=3 days) - Total in Days :</td>
				<td style="text-align: center;">{{ $param->data->s2_tothr }}</td>
				<td class="amount">10.00</td>
				<td class="amount">{{number_format($param->data->s2_totpenamt,2) }}</td>
			</tr>

			<!-- temp end (S2) ------------------------ -->

			<!-- temp start (S3) ------------------------ -->

			<tr class="item item-primary">
			<td></td>
				<td>Severity 3 Case (&lt;=5 days)</td>
				<td>SLA <br>(Day)</td>
				<td>ACTUAL <br>(Day)</td>
				<td>DIFF <br>(Day)</td>
				<td>PENALTY RATE <br>(RM)</td>
				<td>PENALTY AMOUNT <br>(RM)</td>
            </tr>
			<!-- loop here IT S3 -->
			@if($param->data->s3_exceed_details)
			@foreach($param->data->s3_exceed_details as $key=>$value)
			<tr class="item">
                <td></td>
				<td><div style="display: table-cell">{{ ++$key }}.&nbsp;&nbsp;</div><div style="display: table-cell">{{ $value->itseverity_name }}</div></td>
				<td>{{ $value->itseverity_available_durations }}</td>
				<td>{{ $value->itseverity_actual_durations }}</td>
				<td>{{ $value->itseverity_diff }}</td>
				<td class="amount">10.00</td>
				<td class="amount">{{number_format($value->pen_amt,2) }}</td>
			</tr>
			@endforeach
			@endif
			<tr class="item">
				<td colspan="4" style="text-align: right">Severity 3 Case (&lt;=5 days) - Total in Days :</td>
				<td style="text-align: center;">{{ $param->data->s3_tothr }}</td>
				<td class="amount">10.00</td>
				<td class="amount">{{number_format($param->data->s3_totpenamt,2) }}</td>
			</tr>

			<!-- temp end (S3) ------------------------ -->


			<tr class="item item-primary">
                <td></td>
				<td>Severity 4 Case (Mutually agreed between both parties)</td>
				<td>SLA <br>(Day)</td>
				<td>ACTUAL <br>(Day)</td>
				<td>DIFF <br>(Day)</td>
				<td>PENALTY RATE <br>(RM)</td>
				<td>PENALTY AMOUNT <br>(RM)</td>
            </tr>
			<!-- loop here (Resolution Time)-->
			@if($param->data->sla_caseapprover_details)
			@foreach($param->data->sla_caseapprover_details as $key=>$value)
			<tr class="item">
                <td></td>
				<td><div style="display: table-cell">{{ ++$key }}.&nbsp;&nbsp;</div><div style="display: table-cell">{{ $value->itapprover_name }}</div></td>
				<td>{{ $value->itapprover_available_duration }}</td>
				<td>{{ $value->itapprover_actual_duration }}</td>
				<td>{{ $value->itapprover_diff }}</td>
				<td class="amount">{{number_format($value->pen_rate,2) }}</td>
				<td class="amount">{{number_format($value->pen_amt,2) }}</td>
			</tr>
			@endforeach
			@endif
			<tr class="item">
				<td colspan="4" style="text-align: right">Severity 4  Case - Total in Days :</td>
				<td style="text-align: center;">{{ $param->data->total_s4_diff }}</td>
				<td class="amount">10.00</td>
				<td class="amount">{{number_format($param->data->total_s4_pen_amount,2) }}</td>
            </tr>
			<tr class="total">
                <td colspan="4"></td>
                <td colspan="3">
                    &nbsp;
                   {{-- Grand Total: 0.00 --}}
                </td>
            </tr>

			<tr class="heading">
                <td>6.</td>
				<td colspan="6">Service Request</td>
            </tr>
			<tr class="item item-primary">
                <td></td>
				<td>Downtime Request</td>
				<td>SLA <br>(Hour)</td>
				<td>ACTUAL <br>(Hour)</td>
				<td>DIFF <br>(Hour)</td>
				<td>PENALTY RATE <br>(RM)</td>
				<td>PENALTY AMOUNT <br>(RM)</td>
            </tr>
			<!-- loop here -->
			@if($param->data->it_request_list)
			@foreach($param->data->it_request_list as $key=>$value)
			<tr class="item">
                <td></td>
				<td><div style="display: table-cell">{{ ++$key }}.&nbsp;&nbsp;</div><div style="display: table-cell">{{ $value->title }}</div></td>
				<td>{{ $value->sla }}</td>
				<td>{{ $value->actual }}</td>
				<td>{{ $value->exceed_hour }}</td>
				{{-- <td class="amount">{{number_format($value->pen_rate,2) }}</td> --}}
                <td class="amount">10.00 / 50.00</td>
				<td class="amount">{{number_format($value->pen_amount,2) }}</td>
			</tr>
			@endforeach
			@endif
			<tr class="item">
				<td colspan="4" style="text-align: right">Service Request - Total in Hour :</td>
				<td style="text-align: center;">{{ $param->data->service_non_comply ?? 0 }}</td>
				<td class="amount">10.00 / 50.00</td>
				<td class="amount">{{number_format($param->data->penalty_amount->total_it_request,2) }}</td>
            </tr>
			<tr class="total">
                <td colspan="4"></td>
                <td colspan="3">
                    &nbsp;
                   {{-- Grand Total: 0.00 --}}
                </td>
			</tr>

			{{-- DETAIL END --}}

        </table>
    </div>
</body>
</html>
