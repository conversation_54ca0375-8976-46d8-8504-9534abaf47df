{"compilerOptions": {"baseUrl": "./", "target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "isolatedModules": true, "strict": true, "jsx": "preserve", "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "paths": {"@/*": ["./resources/ts/*"], "@themeConfig": ["./themeConfig.ts"], "@layouts/*": ["./resources/ts/@layouts/*"], "@layouts": ["./resources/ts/@layouts"], "@core/*": ["./resources/ts/@core/*"], "@core": ["./resources/ts/@core"], "@images/*": ["./resources/images/*"], "@styles/*": ["./resources/styles/*"], "@validators": ["./resources/ts/@core/utils/validators"], "@db/*": ["./resources/ts/plugins/fake-api/handlers/*"], "@api-utils/*": ["./resources/ts/plugins/fake-api/utils/*"], "@core-scss/*": ["resources/styles/@core/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "skipLibCheck": true, "types": ["vite/client", "unplugin-vue-router/client", "vite-plugin-vue-meta-layouts/client"]}, "include": ["./typed-router.d.ts", "./vite.config.*", "./env.d.ts", "./shims.d.ts", "./resources/ts/**/*", "./resources/ts/**/*.vue", "./themeConfig.ts", "./auto-imports.d.ts", "./components.d.ts"], "exclude": ["./dist", "./node_modules"]}