<?php

/*
 * Function : Insert data from table in db crm into table sla_itcoord in db poms
 * Date : 4 Oct 2019
 * Author : Nur Asmaa
 */

namespace App\crm\sla;

use Carbon\Carbon;
use DateTime;
use Log;
use DB;
use Ramsey\Uuid\Uuid;
use App\Nagios\MigrateUtils;
use Config;
use App\BatchMonitoringStatistic;
use DateInterval;
use DatePeriod;

class insertITCoordSlaDataAdhoc
{

    public static function runAdhocInsertITCoord($begin, $end)
    {
        // insert specific date
        dump('runAdhocInsertITCoord > Process by specified date...');

        $interval = DateInterval::createFromDateString('1 day');
        $period = new DatePeriod($begin, $interval, $end);

        foreach ($period as $dtperiod) {
            self::insertITCoordSlaData($dtperiod);
        }
    }

    /* Read data from Database crm copy into database cdc_poms */

    private static function insertITCoordSlaData($datePeriod = null)
    {

        $dateStart = Carbon::now();

        Log::info(self::class . ' Start : ' . __FUNCTION__ . ' ->> Date Start: ' . $dateStart);
        $dtStartTime = Carbon::now();

        $processDate = null;

        if ($datePeriod) {
            $processDate = $datePeriod->format('Y-m-d');
        } else {
            $yesterdayData = Carbon::yesterday();
            $getCurr = strtotime($yesterdayData);
            $processDate = date('Y-m-d', $getCurr);
        }

        dump("Inserted data for date... " . $processDate);


        try {

            $result = "select distinct `c`.`case_number` AS `case_number`,
                        CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00') AS `itcoord_case_created`,
                        `c`.`name` AS `itcoord_name`,
                        `itcoord_tc`.`task_number_c` AS `itcoord_task_number`,
                        `itcoord_tc`.`sla_task_flag_c` AS `itcoord_sla_flag`,
                        convert_tz(`itcoord_t`.`date_start`,'+00:00','+08:00') AS `itcoord_start_datetime`,
                        convert_tz(`itcoord_t`.`date_due`,'+00:00','+08:00') AS `itcoord_due_datetime`,
                        convert_tz(`itcoord_tc`.`sla_start_15min_c`,'+00:00','+08:00') AS `itcoord_actual_start_datetime`,
                        convert_tz(`itcoord_tc`.`sla_stop_15min_c`,'+00:00','+08:00') AS `itcoord_completed_datetime`,
                        timestampdiff(SECOND,convert_tz(`itcoord_t`.`date_start`,'+00:00','+08:00'),convert_tz(`itcoord_t`.`date_due`,'+00:00','+08:00')) AS `itcoord_available_duration`,
                        timestampdiff(SECOND,convert_tz(`itcoord_tc`.`sla_start_15min_c`,'+00:00','+08:00'),convert_tz(`itcoord_tc`.`sla_stop_15min_c`,'+00:00','+08:00')) AS `itcoord_actual_duration` 
                        from ((((`cases` `c` join `cases_cstm` `cc` on((`c`.`id` = `cc`.`id_c`))) 
                        left join `tasks` `itcoord_t` on((`c`.`id` = `itcoord_t`.`parent_id`))) 
                        left join `tasks_cstm` `itcoord_tc` on(((`itcoord_t`.`id` = `itcoord_tc`.`id_c`) 
                        and (`itcoord_tc`.`sla_task_flag_c` = '2')))) 
                        left join `cstm_list_app` `subcat` on(((`cc`.`sub_category_c` = `subcat`.`value_code`) 
                        and (`subcat`.`type_code` = 'cdc_sub_category_list') 
                        and (trim(`subcat`.`value_code`) <> '') 
                        and (`subcat`.`value_code` not in ('10712_15034','10714_15842','10713_15534'))))) 
                        where ((`cc`.`incident_service_type_c` = 'incident_it') 
                        and (`cc`.`request_type_c` = 'incident') 
                        and (`c`.`deleted` = 0) 
                        and (`itcoord_t`.`deleted` = 0)  
                        and (`itcoord_tc`.`task_number_c` is not null) 
                        and (`c`.`date_entered` >= '2022-03-01') 
                        and (STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') <= ?)
                        and (trim(`subcat`.`value_code`) <> '')) 
                        order by `c`.`case_number` desc";

            $results = DB::connection('mysql_crm')->select($result, array($processDate));

            if (is_array($results) || is_object($results)) {
                $counter = 0;
                foreach ($results as $data) {
                    $count = DB::connection('mysql')->table('sla_itcoord')
                        ->where('case_number', $data->case_number)
                        ->count();

                    $insertedCaseNum = DB::connection('mysql_crm')->table('cases')
                        ->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                        ->join('tasks', 'tasks.parent_id', '=', 'cases_cstm.id_c')
                        ->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
                        ->where('cases.case_number', $data->case_number)
                        ->where('cases_cstm.incident_service_type_c', 'incident_it')
                        ->where('tasks_cstm.sla_task_flag_c', 2)
                        ->where('tasks_cstm.is_sent', 1)
                        ->count();

                    if ($count == 0 && $insertedCaseNum == 0) {

                        $insertDataDate = Carbon::now();

                        DB::connection('mysql')
                            ->insert(
                                'insert into sla_itcoord 
                            (case_number, itcoord_case_created, itcoord_name, itcoord_task_number, itcoord_sla_flag, itcoord_start_datetime, itcoord_due_datetime, itcoord_actual_start_datetime, itcoord_completed_datetime, itcoord_available_duration, itcoord_actual_duration, itcoord_insert_data_datetime) 
                            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                                [
                                    $data->case_number,
                                    $data->itcoord_case_created,
                                    $data->itcoord_name,
                                    $data->itcoord_task_number,
                                    $data->itcoord_sla_flag,
                                    $data->itcoord_start_datetime,
                                    $data->itcoord_due_datetime,
                                    $data->itcoord_actual_start_datetime,
                                    $data->itcoord_completed_datetime,
                                    $data->itcoord_available_duration,
                                    $data->itcoord_actual_duration,
                                    $insertDataDate
                                ]
                            );
                        DB::connection('mysql_crm')
                            ->table('cases')
                            ->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                            ->join('tasks', 'tasks.parent_id', '=', 'cases_cstm.id_c')
                            ->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
                            ->where('cases.case_number', $data->case_number)
                            ->where('cases_cstm.incident_service_type_c', 'incident_it')
                            ->where('tasks_cstm.sla_task_flag_c', 2)
                            ->update([
                                'tasks_cstm.is_sent' => 1,
                                'tasks_cstm.poms_inserted_date' => $insertDataDate
                            ]);

                        $counter++;
                        if ($counter == 50) {
                            sleep(1);
                            $counter = 0;
                        }

                        $logsdata = self::class . ' Successfully insert data for SLA IT Coordinator => for Date : ' . $processDate . ' -> Case Number : ' . $data->case_number . ' , Completed --- Taken Time : ' .
                            json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
                            
                        Log::info($logsdata);
                        dump($logsdata);
                    }
                }
                $total = count($results);
                $note = ' Successfully insert data for SLA IT Coordinator (15 Minutes) => Total : ' . $total;
                $projName = 'POMS Integration';
                $batchName = 'insertITCoordSlaData';
                $remarks = $note;
                $dateModified = Carbon::now();
                $status = 'Success';
                $list = BatchMonitoringStatistic::getBatchName($batchName);
                $dataList = BatchMonitoringStatistic::where('batch_name', 'insertITCoordSlaData')
                    ->first();
                log::info('Masuk Success');
                if (count($list) > 0) {
                    log::info('Success Satu');
                    BatchMonitoringStatistic::updateBatchMonitoring($dataList, $status, $remarks, $dateModified);
                } else {
                    BatchMonitoringStatistic::createBatchMonitoring($projName, $batchName, $status, $remarks, $dateModified);
                    log::info('Success Dua');
                }
            }

            return $results;
        } catch (\Exception $exc) {

            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $projName = 'POMS Integration';
            $batchName = 'insertITCoordSlaData';
            $remarks = $exc->getMessage();
            $dateModified = Carbon::now();
            $status = 'Failed';
            $list = BatchMonitoringStatistic::getBatchName($batchName);
            $dataList = BatchMonitoringStatistic::where('batch_name', 'insertITCoordSlaData')
                ->first();
            log::info('Masuk Error');
            if (count($list) > 0) {
                log::info('Error Satu');
                BatchMonitoringStatistic::updateBatchMonitoring($dataList, $status, $remarks, $dateModified);
            } else {
                BatchMonitoringStatistic::createBatchMonitoring($projName, $batchName, $status, $remarks, $dateModified);
                log::info('Error Dua');
            }
            echo $exc->getTraceAsString();
        }
        return null;
    }
}
