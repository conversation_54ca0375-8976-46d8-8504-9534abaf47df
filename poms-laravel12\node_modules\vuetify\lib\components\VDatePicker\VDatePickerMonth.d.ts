import type { PropType } from 'vue';
export type VDatePickerMonthSlots = {
    day: {
        props: {
            onClick: () => void;
        };
        item: any;
        i: number;
    };
};
export declare const makeVDatePickerMonthProps: <Defaults extends {
    max?: unknown;
    min?: unknown;
    disabled?: unknown;
    month?: unknown;
    year?: unknown;
    modelValue?: unknown;
    showAdjacentMonths?: unknown;
    weekdays?: unknown;
    weeksInMonth?: unknown;
    firstDayOfWeek?: unknown;
    allowedDates?: unknown;
    color?: unknown;
    hideWeekdays?: unknown;
    multiple?: unknown;
    showWeek?: unknown;
    transition?: unknown;
    reverseTransition?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    max: unknown extends Defaults["max"] ? PropType<unknown> : {
        type: PropType<unknown extends Defaults["max"] ? unknown : unknown>;
        default: unknown extends Defaults["max"] ? unknown : {} | Defaults["max"];
    };
    min: unknown extends Defaults["min"] ? PropType<unknown> : {
        type: PropType<unknown extends Defaults["min"] ? unknown : unknown>;
        default: unknown extends Defaults["min"] ? unknown : {} | Defaults["min"];
    };
    disabled: unknown extends Defaults["disabled"] ? {
        type: BooleanConstructor;
        default: null;
    } : Omit<{
        type: BooleanConstructor;
        default: null;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["disabled"] ? boolean : boolean | Defaults["disabled"]>;
        default: unknown extends Defaults["disabled"] ? boolean : boolean | Defaults["disabled"];
    };
    month: unknown extends Defaults["month"] ? (StringConstructor | NumberConstructor)[] : {
        type: PropType<unknown extends Defaults["month"] ? string | number : string | number | Defaults["month"]>;
        default: unknown extends Defaults["month"] ? string | number : NonNullable<string | number> | Defaults["month"];
    };
    year: unknown extends Defaults["year"] ? (StringConstructor | NumberConstructor)[] : {
        type: PropType<unknown extends Defaults["year"] ? string | number : string | number | Defaults["year"]>;
        default: unknown extends Defaults["year"] ? string | number : NonNullable<string | number> | Defaults["year"];
    };
    modelValue: unknown extends Defaults["modelValue"] ? PropType<unknown[]> : {
        type: PropType<unknown extends Defaults["modelValue"] ? unknown[] : unknown[] | Defaults["modelValue"]>;
        default: unknown extends Defaults["modelValue"] ? unknown[] : unknown[] | Defaults["modelValue"];
    };
    showAdjacentMonths: unknown extends Defaults["showAdjacentMonths"] ? BooleanConstructor : {
        type: PropType<unknown extends Defaults["showAdjacentMonths"] ? boolean : boolean | Defaults["showAdjacentMonths"]>;
        default: unknown extends Defaults["showAdjacentMonths"] ? boolean : boolean | Defaults["showAdjacentMonths"];
    };
    weekdays: unknown extends Defaults["weekdays"] ? {
        type: PropType<import("../../composables/calendar.js").CalendarWeekdays[]>;
        default: () => number[];
    } : Omit<{
        type: PropType<import("../../composables/calendar.js").CalendarWeekdays[]>;
        default: () => number[];
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["weekdays"] ? import("../../composables/calendar.js").CalendarWeekdays[] : import("../../composables/calendar.js").CalendarWeekdays[] | Defaults["weekdays"]>;
        default: unknown extends Defaults["weekdays"] ? import("../../composables/calendar.js").CalendarWeekdays[] : import("../../composables/calendar.js").CalendarWeekdays[] | Defaults["weekdays"];
    };
    weeksInMonth: unknown extends Defaults["weeksInMonth"] ? {
        type: PropType<"dynamic" | "static">;
        default: string;
    } : Omit<{
        type: PropType<"dynamic" | "static">;
        default: string;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["weeksInMonth"] ? "static" | "dynamic" : "static" | "dynamic" | Defaults["weeksInMonth"]>;
        default: unknown extends Defaults["weeksInMonth"] ? "static" | "dynamic" : NonNullable<"static" | "dynamic"> | Defaults["weeksInMonth"];
    };
    firstDayOfWeek: unknown extends Defaults["firstDayOfWeek"] ? {
        type: (StringConstructor | NumberConstructor)[];
        default: undefined;
    } : Omit<{
        type: (StringConstructor | NumberConstructor)[];
        default: undefined;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["firstDayOfWeek"] ? string | number : string | number | Defaults["firstDayOfWeek"]>;
        default: unknown extends Defaults["firstDayOfWeek"] ? string | number : NonNullable<string | number> | Defaults["firstDayOfWeek"];
    };
    allowedDates: unknown extends Defaults["allowedDates"] ? PropType<unknown[] | ((date: unknown) => boolean)> : {
        type: PropType<unknown extends Defaults["allowedDates"] ? unknown[] | ((date: unknown) => boolean) : unknown[] | ((date: unknown) => boolean) | Defaults["allowedDates"]>;
        default: unknown extends Defaults["allowedDates"] ? unknown[] | ((date: unknown) => boolean) : NonNullable<unknown[] | ((date: unknown) => boolean)> | Defaults["allowedDates"];
    };
    color: unknown extends Defaults["color"] ? StringConstructor : {
        type: PropType<unknown extends Defaults["color"] ? string : string | Defaults["color"]>;
        default: unknown extends Defaults["color"] ? string : string | Defaults["color"];
    };
    hideWeekdays: unknown extends Defaults["hideWeekdays"] ? BooleanConstructor : {
        type: PropType<unknown extends Defaults["hideWeekdays"] ? boolean : boolean | Defaults["hideWeekdays"]>;
        default: unknown extends Defaults["hideWeekdays"] ? boolean : boolean | Defaults["hideWeekdays"];
    };
    multiple: unknown extends Defaults["multiple"] ? PropType<number | boolean | (string & {}) | "range"> : {
        type: PropType<unknown extends Defaults["multiple"] ? number | boolean | (string & {}) | "range" : number | boolean | (string & {}) | "range" | Defaults["multiple"]>;
        default: unknown extends Defaults["multiple"] ? number | boolean | (string & {}) | "range" : Defaults["multiple"] | NonNullable<number | boolean | (string & {}) | "range">;
    };
    showWeek: unknown extends Defaults["showWeek"] ? BooleanConstructor : {
        type: PropType<unknown extends Defaults["showWeek"] ? boolean : boolean | Defaults["showWeek"]>;
        default: unknown extends Defaults["showWeek"] ? boolean : boolean | Defaults["showWeek"];
    };
    transition: unknown extends Defaults["transition"] ? {
        type: StringConstructor;
        default: string;
    } : Omit<{
        type: StringConstructor;
        default: string;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["transition"] ? string : string | Defaults["transition"]>;
        default: unknown extends Defaults["transition"] ? string : string | Defaults["transition"];
    };
    reverseTransition: unknown extends Defaults["reverseTransition"] ? {
        type: StringConstructor;
        default: string;
    } : Omit<{
        type: StringConstructor;
        default: string;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["reverseTransition"] ? string : string | Defaults["reverseTransition"]>;
        default: unknown extends Defaults["reverseTransition"] ? string : string | Defaults["reverseTransition"];
    };
};
export declare const VDatePickerMonth: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        transition: string;
        disabled: boolean;
        reverseTransition: string;
        showAdjacentMonths: boolean;
        weekdays: import("../../composables/calendar.js").CalendarWeekdays[];
        weeksInMonth: "static" | "dynamic";
        hideWeekdays: boolean;
        showWeek: boolean;
    } & {
        max?: unknown;
        min?: unknown;
        color?: string | undefined;
        multiple?: number | boolean | (string & {}) | "range" | undefined;
        month?: string | number | undefined;
        year?: string | number | undefined;
        modelValue?: unknown[] | undefined;
        firstDayOfWeek?: string | number | undefined;
        allowedDates?: unknown[] | ((date: unknown) => boolean) | undefined;
    } & {
        $children?: {} | import("vue").VNodeChild | {
            day?: ((arg: {
                props: {
                    onClick: () => void;
                };
                item: any;
                i: number;
            }) => import("vue").VNodeChild) | undefined;
        };
        'v-slots'?: {
            day?: false | ((arg: {
                props: {
                    onClick: () => void;
                };
                item: any;
                i: number;
            }) => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:day"?: false | ((arg: {
            props: {
                onClick: () => void;
            };
            item: any;
            i: number;
        }) => import("vue").VNodeChild) | undefined;
    } & {
        "onUpdate:modelValue"?: ((date: unknown) => any) | undefined;
        "onUpdate:month"?: ((date: number) => any) | undefined;
        "onUpdate:year"?: ((date: number) => any) | undefined;
    }, void, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        'update:modelValue': (date: unknown) => true;
        'update:month': (date: number) => true;
        'update:year': (date: number) => true;
    }, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        transition: string;
        disabled: boolean;
        reverseTransition: string;
        showAdjacentMonths: boolean;
        weekdays: import("../../composables/calendar.js").CalendarWeekdays[];
        weeksInMonth: "static" | "dynamic";
        firstDayOfWeek: string | number;
        hideWeekdays: boolean;
        showWeek: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        day: (arg: {
            props: {
                onClick: () => void;
            };
            item: any;
            i: number;
        }) => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        transition: string;
        disabled: boolean;
        reverseTransition: string;
        showAdjacentMonths: boolean;
        weekdays: import("../../composables/calendar.js").CalendarWeekdays[];
        weeksInMonth: "static" | "dynamic";
        hideWeekdays: boolean;
        showWeek: boolean;
    } & {
        max?: unknown;
        min?: unknown;
        color?: string | undefined;
        multiple?: number | boolean | (string & {}) | "range" | undefined;
        month?: string | number | undefined;
        year?: string | number | undefined;
        modelValue?: unknown[] | undefined;
        firstDayOfWeek?: string | number | undefined;
        allowedDates?: unknown[] | ((date: unknown) => boolean) | undefined;
    } & {
        $children?: {} | import("vue").VNodeChild | {
            day?: ((arg: {
                props: {
                    onClick: () => void;
                };
                item: any;
                i: number;
            }) => import("vue").VNodeChild) | undefined;
        };
        'v-slots'?: {
            day?: false | ((arg: {
                props: {
                    onClick: () => void;
                };
                item: any;
                i: number;
            }) => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:day"?: false | ((arg: {
            props: {
                onClick: () => void;
            };
            item: any;
            i: number;
        }) => import("vue").VNodeChild) | undefined;
    } & {
        "onUpdate:modelValue"?: ((date: unknown) => any) | undefined;
        "onUpdate:month"?: ((date: number) => any) | undefined;
        "onUpdate:year"?: ((date: number) => any) | undefined;
    }, {}, {}, {}, {}, {
        transition: string;
        disabled: boolean;
        reverseTransition: string;
        showAdjacentMonths: boolean;
        weekdays: import("../../composables/calendar.js").CalendarWeekdays[];
        weeksInMonth: "static" | "dynamic";
        firstDayOfWeek: string | number;
        hideWeekdays: boolean;
        showWeek: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    transition: string;
    disabled: boolean;
    reverseTransition: string;
    showAdjacentMonths: boolean;
    weekdays: import("../../composables/calendar.js").CalendarWeekdays[];
    weeksInMonth: "static" | "dynamic";
    hideWeekdays: boolean;
    showWeek: boolean;
} & {
    max?: unknown;
    min?: unknown;
    color?: string | undefined;
    multiple?: number | boolean | (string & {}) | "range" | undefined;
    month?: string | number | undefined;
    year?: string | number | undefined;
    modelValue?: unknown[] | undefined;
    firstDayOfWeek?: string | number | undefined;
    allowedDates?: unknown[] | ((date: unknown) => boolean) | undefined;
} & {
    $children?: {} | import("vue").VNodeChild | {
        day?: ((arg: {
            props: {
                onClick: () => void;
            };
            item: any;
            i: number;
        }) => import("vue").VNodeChild) | undefined;
    };
    'v-slots'?: {
        day?: false | ((arg: {
            props: {
                onClick: () => void;
            };
            item: any;
            i: number;
        }) => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:day"?: false | ((arg: {
        props: {
            onClick: () => void;
        };
        item: any;
        i: number;
    }) => import("vue").VNodeChild) | undefined;
} & {
    "onUpdate:modelValue"?: ((date: unknown) => any) | undefined;
    "onUpdate:month"?: ((date: number) => any) | undefined;
    "onUpdate:year"?: ((date: number) => any) | undefined;
}, void, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:modelValue': (date: unknown) => true;
    'update:month': (date: number) => true;
    'update:year': (date: number) => true;
}, string, {
    transition: string;
    disabled: boolean;
    reverseTransition: string;
    showAdjacentMonths: boolean;
    weekdays: import("../../composables/calendar.js").CalendarWeekdays[];
    weeksInMonth: "static" | "dynamic";
    firstDayOfWeek: string | number;
    hideWeekdays: boolean;
    showWeek: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    day: (arg: {
        props: {
            onClick: () => void;
        };
        item: any;
        i: number;
    }) => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    max: PropType<unknown>;
    min: PropType<unknown>;
    disabled: {
        type: BooleanConstructor;
        default: null;
    };
    month: (StringConstructor | NumberConstructor)[];
    year: (StringConstructor | NumberConstructor)[];
    modelValue: PropType<unknown[]>;
    showAdjacentMonths: BooleanConstructor;
    weekdays: {
        type: PropType<import("../../composables/calendar.js").CalendarWeekdays[]>;
        default: () => number[];
    };
    weeksInMonth: {
        type: PropType<"dynamic" | "static">;
        default: string;
    };
    firstDayOfWeek: {
        type: (StringConstructor | NumberConstructor)[];
        default: undefined;
    };
    allowedDates: PropType<unknown[] | ((date: unknown) => boolean)>;
    color: StringConstructor;
    hideWeekdays: BooleanConstructor;
    multiple: PropType<boolean | "range" | number | (string & {})>;
    showWeek: BooleanConstructor;
    transition: {
        type: StringConstructor;
        default: string;
    };
    reverseTransition: {
        type: StringConstructor;
        default: string;
    };
}, import("vue").ExtractPropTypes<{
    max: PropType<unknown>;
    min: PropType<unknown>;
    disabled: {
        type: BooleanConstructor;
        default: null;
    };
    month: (StringConstructor | NumberConstructor)[];
    year: (StringConstructor | NumberConstructor)[];
    modelValue: PropType<unknown[]>;
    showAdjacentMonths: BooleanConstructor;
    weekdays: {
        type: PropType<import("../../composables/calendar.js").CalendarWeekdays[]>;
        default: () => number[];
    };
    weeksInMonth: {
        type: PropType<"dynamic" | "static">;
        default: string;
    };
    firstDayOfWeek: {
        type: (StringConstructor | NumberConstructor)[];
        default: undefined;
    };
    allowedDates: PropType<unknown[] | ((date: unknown) => boolean)>;
    color: StringConstructor;
    hideWeekdays: BooleanConstructor;
    multiple: PropType<boolean | "range" | number | (string & {})>;
    showWeek: BooleanConstructor;
    transition: {
        type: StringConstructor;
        default: string;
    };
    reverseTransition: {
        type: StringConstructor;
        default: string;
    };
}>>;
export type VDatePickerMonth = InstanceType<typeof VDatePickerMonth>;
