<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\InsertDataPerformanceProxiesSchedule::class,
        Commands\ScheduleInsertDataSLAforCS::class,
        Commands\ScheduleInsertDataSLAforITCoord::class,
        Commands\ScheduleInsertDataSLAforITSpec::class,
        // Commands\ScheduleInsertDataSLAforMitel::class,
        Commands\ScheduleInsertDataSLAforITServ::class,
        Commands\ScheduleInsertDataSLAforITSeverity::class,
        Commands\ScheduleInsertDataSLAforInternalFactor::class,
        Commands\ScheduleInsertDataSLAforITByApprover::class,
        Commands\ScheduleInsertNagiosHostStatus::class,
        Commands\ScheduleInsertDataSLAforAspect::class,
        // Commands\SchedulerAlertProxyPerformance::class,
        Commands\ScheduleInsertITServiceRequest::class,
        Commands\ScheduleInsertTalkdeskData::class,
        Commands\ScheduleInsertTalkdeskDataByReportFile::class,
        Commands\ScheduleInsertNagiosHostDownStatus::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('insert-data-performance-proxy')->dailyAt('05:00');
        $schedule->command('insert-data-cs-sla')->dailyAt('01:00');
        $schedule->command('insert-data-itcoord-sla')->dailyAt('01:00');
        $schedule->command('insert-data-itspec-sla')->dailyAt('01:00');
        // $schedule->command('insert-data-mitel-sla')->dailyAt('01:00');
        // $schedule->command('insert-data-aspect-sla')->dailyAt('01:00');
        $schedule->command('insert-data-itseverity-sla')->dailyAt('01:00');
        $schedule->command('insert-data-itserv-sla')->dailyAt('01:00');
        $schedule->command('insert-data-internalfac-sla')->dailyAt('01:00');
        $schedule->command('insert-data-itbyapprover-sla')->dailyAt('01:00');
        // $schedule->command('insert-data-nagios-raw')->everyTenMinutes();
        $schedule->command('insert-data-nagios-service-current')->everyTenMinutes();
        $schedule->command('report-scheduler-sla')->everyMinute();
        $schedule->command('insert-itservice-request')->dailyAt('01:00');
        // $schedule->command('insert-talkdesk-data')->dailyAt('01:00');
        $schedule->command('check-file-talkdesk')->dailyAt('07:30');
        $schedule->command('insert-data-nagios-down')->everyMinute();
        
        // Generate and email monthly SLA report on the 1st of each month at 7:30 AM
        $schedule->command('sla:generate-monthly-report')->monthlyOn(1, '07:30');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
