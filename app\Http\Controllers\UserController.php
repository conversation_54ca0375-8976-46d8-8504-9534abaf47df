<?php

namespace App\Http\Controllers;

use App\ActionLog;
use App\Services\PomsService;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $users = User::all();
        return response()->json(
            [
                'status' => 'success',
                'users' => $users->toArray()
            ],
            200
        );
    }

    public function show(Request $request, $id)
    {
        $user = User::find($id);
        return response()->json(
            [
                'status' => 'success',
                'user' => $user->toArray()
            ],
            200
        );
    }

    public function userList()
    {
        $users = User::all();
        $userList = $users->toArray();
        if ($userList) {
            foreach ($userList as &$user) {
                $user['role'] = json_decode($user['role']);
                // $user['role_desc'] = json_decode($user['role']);
            }
        }
        return response()->json(
            [
                'status' => 'success',
                'users' => $userList
            ],
            200
        );
    }

    public function newUser(Request $request)
    {
        try {
            $req = $request->params;
            $v = Validator::make($req, [
                'username' => 'required|unique:users',
                'password' => 'required|min:3|confirmed',
            ]);
            if ($v->fails()) {
                return response()->json([
                    'status' => 'error',
                    'errors' => $v->errors()
                ], 422);
            }
            $user = new User;
            $user->username = $req['username'];
            $user->name = $req['name'];
            $user->email = $req['email'];
            $user->active = $req['active'];
            $user->password = bcrypt($req['password']);
            $user->role = json_encode($req['role']);
            $user->save();
            $this->handleEmail($user->id, $req['password'], 'registration');
            return response()->json(['status' => 'success'], 200);
        } catch (\Exception $e) {
            Log::error('Error creating new user: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create new user. Please try again later.'
            ], 500);
        }
    }

    public function update(Request $request)
    {
        $req = $request->params;
        $v = Validator::make($req, [
            'id' => 'required',
            'name' => 'required',
            'email' => 'required',
            'role' => 'required',
        ]);
        if ($v->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $v->errors()
            ], 422);
        }
        $user = User::find($req['id']);
        $user->name = $req['name'];
        $user->email = $req['email'];
        $user->active = $req['active'];
        $user->role = json_encode($req['role']);
        $user->save();
        return response()->json(['status' => 'success'], 200);
    }

    public function updatePassword(Request $request)
    {
        $req = $request->params;
        $v = Validator::make($req, [
            'id' => 'required',
            'password' => 'required|min:3|confirmed',
        ]);
        if ($v->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $v->errors()
            ], 422);
        }
        $user = User::find($req['id']);
        $user->password = bcrypt($req['password']);
        $user->save();

        $this->handleEmail($user->id, $req['password'], 'password_reset');

        return response()->json(['status' => 'success'], 200);
    }

    public function resetPassword(Request $request)
    {
        $req = $request->all();
        if (empty($req)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid request'
            ], 400);
        }

        $v = Validator::make($req, [
            'user_id' => 'required|exists:users,id',
            'old_password' => 'required',
            'new_password' => 'required|min:3',
            'new_password_confirm' => 'required|same:new_password',
        ]);

        if ($v->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $v->errors()
            ], 422);
        }

        $user = User::find($req['user_id']);

        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'User not found'
            ], 404);
        }

        if (!Hash::check($req['old_password'], $user->password)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Existing password does not match, please try again.'
            ], 422);
        }

        $user->password = bcrypt($req['new_password']);
        $user->save();

        $logData = $req;
        $logData['old_password'] = '********';
        $logData['new_password'] = '********';
        $logData['new_password_confirm'] = '********';
        ActionLog::saveActionLog('resetPassword', $logData, 'success');

        $this->handleEmail($user->id, $req['new_password'], 'password_reset');

        return response()->json([
            'status' => 'success',
            'message' => 'Password reset successful, please login with new password.'
        ], 200);
    }

    public function editAvatar(Request $request)
    {
        $req = $request->all();
        $v = Validator::make($req, [
            'id' => 'required',
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:1024', // Set max file size to 1MB
        ]);
        if ($v->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $v->errors()
            ], 422);
        }
        $user = User::find($req['id']);

        // Store the avatar file
        $avatarFile = $req['avatar'];
        $avatarFileName = time() . '_' . $user->id;
        $avatarFile->move(public_path('images/avatars'), $avatarFileName);

        $user->avatar = $avatarFileName;
        $user->save();
        return response()->json(['status' => 'success'], 200);
    }


    /**
     * Handle registration or reset password email.
     *
     * @param  int  $userId
     * @param  string  $password
     * @param  string  $type
     * @return void
     */
    protected function handleEmail($userId, $password, $type)
    {
        try {
            $user = User::find($userId);

            // Send email to the user
            $to = $user->email;
            $subject = ($type === 'registration') ? "Registration Successful" : "Password Reset Successful";
            $data = [
                'user' => $user,
                'password' => $password
            ];
            $view = ($type === 'registration') ? 'emails.registration' : 'emails.password_reset';
            Mail::send($view, $data, function($message) use ($to, $subject) {
                $message->from('<EMAIL>', 'POMS');
                $message->to($to)->subject($subject);
            });
        } catch (\Exception $e) {
            Log::error('Error sending email: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to send email. Please try again later.');
        }
    }

}
