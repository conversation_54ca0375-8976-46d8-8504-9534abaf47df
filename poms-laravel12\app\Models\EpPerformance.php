<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EpPerformance extends Model
{
    protected $connection = 'mysql';
    protected $table = 'performance_proxies';

    protected $fillable = [
        'proxy_name',
        'response_time',
        'status',
        'checked_at',
    ];

    protected function casts(): array
    {
        return [
            'response_time' => 'float',
            'checked_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }
}
