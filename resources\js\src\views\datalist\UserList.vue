<template>

    <div class="vx-col">
        <vs-breadcrumb :items="breadcrumbs" separator="chevron_right"></vs-breadcrumb>
        <data-view-sidebar :isSidebarActive="addNewDataSidebar" @closeSidebar="toggleDataSidebar" :data="sidebarData" />
        <vx-card title="User List">
            <div class="mt-4">
                <vs-table max-items="10" pagination search :data="users" stripe>

                    <div slot="header" class="flex flex-wrap-reverse items-center flex-grow justify-between">

                        <div class="flex flex-wrap-reverse items-center">

                            <!-- ADD NEW -->
                            <vs-button class="pl-4 pr-4" color="primary" type="filled" icon-pack="feather" icon="icon-plus" @click="addNewData">Add New</vs-button>
                            <!-- <div class="btn-add-new p-3 mb-4 mr-4 rounded-lg cursor-pointer flex items-center justify-center text-lg font-medium text-base text-primary border border-solid border-primary" @click="addNewData">
                                <feather-icon icon="PlusIcon" svgClasses="h-4 w-4" />
                                <span class="ml-2 text-base text-primary">Add New</span>
                            </div> -->
                        </div>
                    </div>

                    <template slot="thead">
                        <vs-th width="20%">Name</vs-th>
                        <vs-th>Username</vs-th>
                        <vs-th>Email</vs-th>
                        <vs-th>Active</vs-th>
                        <vs-th>Created Date</vs-th>
                        <vs-th>Modified Date</vs-th>
                        <vs-th>Role</vs-th>
                        <vs-th>Action</vs-th>
                    </template>

                    <template slot-scope="{data}">
                        <vs-tr :key="indextr" v-for="(tr, indextr) in data">

                            <vs-td :data="tr.name">
                                {{ tr.name }}
                            </vs-td>

                            <vs-td :data="tr.username">
                                {{ tr.username }}
                            </vs-td>

                            <vs-td :data="tr.email">
                                {{ tr.email }}
                            </vs-td>

                            <vs-td :data="tr.active">
                                <span :class="{'text-success': tr.active === 1, 'text-danger': tr.active === 0}">
                                    {{ tr.active === 1 ? 'Active' : 'Inactive' }}
                                </span>
                            </vs-td>

                            <vs-td :data="tr.created_at">
                                {{ tr.created_at }}
                            </vs-td>

                            <vs-td :data="tr.updated_at">
                                {{ tr.updated_at }}
                            </vs-td>
                            <vs-td :data="tr.role">
                                <vs-list>
                                    <vs-list-item v-if="containsKey(tr.role, '1')" title="Administrator"></vs-list-item>
                                    <vs-list-item v-if="containsKey(tr.role, '2')" title="SLA Dashboard"></vs-list-item>
                                    <vs-list-item v-if="containsKey(tr.role, '3')" title="SLA Report"></vs-list-item>
                                    <vs-list-item v-if="containsKey(tr.role, '4')" title="Technical Dashboard"></vs-list-item>
                                    <vs-list-item v-if="containsKey(tr.role, '5')" title="Inventory Report"></vs-list-item>
                                </vs-list>

                            </vs-td>
                            <vs-td>
                                <feather-icon icon="EditIcon" svgClasses="w-5 h-5 hover:text-primary stroke-current" @click.stop="editData(tr)" />
                                <!-- <feather-icon icon="TrashIcon" svgClasses="w-5 h-5 hover:text-danger stroke-current" class="ml-2" @click.stop="deleteData(tr.id)" /> -->
                            </vs-td>

                        </vs-tr>
                    </template>
                </vs-table>
            </div>
        </vx-card>
    </div>
</template>

<script>
    import DataViewSidebar from './DataViewSidebar.vue'
    import axios from 'axios'

    export default {
        components: {
            DataViewSidebar
        },
        data() {
            return {
                users: [],
                addNewDataSidebar: false,
                sidebarData: {},
                breadcrumbs: [
                    {
                        title: 'Administrator',
                        url: '/admin'
                    },
                    {
                        title: 'User Management',
                        active: true
                    },
                ]
            }
        },
        mounted() {
            const vm = this;
            // vm.fetchUserList();
            vm.displayUsers();
        },
        methods: {
            fetchUserList: function () {
                this.$http({
                    url: `users`,
                    method: 'GET'
                })
                    .then((res) => {
                        if(res.data.status === 'success'){
                            this.users = res.data.users
                        }
                        this.$vs.loading.close();
                    })
                    .catch(err => console.log(err));

            },

            async displayUsers() {
                axios.defaults.headers.common['Authorization'] = `Bearer ${await localStorage.getItem("accessToken")}`
                axios.get('api/users')
                    .then(response => {
                        this.users = response.data.users
                    })
                    .catch(error => {
                        // handle authentication and validation errors here
                        err => console.log(err)
                    })
            },
            addNewData() {
                this.sidebarData = {}
                this.toggleDataSidebar(true)
            },
            deleteData(id) {
                this.$store.dispatch("dataList/removeItem", id).catch(err => { console.error(err) })
            },
            editData(data) {
                // this.sidebarData = JSON.parse(JSON.stringify(this.blankData))
                this.sidebarData = data
                this.toggleDataSidebar(true)
            },
            toggleDataSidebar(val=false) {
                this.addNewDataSidebar = val
            },
            containsKey(obj, key) {
                return obj.includes(key);
            },
            // contains(arrString, string) {
            //     return jQuery.inArray(string, arrString) !== -1;
            // }
            checkInArray(needle, haystack) {
                return haystack.includes(needle)
            }
        }
    }
</script>

<style lang="scss">
    #data-list-list-view {
        .vs-con-table {

            /*
              Below media-queries is fix for responsiveness of action buttons
              Note: If you change action buttons or layout of this page, Please remove below style
            */
            @media (max-width: 689px) {
                .vs-table--search {
                    margin-left: 0;
                    max-width: unset;
                    width: 100%;

                    .vs-table--search-input {
                        width: 100%;
                    }
                }
            }

            @media (max-width: 461px) {
                .items-per-page-handler {
                    display: none;
                }
            }

            @media (max-width: 341px) {
                .data-list-btn-container {
                    width: 100%;

                    .dd-actions,
                    .btn-add-new {
                        width: 100%;
                        margin-right: 0 !important;
                    }
                }
            }

            .product-name {
                max-width: 23rem;
            }

            .vs-table--header {
                display: flex;
                flex-wrap: wrap;
                margin-left: 1.5rem;
                margin-right: 1.5rem;
                > span {
                    display: flex;
                    flex-grow: 1;
                }

                .vs-table--search{
                    padding-top: 0;

                    .vs-table--search-input {
                        padding: 0.9rem 2.5rem;
                        font-size: 1rem;

                        &+i {
                            left: 1rem;
                        }

                        &:focus+i {
                            left: 1rem;
                        }
                    }
                }
            }

            .vs-table {
                border-collapse: separate;
                border-spacing: 0 1.3rem;
                padding: 0 1rem;

                tr{
                    box-shadow: 0 4px 20px 0 rgba(0,0,0,.05);
                    td{
                        padding: 20px;
                        &:first-child{
                            border-top-left-radius: .5rem;
                            border-bottom-left-radius: .5rem;
                        }
                        &:last-child{
                            border-top-right-radius: .5rem;
                            border-bottom-right-radius: .5rem;
                        }
                    }
                    td.td-check{
                        padding: 20px !important;
                    }
                }
            }

            .vs-table--thead{
                th {
                    padding-top: 0;
                    padding-bottom: 0;

                    .vs-table-text{
                        text-transform: uppercase;
                        font-weight: 600;
                    }
                }
                th.td-check{
                    padding: 0 15px !important;
                }
                tr{
                    background: none;
                    box-shadow: none;
                }
            }

            .vs-table--pagination {
                justify-content: center;
            }
        }
    }
</style>
