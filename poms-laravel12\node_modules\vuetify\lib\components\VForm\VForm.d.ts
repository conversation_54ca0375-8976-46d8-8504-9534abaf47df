import type { SubmitEventPromise } from "../../composables/form.js";
export declare const makeVFormProps: <Defaults extends {
    disabled?: unknown;
    fastFail?: unknown;
    readonly?: unknown;
    modelValue?: unknown;
    validateOn?: unknown;
    class?: unknown;
    style?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    disabled: unknown extends Defaults["disabled"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["disabled"] ? boolean : boolean | Defaults["disabled"]>;
        default: unknown extends Defaults["disabled"] ? boolean : boolean | Defaults["disabled"];
    };
    fastFail: unknown extends Defaults["fastFail"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["fastFail"] ? boolean : boolean | Defaults["fastFail"]>;
        default: unknown extends Defaults["fastFail"] ? boolean : boolean | Defaults["fastFail"];
    };
    readonly: unknown extends Defaults["readonly"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["readonly"] ? boolean : boolean | Defaults["readonly"]>;
        default: unknown extends Defaults["readonly"] ? boolean : boolean | Defaults["readonly"];
    };
    modelValue: unknown extends Defaults["modelValue"] ? {
        type: import("vue").PropType<boolean | null>;
        default: null;
    } : Omit<{
        type: import("vue").PropType<boolean | null>;
        default: null;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["modelValue"] ? boolean | null : boolean | Defaults["modelValue"] | null>;
        default: unknown extends Defaults["modelValue"] ? boolean | null : NonNullable<boolean | null> | Defaults["modelValue"];
    };
    validateOn: unknown extends Defaults["validateOn"] ? {
        type: import("vue").PropType<import("../../composables/form.js").FormProps["validateOn"]>;
        default: string;
    } : Omit<{
        type: import("vue").PropType<import("../../composables/form.js").FormProps["validateOn"]>;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["validateOn"] ? ("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined : ("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | Defaults["validateOn"] | undefined>;
        default: unknown extends Defaults["validateOn"] ? ("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined : NonNullable<("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined> | Defaults["validateOn"];
    };
    class: unknown extends Defaults["class"] ? import("vue").PropType<any> : {
        type: import("vue").PropType<unknown extends Defaults["class"] ? any : any>;
        default: unknown extends Defaults["class"] ? any : any;
    };
    style: unknown extends Defaults["style"] ? {
        type: import("vue").PropType<import("vue").StyleValue>;
        default: null;
    } : Omit<{
        type: import("vue").PropType<import("vue").StyleValue>;
        default: null;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["style"] ? import("vue").StyleValue : import("vue").StyleValue | Defaults["style"]>;
        default: unknown extends Defaults["style"] ? import("vue").StyleValue : NonNullable<import("vue").StyleValue> | Defaults["style"];
    };
};
export declare const VForm: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        style: import("vue").StyleValue;
        disabled: boolean;
        readonly: boolean;
        modelValue: boolean | null;
        validateOn: ("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined;
        fastFail: boolean;
    } & {
        class?: any;
    } & {
        $children?: import("vue").VNodeChild | {
            default?: ((arg: {
                errors: import("vue").Ref<{
                    id: number | string;
                    errorMessages: string[];
                }[], import("../../composables/form.js").FieldValidationResult[] | {
                    id: number | string;
                    errorMessages: string[];
                }[]>;
                isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
                isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
                isValidating: import("vue").ShallowRef<boolean, boolean>;
                isValid: import("vue").Ref<boolean | null, boolean | null> & {
                    readonly externalValue: boolean | null;
                };
                items: import("vue").Ref<{
                    id: number | string;
                    validate: () => Promise<string[]>;
                    reset: () => Promise<void>;
                    resetValidation: () => Promise<void>;
                    vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                    isValid: boolean | null;
                    errorMessages: string[];
                }[], import("../../composables/form.js").FormField[] | {
                    id: number | string;
                    validate: () => Promise<string[]>;
                    reset: () => Promise<void>;
                    resetValidation: () => Promise<void>;
                    vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                    isValid: boolean | null;
                    errorMessages: string[];
                }[]>;
                validate: () => Promise<{
                    valid: boolean;
                    errors: {
                        id: number | string;
                        errorMessages: string[];
                    }[];
                }>;
                reset: () => void;
                resetValidation: () => void;
            }) => import("vue").VNodeChild) | undefined;
        } | ((arg: {
            errors: import("vue").Ref<{
                id: number | string;
                errorMessages: string[];
            }[], import("../../composables/form.js").FieldValidationResult[] | {
                id: number | string;
                errorMessages: string[];
            }[]>;
            isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
            isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
            isValidating: import("vue").ShallowRef<boolean, boolean>;
            isValid: import("vue").Ref<boolean | null, boolean | null> & {
                readonly externalValue: boolean | null;
            };
            items: import("vue").Ref<{
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[], import("../../composables/form.js").FormField[] | {
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[]>;
            validate: () => Promise<{
                valid: boolean;
                errors: {
                    id: number | string;
                    errorMessages: string[];
                }[];
            }>;
            reset: () => void;
            resetValidation: () => void;
        }) => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | ((arg: {
                errors: import("vue").Ref<{
                    id: number | string;
                    errorMessages: string[];
                }[], import("../../composables/form.js").FieldValidationResult[] | {
                    id: number | string;
                    errorMessages: string[];
                }[]>;
                isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
                isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
                isValidating: import("vue").ShallowRef<boolean, boolean>;
                isValid: import("vue").Ref<boolean | null, boolean | null> & {
                    readonly externalValue: boolean | null;
                };
                items: import("vue").Ref<{
                    id: number | string;
                    validate: () => Promise<string[]>;
                    reset: () => Promise<void>;
                    resetValidation: () => Promise<void>;
                    vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                    isValid: boolean | null;
                    errorMessages: string[];
                }[], import("../../composables/form.js").FormField[] | {
                    id: number | string;
                    validate: () => Promise<string[]>;
                    reset: () => Promise<void>;
                    resetValidation: () => Promise<void>;
                    vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                    isValid: boolean | null;
                    errorMessages: string[];
                }[]>;
                validate: () => Promise<{
                    valid: boolean;
                    errors: {
                        id: number | string;
                        errorMessages: string[];
                    }[];
                }>;
                reset: () => void;
                resetValidation: () => void;
            }) => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | ((arg: {
            errors: import("vue").Ref<{
                id: number | string;
                errorMessages: string[];
            }[], import("../../composables/form.js").FieldValidationResult[] | {
                id: number | string;
                errorMessages: string[];
            }[]>;
            isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
            isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
            isValidating: import("vue").ShallowRef<boolean, boolean>;
            isValid: import("vue").Ref<boolean | null, boolean | null> & {
                readonly externalValue: boolean | null;
            };
            items: import("vue").Ref<{
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[], import("../../composables/form.js").FormField[] | {
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[]>;
            validate: () => Promise<{
                valid: boolean;
                errors: {
                    id: number | string;
                    errorMessages: string[];
                }[];
            }>;
            reset: () => void;
            resetValidation: () => void;
        }) => import("vue").VNodeChild) | undefined;
    } & {
        onSubmit?: ((e: SubmitEventPromise) => any) | undefined;
        "onUpdate:modelValue"?: ((val: boolean | null) => any) | undefined;
    }, {
        errors: import("vue").Ref<{
            id: number | string;
            errorMessages: string[];
        }[], import("../../composables/form.js").FieldValidationResult[] | {
            id: number | string;
            errorMessages: string[];
        }[]>;
        isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
        isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
        isValidating: import("vue").ShallowRef<boolean, boolean>;
        isValid: import("vue").Ref<boolean | null, boolean | null> & {
            readonly externalValue: boolean | null;
        };
        items: import("vue").Ref<{
            id: number | string;
            validate: () => Promise<string[]>;
            reset: () => Promise<void>;
            resetValidation: () => Promise<void>;
            vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
            isValid: boolean | null;
            errorMessages: string[];
        }[], import("../../composables/form.js").FormField[] | {
            id: number | string;
            validate: () => Promise<string[]>;
            reset: () => Promise<void>;
            resetValidation: () => Promise<void>;
            vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
            isValid: boolean | null;
            errorMessages: string[];
        }[]>;
        validate: () => Promise<{
            valid: boolean;
            errors: {
                id: number | string;
                errorMessages: string[];
            }[];
        }>;
        reset: () => void;
        resetValidation: () => void;
    } & HTMLFormElement & {
        _allExposed: {
            errors: import("vue").Ref<{
                id: number | string;
                errorMessages: string[];
            }[], import("../../composables/form.js").FieldValidationResult[] | {
                id: number | string;
                errorMessages: string[];
            }[]>;
            isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
            isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
            isValidating: import("vue").ShallowRef<boolean, boolean>;
            isValid: import("vue").Ref<boolean | null, boolean | null> & {
                readonly externalValue: boolean | null;
            };
            items: import("vue").Ref<{
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[], import("../../composables/form.js").FormField[] | {
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[]>;
            validate: () => Promise<{
                valid: boolean;
                errors: {
                    id: number | string;
                    errorMessages: string[];
                }[];
            }>;
            reset: () => void;
            resetValidation: () => void;
        };
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        'update:modelValue': (val: boolean | null) => true;
        submit: (e: SubmitEventPromise) => true;
    }, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        style: import("vue").StyleValue;
        disabled: boolean;
        readonly: boolean;
        modelValue: boolean | null;
        validateOn: ("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined;
        fastFail: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: (arg: {
            errors: import("vue").Ref<{
                id: number | string;
                errorMessages: string[];
            }[], import("../../composables/form.js").FieldValidationResult[] | {
                id: number | string;
                errorMessages: string[];
            }[]>;
            isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
            isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
            isValidating: import("vue").ShallowRef<boolean, boolean>;
            isValid: import("vue").Ref<boolean | null, boolean | null> & {
                readonly externalValue: boolean | null;
            };
            items: import("vue").Ref<{
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[], import("../../composables/form.js").FormField[] | {
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[]>;
            validate: () => Promise<{
                valid: boolean;
                errors: {
                    id: number | string;
                    errorMessages: string[];
                }[];
            }>;
            reset: () => void;
            resetValidation: () => void;
        }) => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        style: import("vue").StyleValue;
        disabled: boolean;
        readonly: boolean;
        modelValue: boolean | null;
        validateOn: ("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined;
        fastFail: boolean;
    } & {
        class?: any;
    } & {
        $children?: import("vue").VNodeChild | {
            default?: ((arg: {
                errors: import("vue").Ref<{
                    id: number | string;
                    errorMessages: string[];
                }[], import("../../composables/form.js").FieldValidationResult[] | {
                    id: number | string;
                    errorMessages: string[];
                }[]>;
                isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
                isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
                isValidating: import("vue").ShallowRef<boolean, boolean>;
                isValid: import("vue").Ref<boolean | null, boolean | null> & {
                    readonly externalValue: boolean | null;
                };
                items: import("vue").Ref<{
                    id: number | string;
                    validate: () => Promise<string[]>;
                    reset: () => Promise<void>;
                    resetValidation: () => Promise<void>;
                    vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                    isValid: boolean | null;
                    errorMessages: string[];
                }[], import("../../composables/form.js").FormField[] | {
                    id: number | string;
                    validate: () => Promise<string[]>;
                    reset: () => Promise<void>;
                    resetValidation: () => Promise<void>;
                    vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                    isValid: boolean | null;
                    errorMessages: string[];
                }[]>;
                validate: () => Promise<{
                    valid: boolean;
                    errors: {
                        id: number | string;
                        errorMessages: string[];
                    }[];
                }>;
                reset: () => void;
                resetValidation: () => void;
            }) => import("vue").VNodeChild) | undefined;
        } | ((arg: {
            errors: import("vue").Ref<{
                id: number | string;
                errorMessages: string[];
            }[], import("../../composables/form.js").FieldValidationResult[] | {
                id: number | string;
                errorMessages: string[];
            }[]>;
            isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
            isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
            isValidating: import("vue").ShallowRef<boolean, boolean>;
            isValid: import("vue").Ref<boolean | null, boolean | null> & {
                readonly externalValue: boolean | null;
            };
            items: import("vue").Ref<{
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[], import("../../composables/form.js").FormField[] | {
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[]>;
            validate: () => Promise<{
                valid: boolean;
                errors: {
                    id: number | string;
                    errorMessages: string[];
                }[];
            }>;
            reset: () => void;
            resetValidation: () => void;
        }) => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | ((arg: {
                errors: import("vue").Ref<{
                    id: number | string;
                    errorMessages: string[];
                }[], import("../../composables/form.js").FieldValidationResult[] | {
                    id: number | string;
                    errorMessages: string[];
                }[]>;
                isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
                isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
                isValidating: import("vue").ShallowRef<boolean, boolean>;
                isValid: import("vue").Ref<boolean | null, boolean | null> & {
                    readonly externalValue: boolean | null;
                };
                items: import("vue").Ref<{
                    id: number | string;
                    validate: () => Promise<string[]>;
                    reset: () => Promise<void>;
                    resetValidation: () => Promise<void>;
                    vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                    isValid: boolean | null;
                    errorMessages: string[];
                }[], import("../../composables/form.js").FormField[] | {
                    id: number | string;
                    validate: () => Promise<string[]>;
                    reset: () => Promise<void>;
                    resetValidation: () => Promise<void>;
                    vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                    isValid: boolean | null;
                    errorMessages: string[];
                }[]>;
                validate: () => Promise<{
                    valid: boolean;
                    errors: {
                        id: number | string;
                        errorMessages: string[];
                    }[];
                }>;
                reset: () => void;
                resetValidation: () => void;
            }) => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | ((arg: {
            errors: import("vue").Ref<{
                id: number | string;
                errorMessages: string[];
            }[], import("../../composables/form.js").FieldValidationResult[] | {
                id: number | string;
                errorMessages: string[];
            }[]>;
            isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
            isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
            isValidating: import("vue").ShallowRef<boolean, boolean>;
            isValid: import("vue").Ref<boolean | null, boolean | null> & {
                readonly externalValue: boolean | null;
            };
            items: import("vue").Ref<{
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[], import("../../composables/form.js").FormField[] | {
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[]>;
            validate: () => Promise<{
                valid: boolean;
                errors: {
                    id: number | string;
                    errorMessages: string[];
                }[];
            }>;
            reset: () => void;
            resetValidation: () => void;
        }) => import("vue").VNodeChild) | undefined;
    } & {
        onSubmit?: ((e: SubmitEventPromise) => any) | undefined;
        "onUpdate:modelValue"?: ((val: boolean | null) => any) | undefined;
    }, {
        errors: import("vue").Ref<{
            id: number | string;
            errorMessages: string[];
        }[], import("../../composables/form.js").FieldValidationResult[] | {
            id: number | string;
            errorMessages: string[];
        }[]>;
        isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
        isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
        isValidating: import("vue").ShallowRef<boolean, boolean>;
        isValid: import("vue").Ref<boolean | null, boolean | null> & {
            readonly externalValue: boolean | null;
        };
        items: import("vue").Ref<{
            id: number | string;
            validate: () => Promise<string[]>;
            reset: () => Promise<void>;
            resetValidation: () => Promise<void>;
            vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
            isValid: boolean | null;
            errorMessages: string[];
        }[], import("../../composables/form.js").FormField[] | {
            id: number | string;
            validate: () => Promise<string[]>;
            reset: () => Promise<void>;
            resetValidation: () => Promise<void>;
            vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
            isValid: boolean | null;
            errorMessages: string[];
        }[]>;
        validate: () => Promise<{
            valid: boolean;
            errors: {
                id: number | string;
                errorMessages: string[];
            }[];
        }>;
        reset: () => void;
        resetValidation: () => void;
    } & HTMLFormElement & {
        _allExposed: {
            errors: import("vue").Ref<{
                id: number | string;
                errorMessages: string[];
            }[], import("../../composables/form.js").FieldValidationResult[] | {
                id: number | string;
                errorMessages: string[];
            }[]>;
            isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
            isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
            isValidating: import("vue").ShallowRef<boolean, boolean>;
            isValid: import("vue").Ref<boolean | null, boolean | null> & {
                readonly externalValue: boolean | null;
            };
            items: import("vue").Ref<{
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[], import("../../composables/form.js").FormField[] | {
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[]>;
            validate: () => Promise<{
                valid: boolean;
                errors: {
                    id: number | string;
                    errorMessages: string[];
                }[];
            }>;
            reset: () => void;
            resetValidation: () => void;
        };
    }, {}, {}, {}, {
        style: import("vue").StyleValue;
        disabled: boolean;
        readonly: boolean;
        modelValue: boolean | null;
        validateOn: ("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined;
        fastFail: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    style: import("vue").StyleValue;
    disabled: boolean;
    readonly: boolean;
    modelValue: boolean | null;
    validateOn: ("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined;
    fastFail: boolean;
} & {
    class?: any;
} & {
    $children?: import("vue").VNodeChild | {
        default?: ((arg: {
            errors: import("vue").Ref<{
                id: number | string;
                errorMessages: string[];
            }[], import("../../composables/form.js").FieldValidationResult[] | {
                id: number | string;
                errorMessages: string[];
            }[]>;
            isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
            isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
            isValidating: import("vue").ShallowRef<boolean, boolean>;
            isValid: import("vue").Ref<boolean | null, boolean | null> & {
                readonly externalValue: boolean | null;
            };
            items: import("vue").Ref<{
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[], import("../../composables/form.js").FormField[] | {
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[]>;
            validate: () => Promise<{
                valid: boolean;
                errors: {
                    id: number | string;
                    errorMessages: string[];
                }[];
            }>;
            reset: () => void;
            resetValidation: () => void;
        }) => import("vue").VNodeChild) | undefined;
    } | ((arg: {
        errors: import("vue").Ref<{
            id: number | string;
            errorMessages: string[];
        }[], import("../../composables/form.js").FieldValidationResult[] | {
            id: number | string;
            errorMessages: string[];
        }[]>;
        isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
        isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
        isValidating: import("vue").ShallowRef<boolean, boolean>;
        isValid: import("vue").Ref<boolean | null, boolean | null> & {
            readonly externalValue: boolean | null;
        };
        items: import("vue").Ref<{
            id: number | string;
            validate: () => Promise<string[]>;
            reset: () => Promise<void>;
            resetValidation: () => Promise<void>;
            vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
            isValid: boolean | null;
            errorMessages: string[];
        }[], import("../../composables/form.js").FormField[] | {
            id: number | string;
            validate: () => Promise<string[]>;
            reset: () => Promise<void>;
            resetValidation: () => Promise<void>;
            vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
            isValid: boolean | null;
            errorMessages: string[];
        }[]>;
        validate: () => Promise<{
            valid: boolean;
            errors: {
                id: number | string;
                errorMessages: string[];
            }[];
        }>;
        reset: () => void;
        resetValidation: () => void;
    }) => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | ((arg: {
            errors: import("vue").Ref<{
                id: number | string;
                errorMessages: string[];
            }[], import("../../composables/form.js").FieldValidationResult[] | {
                id: number | string;
                errorMessages: string[];
            }[]>;
            isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
            isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
            isValidating: import("vue").ShallowRef<boolean, boolean>;
            isValid: import("vue").Ref<boolean | null, boolean | null> & {
                readonly externalValue: boolean | null;
            };
            items: import("vue").Ref<{
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[], import("../../composables/form.js").FormField[] | {
                id: number | string;
                validate: () => Promise<string[]>;
                reset: () => Promise<void>;
                resetValidation: () => Promise<void>;
                vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
                isValid: boolean | null;
                errorMessages: string[];
            }[]>;
            validate: () => Promise<{
                valid: boolean;
                errors: {
                    id: number | string;
                    errorMessages: string[];
                }[];
            }>;
            reset: () => void;
            resetValidation: () => void;
        }) => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | ((arg: {
        errors: import("vue").Ref<{
            id: number | string;
            errorMessages: string[];
        }[], import("../../composables/form.js").FieldValidationResult[] | {
            id: number | string;
            errorMessages: string[];
        }[]>;
        isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
        isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
        isValidating: import("vue").ShallowRef<boolean, boolean>;
        isValid: import("vue").Ref<boolean | null, boolean | null> & {
            readonly externalValue: boolean | null;
        };
        items: import("vue").Ref<{
            id: number | string;
            validate: () => Promise<string[]>;
            reset: () => Promise<void>;
            resetValidation: () => Promise<void>;
            vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
            isValid: boolean | null;
            errorMessages: string[];
        }[], import("../../composables/form.js").FormField[] | {
            id: number | string;
            validate: () => Promise<string[]>;
            reset: () => Promise<void>;
            resetValidation: () => Promise<void>;
            vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
            isValid: boolean | null;
            errorMessages: string[];
        }[]>;
        validate: () => Promise<{
            valid: boolean;
            errors: {
                id: number | string;
                errorMessages: string[];
            }[];
        }>;
        reset: () => void;
        resetValidation: () => void;
    }) => import("vue").VNodeChild) | undefined;
} & {
    onSubmit?: ((e: SubmitEventPromise) => any) | undefined;
    "onUpdate:modelValue"?: ((val: boolean | null) => any) | undefined;
}, {
    errors: import("vue").Ref<{
        id: number | string;
        errorMessages: string[];
    }[], import("../../composables/form.js").FieldValidationResult[] | {
        id: number | string;
        errorMessages: string[];
    }[]>;
    isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
    isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
    isValidating: import("vue").ShallowRef<boolean, boolean>;
    isValid: import("vue").Ref<boolean | null, boolean | null> & {
        readonly externalValue: boolean | null;
    };
    items: import("vue").Ref<{
        id: number | string;
        validate: () => Promise<string[]>;
        reset: () => Promise<void>;
        resetValidation: () => Promise<void>;
        vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
        isValid: boolean | null;
        errorMessages: string[];
    }[], import("../../composables/form.js").FormField[] | {
        id: number | string;
        validate: () => Promise<string[]>;
        reset: () => Promise<void>;
        resetValidation: () => Promise<void>;
        vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
        isValid: boolean | null;
        errorMessages: string[];
    }[]>;
    validate: () => Promise<{
        valid: boolean;
        errors: {
            id: number | string;
            errorMessages: string[];
        }[];
    }>;
    reset: () => void;
    resetValidation: () => void;
} & HTMLFormElement & {
    _allExposed: {
        errors: import("vue").Ref<{
            id: number | string;
            errorMessages: string[];
        }[], import("../../composables/form.js").FieldValidationResult[] | {
            id: number | string;
            errorMessages: string[];
        }[]>;
        isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
        isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
        isValidating: import("vue").ShallowRef<boolean, boolean>;
        isValid: import("vue").Ref<boolean | null, boolean | null> & {
            readonly externalValue: boolean | null;
        };
        items: import("vue").Ref<{
            id: number | string;
            validate: () => Promise<string[]>;
            reset: () => Promise<void>;
            resetValidation: () => Promise<void>;
            vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
            isValid: boolean | null;
            errorMessages: string[];
        }[], import("../../composables/form.js").FormField[] | {
            id: number | string;
            validate: () => Promise<string[]>;
            reset: () => Promise<void>;
            resetValidation: () => Promise<void>;
            vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
            isValid: boolean | null;
            errorMessages: string[];
        }[]>;
        validate: () => Promise<{
            valid: boolean;
            errors: {
                id: number | string;
                errorMessages: string[];
            }[];
        }>;
        reset: () => void;
        resetValidation: () => void;
    };
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:modelValue': (val: boolean | null) => true;
    submit: (e: SubmitEventPromise) => true;
}, string, {
    style: import("vue").StyleValue;
    disabled: boolean;
    readonly: boolean;
    modelValue: boolean | null;
    validateOn: ("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined;
    fastFail: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: (arg: {
        errors: import("vue").Ref<{
            id: number | string;
            errorMessages: string[];
        }[], import("../../composables/form.js").FieldValidationResult[] | {
            id: number | string;
            errorMessages: string[];
        }[]>;
        isDisabled: Readonly<import("vue").Ref<boolean, boolean>>;
        isReadonly: Readonly<import("vue").Ref<boolean, boolean>>;
        isValidating: import("vue").ShallowRef<boolean, boolean>;
        isValid: import("vue").Ref<boolean | null, boolean | null> & {
            readonly externalValue: boolean | null;
        };
        items: import("vue").Ref<{
            id: number | string;
            validate: () => Promise<string[]>;
            reset: () => Promise<void>;
            resetValidation: () => Promise<void>;
            vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
            isValid: boolean | null;
            errorMessages: string[];
        }[], import("../../composables/form.js").FormField[] | {
            id: number | string;
            validate: () => Promise<string[]>;
            reset: () => Promise<void>;
            resetValidation: () => Promise<void>;
            vm: import("vue").Raw<import("vue").ComponentInternalInstance>;
            isValid: boolean | null;
            errorMessages: string[];
        }[]>;
        validate: () => Promise<{
            valid: boolean;
            errors: {
                id: number | string;
                errorMessages: string[];
            }[];
        }>;
        reset: () => void;
        resetValidation: () => void;
    }) => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    fastFail: BooleanConstructor;
    readonly: BooleanConstructor;
    modelValue: {
        type: import("vue").PropType<boolean | null>;
        default: null;
    };
    validateOn: {
        type: import("vue").PropType<import("../../composables/form.js").FormProps["validateOn"]>;
        default: string;
    };
    class: import("vue").PropType<import("../../composables/component.js").ClassValue>;
    style: {
        type: import("vue").PropType<import("vue").StyleValue>;
        default: null;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    fastFail: BooleanConstructor;
    readonly: BooleanConstructor;
    modelValue: {
        type: import("vue").PropType<boolean | null>;
        default: null;
    };
    validateOn: {
        type: import("vue").PropType<import("../../composables/form.js").FormProps["validateOn"]>;
        default: string;
    };
    class: import("vue").PropType<import("../../composables/component.js").ClassValue>;
    style: {
        type: import("vue").PropType<import("vue").StyleValue>;
        default: null;
    };
}>>;
export type VForm = InstanceType<typeof VForm>;
