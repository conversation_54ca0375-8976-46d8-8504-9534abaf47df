<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'sqlite' => [
            'driver' => 'sqlite',
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
        ],

        'mysql' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        'mysql_ep_support' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_EP_SUPPORT_HOST', '**************'),
            'port' => env('DB_MYSQL_EP_SUPPORT_PORT', '3306'),
            'database' => env('DB_MYSQL_EP_SUPPORT_DATABASE', 'ep_support'),
            'username' => env('DB_MYSQL_EP_SUPPORT_USERNAME', 'dev_user'),
            'password' => env('DB_MYSQL_EP_SUPPORT_PASSWORD', 'cDc@2019'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_crm' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_HOST', '***************'),
            'port' => env('DB_MYSQL_CRM_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_DATABASE', 'cdccrm'),
            'username' => env('DB_MYSQL_CRM_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_PASSWORD', 'cDccRm@2017'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_inventory' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_INV_HOST', '**************'),
            'port' => env('DB_MYSQL_INV_PORT', '3306'),
            'database' => env('DB_MYSQL_INV_DATABASE', 'snipeit'),
            'username' => env('DB_MYSQL_INV_USERNAME', 'snipe_user'),
            'password' => env('DB_MYSQL_INV_PASSWORD', 'cdcSnipe@2020'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_poms' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST', '*************'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'cdc_poms'),
            'username' => env('DB_USERNAME', 'poms_user'),
            'password' => env('DB_PASSWORD', 'cDcPoms@2019'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_cdccms' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CDCCMS_HOST', '**************'),
            'port' => env('DB_MYSQL_CDCCMS_PORT', '3306'),
            'database' => env('DB_MYSQL_CDCCMS_DATABASE', 'cdccms'),
            'username' => env('DB_MYSQL_CDCCMS_USERNAME', 'epcmsadmin'),
            'password' => env('DB_MYSQL_CDCCMS_PASSWORD', 'cDc@2019'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_helpdesk' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_HELPPDESK_HOST', '*************'),
            'port' => env('DB_MYSQL_HELPPDESK_PORT', '3306'),
            'database' => env('DB_MYSQL_HELPPDESK_DATABASE', 'helpdesk_cdc'),
            'username' => env('DB_MYSQL_HELPPDESK_USERNAME', 'osticket'),
            'password' => env('DB_MYSQL_HELPPDESK_PASSWORD', 'cDc@2020'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'pgsql' => [
            'driver' => 'pgsql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'host' => env('DB_HOST', 'localhost'),
            'port' => env('DB_PORT', '1433'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
        ],

        'sqlsrv_mitel' => [
            'driver' => 'sqlsrv',
            'host' => env('DB_MITEL_HOST', '************'),
            'port' => env('DB_MITEL_PORT', '1433'),
            'database' => env('DB_MITEL_DATABASE', 'CCMData'),
            'username' => env('DB_MITEL_USERNAME', 'middleware-db'),
            'password' => env('DB_MITEL_PASSWORD', 'Cdc@m1ddl3ware'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
        ],

        'sqlsrv_aspect' => [
            'driver' => 'sqlsrv',
            'host' => env('DB_ASPECT_HOST', '************'),
            'port' => env('DB_ASPECT_PORT', '1433'),
            'database' => env('DB_ASPECT_DATABASE', 'detail_epro'),
            'username' => env('DB_ASPECT_USERNAME', 'dbusr'),
            'password' => env('DB_ASPECT_PASSWORD', 'Casb@1234'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
        ],

        'oracle_nextgen_rpt' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_NEXTGEN_RPT_TNS', ''),
            'host'          => env('DB_NEXTGEN_RPT_HOST', 'rac-cluster-scan.eperolehan.com.my'), // stg
            'port'          => env('DB_NEXTGEN_RPT_PORT', '1521'),
            'database'      => env('DB_NEXTGEN_RPT_DATABASE', 'uatapp'),
            'service_name'  => env('DB_NEXTGEN_RPT_DATABASE', 'uatapp'),
            'username'      => env('DB_NEXTGEN_RPT_USERNAME', 'ngep_uat'),
            'password'      => env('DB_NEXTGEN_RPT_PASSWORD', 'ng3p_u4t'),
            'charset'       => env('DB_NEXTGEN_RPT_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_NEXTGEN_RPT_PREFIX', ''),

        ],

        // oam readonly
        'oracle_oam' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_BPM_RPT_TNS', ''),
            // 'host'          => env('DB_NEXTGEN_OAM_HOST', '**************'), // ************** //racprd-cluster-scan.eperolehan.com.my
            'host'          => env('DB_NEXTGEN_OAM_HOST', 'racdb-cluster-scan.eperolehan.com.my'),
            'port'          => env('DB_NEXTGEN_OAM_PORT', '1521'),
            'database'      => env('DB_NEXTGEN_OAM_DATABASE', 'ngepdb'),
            'service_name'  => env('DB_NEXTGEN_OAM_DATABASE', 'epsso'),
            'username'      => env('DB_NEXTGEN_OAM_USERNAME', 'epss'),
            'password'      => env('DB_NEXTGEN_OAM_PASSWORD', 'ep55oam'),
            'charset'       => env('DB_NEXTGEN_OAM_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_NEXTGEN_OAM_PREFIX', ''),

        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'predis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'predis'),
            'prefix' => Str::slug(env('APP_NAME', 'laravel'), '_').'_database',
        ],

        'default' => [
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => env('REDIS_DB', 0),
        ],

        'cache' => [
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => env('REDIS_CACHE_DB', 1),
        ],

    ],

];
