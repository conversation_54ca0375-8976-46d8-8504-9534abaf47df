<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ParameterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);

        return [
            'id'    => $this->id,
            'page' => $this->page,
            'parameter_code'  => $this->parameter_code,
            'parameter_name'  => $this->parameter_name,
            'parameter_desc'  => $this->parameter_desc,
            'parameter_value'  => $this->parameter_value,
            'modified_by'  => $this->modified_by,
            'modified_date'  => $this->modified_date
        ];
    }

    public function with($request)
    {
        return [
            'version'       => '1.0.0',
            'author_url'    => 'http://www.commercedc.com.my/'
        ];
    }
}
