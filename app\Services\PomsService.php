<?php

namespace App\Services;

use Carbon\Carbon;
use DB;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
class PomsService
{

    public static $TASK_MISSING_MODULE = array();

    public static $POMS_ACCESS_ROLE = array(
        1 => 'Administrator',
        2 => 'SLA Dashboard',
        3 => 'SLA Report',
        4 => 'Technical Dashboard'
    );

    public static $NAGIOS_HOST_STATUS = array(
        1 => 'HOST_PENDING',
        2 => 'SD_HOST_UP',
        3 => 'SD_HOST_DOWN',
        4 => 'SD_HOST_UNREACHABLE'
    );

    public static $NAGIOS_HOST_PORTAL = array('prdportal01', 'prdportal02', 'prdportal03', 'prdportal04', 'prdportal05', 'prdportal06');
    public static $NAGIOS_HOST_WEB = array('dmzweb01', 'dmzweb02', 'dmzweb03', 'dmzweb04', 'dmzweb05');
    // public static $NAGIOS_HOST_NETWORK = array('CBJ-KVDC-NGCORE01', 'CBJ-KVDC-NGCORE02');
    public static $NAGIOS_HOST_NETWORK = array('CBJ-KVDC-NGCORE01');
    public static $NAGIOS_HOST_SSO = array('dmzssoweb01', 'dmzssoweb02', 'prdsso01', 'prdsso02', 'prdsso03');
    public static $NAGIOS_HOST_SOLR = array('prdsolr01');
    public static $NAGIOS_HOST_DB = array('prddbs01', 'prddbs02', 'prddbs03', 'prddbs04', 'prddbs11', 'prddbs12', 'prddbs13');
    public static $NAGIOS_HOST_BPM = array('prdbpmsoa01', 'prdbpmsoa02', 'prdbpmsoa03', 'prdsoaint01', 'prdsoaint02');

    public static $NAGIOS_EXCLUDE_SERVICE = array(
        'TCP Port 5556 - Node Manager',
        'TCP Port 7001 - Admin Server',
        'TCP Port 5556 - Node Manager',
        'TCP Port 5556 - Node Manager',
        'TCP Port 5556 - Node Manager',
        'TCP Port 7001 - Admin Server - prdOSBBTDomain',
        'TCP Port 7003 - Admin Server - prdOLOSBDomain',
        'TCP Port 5556 - Node Manager',
        'EPLFY1 TNS Ping',
        'EPSOA1 TNS Ping',
        'EPSSO1 TNS Ping',
        'NGEPDB1 TNS Ping',
        'NGEPLFY1 TNS Ping',
        'NGEPSOA1 TNS Ping',
        'EPLFY2 TNS Ping',
        'EPSOA2 TNS Ping',
        'EPSSO2 TNS Ping',
        'NGEPDB2 TNS Ping',
        'NGEPLFY2 TNS Ping',
        'NGEPSOA2 TNS Ping',
        'EPLFY3 TNS Ping',
        'EPSOA3 TNS Ping',
        'EPSSO3 TNS Ping',
        'NGEPSOA3 TNS Ping',
        'EPLFY4 TNS Ping',
        'EPSOA4 TNS Ping',
        'EPSSO4 TNS Ping',
        'EPAPP1 TNS Ping',
        'NGEPDBS1 TNS Ping',
        'EPAPP2 TNS Ping',
        'NGEPDBS2 TNS Ping',
        'EPAPP3 TNS Ping',
        'TCP Port 5556 - Node Manager',
        'TCP Port 7001 - Admin Server',
        'TCP Port 5556 - Node Manager'
    );

    public function getMitelSlaData($fromDate, $toDate)
    {
        $query = "SELECT e.*, ROUND(((e.call_answered + (ISNULL(e.call_abandon, 0) - ISNULL(e.call_abandoned_short, 0))) / e.call_offered) * 100, 1) AS service_level
        FROM (
            SELECT CONVERT(VARCHAR(10), MidnightStartDate, 126) AS day_per_month
                ,CAST(sum(QueueOffered) AS FLOAT) AS call_offered
                ,(
                    SELECT sum(q.Column2Count)
                    FROM QueueSpectrumByPeriodStats q
                    WHERE q.SpectrumType = 'Answer'
                        AND q.midnightstartdate BETWEEN CONVERT(DATETIME, '$fromDate 00:00:01:001')
                            AND CONVERT(DATETIME, '$toDate 23:59:59:990')
                        AND q.QueueReporting IN ('P101','P105','P108','P110','P113','P201','P202','P203')
                        AND CONVERT(VARCHAR(10), q.MidnightStartDate, 126) = CONVERT(VARCHAR(10), a.MidnightStartDate, 126)
                    GROUP BY CONVERT(VARCHAR(10), q.MidnightStartDate, 126)
                    ) AS call_answered
                ,(
                    SELECT sum(q.Column2Count)
                    FROM QueueSpectrumByPeriodStats q
                    WHERE q.SpectrumType = 'Abandon'
                        AND q.midnightstartdate BETWEEN CONVERT(DATETIME, '$fromDate 00:00:01:001')
                            AND CONVERT(DATETIME, '$toDate 23:59:59:990')
                        AND q.QueueReporting IN ('P101','P105','P108','P110','P113','P201','P202','P203')
                        AND CONVERT(VARCHAR(10), q.MidnightStartDate, 126) = CONVERT(VARCHAR(10), a.MidnightStartDate, 126)
                    GROUP BY CONVERT(VARCHAR(10), q.MidnightStartDate, 126)
                    ) AS call_abandon
                ,SUM(QueueAbandoned) AS call_abandoned_long
                ,CAST(sum(QueueShortAbandoned) AS FLOAT) AS call_abandoned_short
                ,SUM(QueueAnswered) AS call_handled
                ,ROUND(((CAST(SUM(QueueAbandoned) AS FLOAT) / CAST(SUM(QueueOffered) AS FLOAT)) * 100), 1) AS abandon_perc
                ,ROUND(((CAST(SUM(QueueAnswered) AS FLOAT) / CAST(SUM(QueueOffered) AS FLOAT)) * 100), 1) AS answer_perc
            FROM QueuePerformanceByPeriodStats a
            WHERE QueueReporting IN ('P101','P105','P108','P110','P113','P201','P202','P203')
                AND midnightstartdate BETWEEN CONVERT(DATETIME, '$fromDate 00:00:01:001')
                    AND CONVERT(DATETIME, '$toDate 23:59:59:990')
            GROUP BY CONVERT(VARCHAR(10), MidnightStartDate, 126)
            ) e";

        return $resMitelData = DB::connection('sqlsrv_mitel')->select($query);
    }

    // public function getAspectSlaData($queryDate)
    // {
    //     $slaThreshold = 10;
    //     $date = $queryDate->format('Y-m-d H:i:s');

    //     $query = "
    //     SELECT a.*, cast((a.call_handle) + (a.call_short) AS FLOAT) / (a.call_offer) * 100 AS mitel_performance
    //     FROM (SELECT count(CASE WHEN queuetime < $slaThreshold
    //                                    AND user_id IS NOT NULL
    //                                  THEN 1
    //                             ELSE NULL END
    //         ) AS call_handle, count(CASE WHEN queuetime < $slaThreshold
    //                                             AND user_id IS NULL
    //                                           THEN 1
    //                                      ELSE NULL END
    //         ) AS call_short,
    //                  count(CASE WHEN queuetime > $slaThreshold
    //                                    AND user_id IS NULL
    //                                  THEN 1
    //                             ELSE NULL END
    //                      ) AS call_long, count(1) AS call_offer
    //           FROM detail_epro.dbo.CallDetail
    //           WHERE Service_Id IN (49, 50)
    //           AND CallTypeId = 1 AND CallStartDt >= Todatetimeoffset('$date', '+08:00') AND
    //                   CallStartDt <= Todatetimeoffset(DATEADD(DAY, 1, '$date'), '+08:00')) a
    //     ";

    //     return DB::connection('sqlsrv_aspect')->select($query);
    // }

    public function getAspectSlaData($queryDate)
    {
        $slaThreshold = 10; // normal (else)
        // $slaThreshold = 30; // 7/4/2020
        // $slaThreshold = 25; // 8/4/2020 - 22/7/2020
        $serviceId = '49, 50'; // 15/5/2020 onward
        // $serviceId = '14,15,16,17,18,19,20,21'; // before 15/5/2020
        $date = $queryDate->format('Y-m-d H:i:s');

        $query = "
        SELECT
        a.*,
        cast ((a.call_abandon_long) AS FLOAT) /(a.acd_call_offer) * 100 AS abandon_percentage,
        cast ((a.acd_call_handle2) AS FLOAT) /(a.acd_call_offer) * 100 AS answer_percentage,
        cast ((a.acd_call_handle) AS FLOAT) / (a.acd_call_offer-a.call_abandon_short) * 100 AS service_level
        FROM
        (
        SELECT
        count(CASE WHEN service_id IN ($serviceId) THEN 1 ELSE NULL END ) AS acd_call_offer,
        count(CASE WHEN user_id IS NOT NULL THEN 1 ELSE NULL END ) AS acd_call_handle2,
        count(CASE WHEN queuetime < $slaThreshold AND user_id IS NOT NULL THEN 1 ELSE NULL END ) AS acd_call_handle,
        count(CASE WHEN queuetime < $slaThreshold AND user_id IS NULL THEN 1 ELSE NULL END ) AS call_abandon_short,
        count(CASE when user_id IS NULL THEN 1 ELSE NULL END) AS call_abandon_long
        FROM
        detail_epro.dbo.calldetail
        WHERE calltypeid = 1
        AND callstartdt >= todatetimeoffset ('$date', '+08:00')
        AND callstartdt <= todatetimeoffset (DATEADD(DAY, 1, '$date'), '+08:00')
        AND service_id IN ($serviceId) ) a
        ";

        return DB::connection('sqlsrv_aspect')->select($query);
    }

    public function getNewAspectSlaData($queryDate) // to match with aspect summary dataview
    {
        $date = $queryDate->format('Y-m-d H:i:s');

        $query = "
        SELECT a.*,
            cast((a.call_abandon_long) AS FLOAT) / (a.acd_call_offer) * 100 AS abandon_percentage,
            cast((a.acd_call_handle) AS FLOAT) / (a.acd_call_offer) * 100 AS answer_percentage,
            cast((a.call_WI_lvl) AS FLOAT) / (a.acd_call_offer - a.call_abandon_short) * 100 AS service_level
        FROM (SELECT sum(CASE WHEN CallActionId NOT IN(4, 6, 0, 16, 38, 39, 40, 41) THEN TotalCalls END) AS acd_call_offer,
                    sum(CASE WHEN CallActionId IN(3, 8, 18, 19, 35) THEN TotalCalls END) AS acd_call_handle,
                    sum(CASE WHEN CallActionId IN(3, 8, 18, 19, 43, 35, 55)
                                THEN acdwiservicecalls END) AS call_WI_lvl,
                    ISNULL(sum(CASE WHEN CallActionId IN(5, 23, 24) THEN ShortCalls END), 0) AS call_abandon_short,
                    ISNULL(sum(CASE WHEN CallActionId IN(5, 24, 38) THEN TotalCalls END), 0) AS call_abandon_long
            FROM summary_epro..mediaservicesummary
            WHERE
                    CallTypeId = 1
                    AND BeginTimePeriodDt >= Todatetimeoffset('$date', '+08:00')
                    AND BeginTimePeriodDt < Todatetimeoffset(DATEADD(DAY, 1, '$date'), '+08:00')
                    AND Service_Id IN(49, 50)) a
        ";

        return DB::connection('sqlsrv_aspect')->select($query);
    }


    public function getTalkdeskDashboardSlaData()
    {
        $data = DB::connection('mysql')->table('sla_mitel')
            ->where('date_call', DB::raw('(SELECT MAX(date_call) FROM sla_mitel)'))
            ->first();

        return $data;
    }
}
