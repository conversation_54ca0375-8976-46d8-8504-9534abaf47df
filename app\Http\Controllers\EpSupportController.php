<?php

namespace App\Http\Controllers;

use App\Services\Traits\ePLoginStatisticService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class EpSupportController extends Controller
{
    use ePLoginStatisticService;

    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    public function getEpLoginStats(Request $request)
    {
        $selectedDate = $request->input('date') ?? date('Y-m-d');
        $loginStatistics = $this->fetchLoginStatistics($selectedDate);

        list($dataTimeStat, $dataSupplierHourlyStat, $dataPTJHourlyStat) = $this->prepareStatisticsData($loginStatistics, $selectedDate);

        $TIME_RANGE_LOGIN = 15;
        $carbonNow = Carbon::now();
        $DisplayData15MinuteBefore = $this->getLoginStatisticCurr15MinuteBefore($carbonNow->toDateTimeString(), $TIME_RANGE_LOGIN);

        if ($selectedDate == date('Y-m-d')) {
            list($resultTime, $countPTJ, $countSupp, $countTotal) = $this->prepareCurrentTimeData($carbonNow, $TIME_RANGE_LOGIN, $DisplayData15MinuteBefore);
        } else {
            list($resultTime, $countPTJ, $countSupp, $countTotal) = $this->preparePreviousDayData($selectedDate, $dataPTJHourlyStat, $dataSupplierHourlyStat);
        }

        $exactMonth = date('F', strtotime($request->input('date')));

        $response = [
            "dataTimeStat" => array_keys($dataTimeStat),
            "dataSupplierHourlyStat" => array_values($dataSupplierHourlyStat),
            "dataPTJHourlyStat" => array_values($dataPTJHourlyStat),
            "sumSupp" => $countPTJ,
            "sumPTJ" => $countSupp,
            "sumUserLogin" => $countTotal,
            "selectedDate" => $selectedDate,
            "ActualDate" => $request->input('date'),
            "exactMonth" => $exactMonth,
            "resultTime" => $resultTime
        ];

        return response()->json($response);
    }

    private function fetchLoginStatistics($selectedDate)
    {
        return DB::connection('mysql_ep_support')->table('ep_login_statistic')
            ->select('date_login', 'time_login', 'login_total', 'user_group')
            ->where('date_login', $selectedDate)
            ->where('time_login', '!=', null)
            ->orderBy('time_login')
            ->get();
    }

    private function prepareStatisticsData($loginStatistics, $selectedDate)
    {
        $dataTimeStat = [];
        $dataSupplierHourlyStat = [];
        $dataPTJHourlyStat = [];

        foreach ($loginStatistics as $loginStatistic) {
            $time = $selectedDate . 'T' . $loginStatistic->time_login . '.000000Z';

            $dataTimeStat[$time] = $dataTimeStat[$time] ?? 0;
            $dataTimeStat[$time]++;

            if ($loginStatistic->user_group === 'SUPPLIER') {
                $dataSupplierHourlyStat[$time] = $dataSupplierHourlyStat[$time] ?? 0;
                $dataSupplierHourlyStat[$time] += $loginStatistic->login_total;
            } elseif ($loginStatistic->user_group === 'PTJ') {
                $dataPTJHourlyStat[$time] = $dataPTJHourlyStat[$time] ?? 0;
                $dataPTJHourlyStat[$time] += $loginStatistic->login_total;
            }
        }

        return [$dataTimeStat, $dataSupplierHourlyStat, $dataPTJHourlyStat];
    }

    private function prepareCurrentTimeData($carbonNow, $TIME_RANGE_LOGIN, $DisplayData15MinuteBefore)
    {
        $currTimeResult = $carbonNow->format('H:i A');

        if ($TIME_RANGE_LOGIN != null && $TIME_RANGE_LOGIN > 0) {
            $timeBefore = $carbonNow->subMinutes($TIME_RANGE_LOGIN);
            $timeBeforeResult = $timeBefore->format('H:i A');
            $currTimeResult = 'Total user login within ' . $TIME_RANGE_LOGIN . ' minutes from ' . $timeBeforeResult . ' to ' . $currTimeResult;
        }

        $resultTime = $currTimeResult;

        $countPTJ = 0;
        $countSupp = 0;
        $countTotal = 0;
        foreach ($DisplayData15MinuteBefore as $obj) {
            if ($obj->user_group == 'PTJ') {
                $countPTJ += $obj->login_total;
            } else {
                $countSupp += $obj->login_total;
            }
            $countTotal = $countPTJ + $countSupp;
        }

        return [$resultTime, $countPTJ, $countSupp, $countTotal];
    }

    public function preparePreviousDayData($selectedDate, $dataPTJHourlyStat, $dataSupplierHourlyStat)
    {
        $selectedDate = Carbon::createFromFormat('Y-m-d', $selectedDate);

        $countPTJ = array_sum($dataPTJHourlyStat);
        $countSupp = array_sum($dataSupplierHourlyStat);
        $countTotal = $countPTJ + $countSupp;

        $resultTime = 'Total user login on ' . $selectedDate->format('d F Y') . '';

        return [$resultTime, $countPTJ, $countSupp, $countTotal];
    }
}
