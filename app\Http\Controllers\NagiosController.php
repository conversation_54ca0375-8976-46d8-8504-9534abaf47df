<?php

namespace App\Http\Controllers;

use App\Services\PomsService;
use GuzzleHttp\Client;
use Goutte\Client as GClient;

class NagiosController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Current Host Status
        $uri = "http://nagios-apps01.eperolehan.com.my/nagiosxi/backend/?cmd=gethoststatus&username=middleware&ticket=j7bs7lfan2493vikq7o9mo26d6mhdc4vgeqn29uo9vubvfsplkvbc0fp558hebgs";

        $client = new Client([
            'base_uri' => $uri,
        ]);

        $response = $client->get($uri, [
            'headers' => [
                'Accept' => 'application/xml'
            ]
        ]);

        $body = $response->getBody();

        /** @var $body GuzzleHttp\Psr7\Stream */
        // $contents = (string) $body;

        $xml = simplexml_load_string($body);

        dd($xml);
        $json = json_encode($xml);
        $array = json_decode($json, TRUE);

        return $array;
    }

    public function scrapeNagios()
    {
        $client = new GClient();

        // Go to the nagiosxi website
        $crawler = $client->request('GET', 'http://nagios-apps01.eperolehan.com.my/nagiosxi/reports/');
        $form = $crawler->selectButton('Login')->form();
        $client->submit($form, array('username' => 'middleware', 'password' => 'cDc@2019'));

        $url_host = "http://nagios-apps01.eperolehan.com.my/nagiosxi/reports/availability.php?";

        $url_param = "host=&service=&reportperiod=last24hours&startdate=&enddate=&host=&hostgroup=PRD+Environment&servicegroup=&reporttimesubmitbutton=Update&advanced=0&assumeinitialstates=yes&assumestateretention=yes&assumestatesduringdowntime=yes&includesoftstates=no&assumedhoststate=3&assumedservicestate=6&timeperiod=&manual_run=1";

        $url = $url_host . $url_param;

        $crawler = $client->request('GET', $url);

        $flat = function (string $selector) use ($crawler) {
            $result = [];
            $count = 0;
            $crawler->filter($selector)->each(function ($table, $i) use (&$result, &$count) {
                $table->filter('tr')->each(function ($tr, $i) use (&$result, &$count) {
                    $tr->filter('td')->each(function ($td, $i) use (&$result, &$count) {
                        $html = trim($td->html());
                        if (strpos($html, '<table') !== FALSE) return;

                        $iterator = $td->getIterator()->getArrayCopy()[0];
                        $address = $iterator->getNodePath();

                        if (!empty($html)) {
                            if ($html !== '<hr noshade size="1">') {
                                $result[$address] = $html . '_' . $count++;
                            }
                        }
                    });
                });
            });

            return $result;
        };

        //$val = json_encode($flat('.infotable'));
        $val = $flat('.infotable');

        dd($val);
        //dd($crawler->html());

    }

    public function nagiosHostStatusApi()
    {
        // dd('masuk');

        // $hostUri = 'http://nagios-apps01.eperolehan.com.my/nagios/cgi-bin/statusjson.cgi?query=hostlist&hostgroup=PRD+Environment';
        // $hostUri = 'http://nagios-apps01.eperolehan.com.my/nagios/cgi-bin/statusjson.cgi?query=hostlist';
        // $hostCountUri = 'http://nagios-apps01.eperolehan.com.my/nagios/cgi-bin/statusjson.cgi?query=hostcount&hostgroup=PRD+Environment';
        $serviceUri = 'http://nagios-apps01.eperolehan.com.my/nagios/cgi-bin/statusjson.cgi?query=servicelist&hostgroup=PRD+Environment';
        $serviceNetUri = 'http://nagios-apps01.eperolehan.com.my/nagios/cgi-bin/statusjson.cgi?query=servicelist';

        $objService = $this->fetchNagiosData($serviceUri);
        $objServiceNetwork1 = $this->fetchNagiosData($serviceNetUri . '&hostname=CBJ-KVDC-NGCORE01');
        // $objServiceNetwork2 = $this->fetchNagiosData($serviceNetUri . '&hostname=CBJ-NDC-NGCORE02');

        $serviceListPrdData = $objService->data->servicelist;
        $serviceListNetData1 = $objServiceNetwork1->data->servicelist;
        // $serviceListNetData2 = $objServiceNetwork2->data->servicelist;
        // $serviceListNetData = (object) array_merge((array) $serviceListNetData1, (array) $serviceListNetData2);dd($serviceListNetData);
        $serviceListNetData = (object) array_merge((array) $serviceListNetData1);

        $objPortal = $this->getHostDetailObj('portal',$serviceListPrdData, PomsService::$NAGIOS_HOST_PORTAL);
        $objWeb = $this->getHostDetailObj('web',$serviceListPrdData, PomsService::$NAGIOS_HOST_WEB);
        $objNetwork = $this->getHostDetailObj('network', $serviceListNetData, PomsService::$NAGIOS_HOST_NETWORK);
        $objSSO = $this->getHostDetailObj('sso',$serviceListPrdData, PomsService::$NAGIOS_HOST_SSO);
        $objSOLR = $this->getHostDetailObj('solr',$serviceListPrdData, PomsService::$NAGIOS_HOST_SOLR);
        $objDatabase = $this->getHostDetailObj('database',$serviceListPrdData, PomsService::$NAGIOS_HOST_DB);
        $objBPM = $this->getHostDetailObj('bpm',$serviceListPrdData, PomsService::$NAGIOS_HOST_BPM);

        /* ADD DUMMY SOLR: PROSOLR02 - 20240506 */
        $objSOLR->host->prdsolr02 = (object) [
            "host_name" => "prdsolr02",
            "availability_status" => "UP",
            "availability_count" => 1,
            "ping_detail" => (object) [
                "Ping" => 2,
                "service_availability_count" => 1
            ]
        ];

        $hostList = array($objPortal, $objWeb, $objNetwork, $objSSO, $objSOLR, $objDatabase, $objBPM);
        $hostUpPercent = $this->checkHostAvailabilityStatus($hostList);

        $dummyHost = new \stdClass();
        $dummyHost->host_name = "CBJ-KVDC-NGCORE02";
        $dummyHost->availability_status = "PASSIVE";

        if (!isset($objNetwork->host)) {
            $objNetwork->host = new \stdClass();
        }

        $objNetwork->host->{"CBJ-KVDC-NGCORE02"} = $dummyHost;

        return response()->json(
            array(
                'data' => array(
                    'portal' => $objPortal,
                    'web' => $objWeb,
                    'network' => $objNetwork,
                    'sso' => $objSSO,
                    'solr' => $objSOLR,
                    'database' => $objDatabase,
                    'bpm' => $objBPM,
                    'host_up_percentage' => $hostUpPercent
                )
            )
        );
    }

    public function fetchNagiosData($uri) {
        $username = 'middleware';
        $password = 'cDc@2019';

        $client = new Client();
        $body = $client->request('GET', $uri, [
            'headers' => [
                'Accept' => 'application/json',
                'Content-type' => 'application/json'
            ],
            'auth' => [
                $username,
                $password
            ]
        ])->getBody();

        return json_decode($body);
    }

    public function getHostDetailObj($group, $hostdata, $hostgroup)
    {

        $objHost = (object)array();
        $serviceCount = 0;
        $serviceAvailableCount = 0;
        $tempHost = (object)array();

        foreach ($hostdata as $host => $service) {

            if (in_array($host, $hostgroup)) {
                $obj = (object)array();
                $tcp = (object)array();
                $tns = (object)array();
                $ping = (object)array();

                if($group == 'network' || $group == 'solr'){
                    $ping = $this->checkServiceComponent($service, 'Ping');
                    $nodeAvailabilityCount = $ping->service_availability_count;
                } else {
                    $tcp = $this->checkServiceComponent($service, 'TCP Port ');
                    $nodeAvailabilityCount = $tcp->service_availability_count;
                    $tns = $this->checkServiceComponent($service, 'TNS Ping');
                    $nodeAvailabilityCount += $tns->service_availability_count;
                }

                $obj->host_name = $host;

                if ($nodeAvailabilityCount > 0) {
                    $obj->availability_status = 'UP';
                    $serviceAvailableCount++;
                } else {
                    $obj->availability_status = 'DOWN';
                }
                $obj->availability_count = $nodeAvailabilityCount;
                if($group == 'network' || $group == 'solr'){
                    $obj->ping_detail = $ping;
                } else {
                    $obj->tcp_detail = $tcp;
                    $obj->tns_detail = $tns;
                }

                $tempHost->{$host} = $obj;
                $serviceCount++;
            }
        }

        $objHost->host = $tempHost;
        $objHost->service_count = $serviceCount;
        $objHost->service_available_count = $serviceAvailableCount;
        if($serviceAvailableCount > 0) {
            $objHost->host_availability = 'UP';
        } else {
            $objHost->host_availability = 'DOWN';
        }

        return $objHost;
    }

    public function checkServiceComponent($service, $componentName) {
        $componentObj = (object)array();
        $nodeAvailabilityCount = 0;
        foreach ($service as $key => $status) {
            if (strpos($key, $componentName) !== false) {
                if ($status === 2) {
                    $nodeAvailabilityCount++;
                }
                $componentObj->{$key} = $status;
            }
        }

        $componentObj->service_availability_count = $nodeAvailabilityCount;

        return $componentObj;
    }

    private function checkHostAvailabilityStatus($hosts)
    {
        $totalCount = 0;
        $availableCount = 0;

        foreach ($hosts as $host) {
            if ($host->host_availability === 'UP') {
                $availableCount++;
            }
            $totalCount++;
        }

        if ($availableCount === $totalCount) {
            return 100;
        }

        return 0;
    }
}
