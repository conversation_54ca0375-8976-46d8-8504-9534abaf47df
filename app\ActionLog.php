<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class ActionLog extends Model
{
    protected $connection = 'mysql';
    protected $primaryKey = 'id';
    protected $table = 'action_log';
    public $timestamps = false;

    protected $fillable = [
        'action', // Add 'action' to the fillable property
        'action_data',
        'action_status',
        'created_by',
        'created_at',
    ];

    public static function saveActionLog($actionName, $actionData, $status, $userid = null)
    {
        $username = null;
        $user = ($userid == null) ? Auth::user() : User::find($userid);
        if ($user != null) {
            $username = $user->username;
        }

        $actionLog = ActionLog::create([
            'action' => $actionName,
            'action_data' => json_encode($actionData),
            'action_status' => $status,
            'created_by' => $username,
            'created_at' => now(),
        ]);

        return $actionLog;
    }
}
