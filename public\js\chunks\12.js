(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[12],{

/***/ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/src/views/datalist/ParameterSetting.vue?vue&type=script&lang=js&":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib??ref--4-0!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/src/views/datalist/ParameterSetting.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ __webpack_exports__["default"] = ({
  data: function data() {
    return {
      parameters: [],
      breadcrumbs: [{
        title: 'Administrator',
        url: '/admin'
      }, {
        title: 'SLA Dashboard',
        active: true
      }],
      parameter: {
        id: '',
        name: '',
        value: '',
        userid: ''
      },
      popupActive: false // value1: '',
      // value2: '',

    };
  },
  computed: {// validName() {
    //     return (this.valMultipe.value1.length > 0 && this.valMultipe.value2.length > 0)
    // }
  },
  mounted: function mounted() {
    var vm = this;
    vm.fetchParameterList();
  },
  methods: {
    fetchParameterList: function fetchParameterList() {
      var _this = this;

      var api_url = '/api/parameter/sla_dashboard';
      this.$vs.loading();
      fetch(api_url).then(function (res) {
        return res.json();
      }).then(function (res) {
        _this.parameters = res.data;

        _this.$vs.loading.close();
      }).catch(function (err) {
        return console.log(err);
      });
    },
    openEditDialogue: function openEditDialogue(param_id, param_name, param_value) {
      var _this2 = this;

      console.log('parameter_id: ' + param_id);
      this.parameter.id = param_id;
      this.parameter.name = param_name;
      this.parameter.value = param_value;
      this.popupActive = true;
      this.$http({
        url: "user",
        method: 'GET'
      }).then(function (res) {
        _this2.parameter.userid = res.data.id;
      }).catch(function (err) {
        return console.log(err);
      });
    },
    updateParamValue: function updateParamValue() {
      var _this3 = this;

      // Update
      fetch('api/parameter', {
        method: 'put',
        body: JSON.stringify(this.parameter),
        headers: {
          'content-type': 'application/json'
        }
      }).then(function (res) {
        return res.json();
      }).then(function (data) {
        _this3.popupActive = false;

        _this3.$vs.notify({
          color: 'success',
          title: 'Update Successfully',
          text: 'Selected parameter value successfully updated!'
        });

        _this3.fetchParameterList();
      }).catch(function (err) {
        return console.log(err);
      });
    },
    acceptAlert: function acceptAlert() {
      this.clearValMultiple();
      this.$vs.notify({
        color: 'success',
        title: 'Accept Selected',
        text: 'Lorem ipsum dolor sit amet, consectetur'
      });
    },
    close: function close() {
      this.$vs.notify({
        color: 'danger',
        title: 'Closed',
        text: 'You close a dialog!'
      });
    },
    clearValMultiple: function clearValMultiple() {
      this.valMultipe.value1 = "";
      this.valMultipe.value2 = "";
    }
  }
});

/***/ }),

/***/ "./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/src/views/datalist/ParameterSetting.vue?vue&type=template&id=1e4f97d4&":
/*!***************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/src/views/datalist/ParameterSetting.vue?vue&type=template&id=1e4f97d4& ***!
  \***************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
var render = function() {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  return _c(
    "div",
    { staticClass: "vx-col" },
    [
      _c("vs-breadcrumb", {
        attrs: { items: _vm.breadcrumbs, separator: "chevron_right" }
      }),
      _vm._v(" "),
      _c(
        "vx-card",
        { attrs: { title: "SLA Dashboard Parameter Setting" } },
        [
          _c(
            "vs-table",
            {
              attrs: {
                "max-items": "10",
                pagination: "",
                search: "",
                data: _vm.parameters,
                stripe: ""
              },
              scopedSlots: _vm._u([
                {
                  key: "default",
                  fn: function(ref) {
                    var data = ref.data
                    return _vm._l(data, function(tr, indextr) {
                      return _c(
                        "vs-tr",
                        { key: indextr },
                        [
                          _c("vs-td", { attrs: { data: tr.parameter_name } }, [
                            _vm._v(
                              "\n                        " +
                                _vm._s(tr.parameter_name) +
                                "\n                    "
                            )
                          ]),
                          _vm._v(" "),
                          _c("vs-td", { attrs: { data: tr.parameter_desc } }, [
                            _vm._v(
                              "\n                        " +
                                _vm._s(tr.parameter_desc) +
                                "\n                    "
                            )
                          ]),
                          _vm._v(" "),
                          _c("vs-td", { attrs: { data: tr.parameter_value } }, [
                            _vm._v(
                              "\n                        " +
                                _vm._s(tr.parameter_value) +
                                "\n                        "
                            )
                          ]),
                          _vm._v(" "),
                          _c(
                            "vs-td",
                            { attrs: { data: tr.modified_by_name } },
                            [
                              _vm._v(
                                "\n                        " +
                                  _vm._s(tr.modified_by_name) +
                                  "\n                    "
                              )
                            ]
                          ),
                          _vm._v(" "),
                          _c("vs-td", { attrs: { data: tr.modified_date } }, [
                            _vm._v(
                              "\n                        " +
                                _vm._s(tr.modified_date_format) +
                                "\n                    "
                            )
                          ]),
                          _vm._v(" "),
                          _c(
                            "vs-td",
                            [
                              _c("vs-button", {
                                attrs: {
                                  color: "warning",
                                  type: "filled",
                                  "icon-pack": "feather",
                                  icon: "icon-edit"
                                },
                                on: {
                                  click: function($event) {
                                    return _vm.openEditDialogue(
                                      tr.id,
                                      tr.parameter_name,
                                      tr.parameter_value
                                    )
                                  }
                                }
                              })
                            ],
                            1
                          )
                        ],
                        1
                      )
                    })
                  }
                }
              ])
            },
            [
              _c(
                "template",
                { slot: "thead" },
                [
                  _c(
                    "vs-th",
                    { attrs: { "sort-key": "parameter_name", width: "20%" } },
                    [_vm._v("Name")]
                  ),
                  _vm._v(" "),
                  _c("vs-th", [_vm._v("Description")]),
                  _vm._v(" "),
                  _c("vs-th", { attrs: { "sort-key": "parameter_value" } }, [
                    _vm._v("Value")
                  ]),
                  _vm._v(" "),
                  _c("vs-th", { attrs: { "sort-key": "modified_by" } }, [
                    _vm._v("Modified By")
                  ]),
                  _vm._v(" "),
                  _c("vs-th", { attrs: { "sort-key": "modified_date" } }, [
                    _vm._v("Modified Date")
                  ]),
                  _vm._v(" "),
                  _c("vs-th", [_vm._v("Action")])
                ],
                1
              )
            ],
            2
          ),
          _vm._v(" "),
          _c(
            "vs-popup",
            {
              attrs: {
                classContent: "popup-example",
                title: "Edit Parameter Value",
                active: _vm.popupActive
              },
              on: {
                "update:active": function($event) {
                  _vm.popupActive = $event
                }
              }
            },
            [
              _c("span", [_vm._v("Name")]),
              _vm._v(" "),
              _c("vs-input", {
                staticClass: "mt-3 w-full",
                attrs: {
                  disabled: "",
                  placeholder: "Parameter Name",
                  "vs-placeholder": "Parameter Name"
                },
                model: {
                  value: _vm.parameter.name,
                  callback: function($$v) {
                    _vm.$set(_vm.parameter, "name", $$v)
                  },
                  expression: "parameter.name"
                }
              }),
              _vm._v(" "),
              _c("span", [_vm._v("Parameter Value")]),
              _vm._v(" "),
              _c("vs-input", {
                staticClass: "mt-3 mb-3 w-full",
                attrs: {
                  placeholder: "Parameter Value",
                  "vs-placeholder": "Parameter Value"
                },
                model: {
                  value: _vm.parameter.value,
                  callback: function($$v) {
                    _vm.$set(_vm.parameter, "value", $$v)
                  },
                  expression: "parameter.value"
                }
              }),
              _vm._v(" "),
              _c("vs-button", { on: { click: _vm.updateParamValue } }, [
                _vm._v("Update")
              ]),
              _vm._v(" "),
              _c(
                "vs-button",
                {
                  attrs: { color: "warning" },
                  on: {
                    click: function($event) {
                      _vm.popupActive = false
                    }
                  }
                },
                [_vm._v("Cancel")]
              )
            ],
            1
          )
        ],
        1
      )
    ],
    1
  )
}
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ "./resources/js/src/views/datalist/ParameterSetting.vue":
/*!**************************************************************!*\
  !*** ./resources/js/src/views/datalist/ParameterSetting.vue ***!
  \**************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _ParameterSetting_vue_vue_type_template_id_1e4f97d4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ParameterSetting.vue?vue&type=template&id=1e4f97d4& */ "./resources/js/src/views/datalist/ParameterSetting.vue?vue&type=template&id=1e4f97d4&");
/* harmony import */ var _ParameterSetting_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ParameterSetting.vue?vue&type=script&lang=js& */ "./resources/js/src/views/datalist/ParameterSetting.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport *//* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");





/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _ParameterSetting_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _ParameterSetting_vue_vue_type_template_id_1e4f97d4___WEBPACK_IMPORTED_MODULE_0__["render"],
  _ParameterSetting_vue_vue_type_template_id_1e4f97d4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/js/src/views/datalist/ParameterSetting.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./resources/js/src/views/datalist/ParameterSetting.vue?vue&type=script&lang=js&":
/*!***************************************************************************************!*\
  !*** ./resources/js/src/views/datalist/ParameterSetting.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ParameterSetting_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../node_modules/babel-loader/lib??ref--4-0!../../../../../node_modules/vue-loader/lib??vue-loader-options!./ParameterSetting.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/src/views/datalist/ParameterSetting.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ParameterSetting_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/js/src/views/datalist/ParameterSetting.vue?vue&type=template&id=1e4f97d4&":
/*!*********************************************************************************************!*\
  !*** ./resources/js/src/views/datalist/ParameterSetting.vue?vue&type=template&id=1e4f97d4& ***!
  \*********************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_ParameterSetting_vue_vue_type_template_id_1e4f97d4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../node_modules/vue-loader/lib??vue-loader-options!./ParameterSetting.vue?vue&type=template&id=1e4f97d4& */ "./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/src/views/datalist/ParameterSetting.vue?vue&type=template&id=1e4f97d4&");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_ParameterSetting_vue_vue_type_template_id_1e4f97d4___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_ParameterSetting_vue_vue_type_template_id_1e4f97d4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });



/***/ })

}]);