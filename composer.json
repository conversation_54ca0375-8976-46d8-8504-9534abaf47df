{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.2", "ext-curl": "*", "ext-gd": "*", "ext-json": "*", "barryvdh/laravel-snappy": "^0.4.7", "fabpot/goutte": "^3.2", "fideloper/proxy": "^4.0", "guzzlehttp/guzzle": "^6.3", "huddledigital/zendesk-laravel": "^3.4", "laravel/framework": "^6.0", "laravel/tinker": "^1.0", "maatwebsite/excel": "^3.1", "tymon/jwt-auth": "dev-develop", "yajra/laravel-oci8": "^6.0"}, "require-dev": {"facade/ignition": "^1.4", "fzaninotto/faker": "^1.4", "mockery/mockery": "^1.0", "nunomaduro/collision": "^3.0", "phpunit/phpunit": "^8.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}