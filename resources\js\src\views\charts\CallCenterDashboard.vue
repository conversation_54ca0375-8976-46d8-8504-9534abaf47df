<template>
    <div class="vx-col w-full mb-base">
        <vx-card id="call-widget" title="Customer Service Call Centre" class="vs-con-loading__container">
            <template slot="">
                <div class="vx-row mt-5">
                    <div class="vx-col w-full md:w-1/2">
                        <e-charts id="answeredChart" :height="250" :options="gaugeAnswered"></e-charts>
                    </div>
                    <div class="vx-col w-full md:w-1/2">
                        <e-charts id="abandonedChart" :height="250" :options="gaugeAbandoned"></e-charts>
                    </div>
                </div>
            </template>
            <div class="flex justify-between text-center">
                <div class="w-1/2 border border-solid d-theme-border-grey-light border-r-0 border-b-0 border-l-0">
                    <p class="mt-4">Answered</p>
                    <p class="mb-4 text-3xl font-semibold text-success">{{ callCenterData['call_handled'] }}</p>
                </div>
                <div class="w-1/2 border border-solid d-theme-border-grey-light border-r-0 border-b-0">
                    <p class="mt-4">Abandoned</p>
                    <p class="mb-4 text-3xl font-semibold text-danger">{{ callCenterData['call_abandoned_long'] }}</p>
                </div>
            </div>
            <div class="mt-4">
                <span class="text-xs italic">Latest on {{ callCenterData['date_call'] }}</span>
            </div>
        </vx-card>
    </div>
</template>

<script>
    import ECharts from "vue-echarts/components/ECharts";
    import "echarts/lib/component/tooltip";
    import "echarts/lib/component/legend";

    import "echarts/theme/dark";

    export default {
        data() {
            return {
                threshold_answered: 0,
                threshold_abandoned: 0,
                callCenterData: [],
                themeDark: false,
                intervalConfig: {
                    shouldRun: false,
                    timing: 300000, // 5 Minutes
                },

                /* GAUGE ANSWERED */
                gaugeAnswered: {
                    grid: {
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 0
                    },
                    tooltip: {
                        // formatter: "{a} <br/>{b} : {c}%"
                        formatter: "{a}: <br/>{c}%"
                    },
                    toolbox: {
                        show: true,
                        feature: {
                            mark: {show: true}
                        }
                    },
                    series: [
                        {
                            title: {
                                show: true,
                                offsetCenter: [0, 100],
                                color: "#7367F0",
                                fontSize: 20
                            },
                            name: "Answered Percentage",
                            type: "gauge",
                            radius: "100%",
                            detail: {
                                formatter: "{value}%",
                                offsetCenter: [0, "60%"],
                                textStyle: {
                                    fontSize: 16
                                }
                            },
                            data: [
                                {
                                    value: 0
                                    // name: 'System'
                                }
                            ],
                            axisLine: {
                                lineStyle: {
                                    color: [[0, "#ea5455"], [1, "#28c76f"]],
                                    width: 20
                                }
                            },
                            splitLine: {
                                show: true,
                                length: 30,
                                lineStyle: {
                                    color: "auto"
                                }
                            },
                            axisTick: {
                                splitNumber: 5,
                                length: 8
                            }
                        }
                    ],
                    animationDuration: 2000
                },

                /* GAUGE ABANDONED */
                gaugeAbandoned: {
                    grid: {
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 0
                    },
                    tooltip: {
                        // formatter: "{a} <br/>{b} : {c}%"
                        formatter: "{a}: <br/>{c}%"
                    },
                    toolbox: {
                        show: true,
                        feature: {
                            mark: {show: true}
                        }
                    },
                    series: [
                        {
                            title: {
                                show: true,
                                offsetCenter: [0, 100],
                                color: "#7367F0",
                                fontSize: 20
                            },
                            name: "Abandoned Percentage",
                            type: "gauge",
                            radius: "100%",
                            detail: {
                                formatter: "{value}%",
                                offsetCenter: [0, "60%"],
                                textStyle: {
                                    fontSize: 16
                                }
                            },
                            data: [
                                {
                                    value: 0
                                    // name: 'System'
                                }
                            ],
                            axisLine: {
                                lineStyle: {
                                    color: [[0, "#28c76f"], [1, "#ea5455"]],
                                    width: 20
                                }
                            },
                            splitLine: {
                                show: true,
                                length: 30,
                                lineStyle: {
                                    color: "auto"
                                }
                            },
                            axisTick: {
                                splitNumber: 5,
                                length: 8
                            }
                        }
                    ],
                    animationDuration: 2000
                }

            };
        },
        components: {
            ECharts
        },

        mounted() {

            const vm = this;
            vm.fetchParameterData().then(() => {
                vm.fetchCallCenterData();
            });

            if (this.intervalConfig.shouldRun) {
                setInterval(() => {
                    vm.fetchCallCenterData();
                }, this.intervalConfig.timing)
            }
        },

        methods: {

            fetchParameterData: async function () {
                const url = '/api/parameter/sla_dashboard';
                try {
                    const res = await fetch(url);
                    const { data: paramList } = await res.json();

                    paramList.forEach(param => {
                        if (param.parameter_code === 'mitel_answered') {
                            this.gaugeAnswered.series[0].axisLine.lineStyle.color[0][0] = param.parameter_value / 100;
                        }
                        if (param.parameter_code === 'mitel_abandoned') {
                            this.gaugeAbandoned.series[0].axisLine.lineStyle.color[0][0] = param.parameter_value / 100;
                        }
                    });
                } catch (err) {
                    console.error(err);
                }
            },

            fetchCallCenterData: async function () {
                const url = '/api/callcenter/dashboard';

                this.$vs.loading({
                    container: '#call-widget',
                    scale: 0.6
                });

                try {
                    const res = await fetch(url);
                    const { data: callCenterData } = await res.json();

                    this.callCenterData = callCenterData;
                    this.gaugeAnswered.series[0].data[0].value = callCenterData.service_level;
                    this.gaugeAbandoned.series[0].data[0].value = callCenterData.abandon_perc;
                } catch (err) {
                    console.error(err);
                } finally {
                    this.$vs.loading.close('#call-widget > .con-vs-loading');
                }
            }
        }
    };
</script>
