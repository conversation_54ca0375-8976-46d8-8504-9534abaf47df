<template>
    <vx-card id="ep-performance-widget" title="Status of 16 Proxy (SLAs) Response Time" class="vs-con-loading__container">
        <vs-row>
            <div class="md:w-1/3">
                <vue-apex-charts type="bar" height="350" :options="transChart.chartOptions" :series="transChart.series"></vue-apex-charts>
                <span class="text-xs italic suc">Latest on {{ fetchDate }}</span>
            </div>

            <div class="md:w-2/3">
                <vs-table :data="responseData">

                    <template slot="thead">
                        <vs-th>Function</vs-th>
                        <vs-th>No. of Transactions</vs-th>
                        <vs-th>&gt; 3000ms</vs-th>
                        <vs-th>%</vs-th>
                    </template>

                    <template slot-scope="{data}">
                        <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td :data="tr.module_name">
                                {{tr.module_name}}
                            </vs-td>
                            <vs-td :data="tr.total_trans">
                                {{tr.total_trans}}
                            </vs-td>
                            <vs-td :data="tr.total_exceed_trans">
                                <span :class="[tr.total_exceed_trans > 0 ? 'text-danger' : 'text-success' , 'font-semibold']">{{tr.total_exceed_trans}}</span>
                            </vs-td>
                            <vs-td :data="tr.ratio">
                                <span :class="[tr.ratio < 95 ? 'text-danger' : 'text-default' , 'font-semibold']">{{tr.ratio}}</span>
                            </vs-td>

                            <template class="expand-user" slot="expand">
                                <vs-table class="w-full" vs-table :data="responseData" stripe>
                                    <vs-tr :data="tr" :key="indexSub" v-for="(tr, indexSub) in data[indextr].module_data">
                                        <vs-td :data="tr.module" width="43%">
                                            <span class="text-xs">{{tr.module}} - {{tr.transaction}}</span>
                                        </vs-td>
                                        <vs-td :data="tr.total_trans" width="32%">
                                            {{tr.total_trans}}
                                        </vs-td>
                                        <vs-td :data="tr.total_trans_exceed" width="18%">
                                            <span :class="[tr.total_trans_exceed > 0 ? 'text-danger' : 'text-success' , 'font-semibold']">{{tr.total_trans_exceed}}</span>
                                        </vs-td>
                                        <vs-td :data="tr.ratio" width="7%">
                                            {{tr.ratio}}
                                        </vs-td>
                                    </vs-tr>
                                </vs-table>
                            </template>
                        </vs-tr>
                    </template>
                </vs-table>
            </div>

        </vs-row>

    </vx-card>
</template>

<script>
    import VueApexCharts from 'vue-apexcharts'
    import {format, compareAsc} from 'date-fns'
    export default {
        data() {
            return {
                responseData: [],
                fetchDate: '',
                transChart: {
                    series: [
                        {
                            name: 'Total Transaction',
                            data: []
                        },
                        {
                            name: '> 3000ms',
                            data: []
                        }
                    ],
                    chartOptions: {
                        colors: ['#1a73e8','#ea5455'],
                        chart: {
                            stacked: true,
                        },
                        plotOptions: {
                            bar: {
                                horizontal: true,
                            }
                        },
                        stroke: {
                            width: 1,
                            colors: ['#fff']
                        },
                        dataLabels: {
                            enabled: false
                        },
                        xaxis: {
                            categories: [],
                        }
                    }
                },
            }
        },

        mounted() {
            this.fetchData();
        },

        methods: {
            setChartData: function () {
                let totalTrans = [];
                let nameTrans = [];
                let exceedTrans = [];
                this.responseData.map(function (value, key) {
                    totalTrans.push(value.total_trans);
                    nameTrans.push(value.module_name);
                    exceedTrans.push(value.total_exceed_trans);
                });

                this.transChart.chartOptions = {
                    xaxis: {
                        categories: nameTrans
                    }
                };

                this.transChart.series = [
                    {
                        data: totalTrans
                    },
                    {
                        data: exceedTrans
                    }
                ];
            },

            fetchData: function () {
                let url = '/api/ep/performance';
                this.$vs.loading({container: '#ep-performance-widget', scale: 0.6});
                fetch(url)
                    .then(res => res.json())
                    .then(res => {
                        this.fetchDate = res.query_date;
                        this.responseData = res.data;
                        this.setChartData();
                        this.$vs.loading.close('#ep-performance-widget > .con-vs-loading');
                    })
                    .catch(err => console.log(err));

            }
        },

        components: {
            VueApexCharts,
        }
    }
</script>
