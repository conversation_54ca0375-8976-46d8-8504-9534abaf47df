<?php

namespace App\crm\sla;

use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class InsertTalkdeskDataByReportFile
{

    public static function runCheckTalkdeskFile()
    {
        $checkingDate = Carbon::now();
        Log::debug(self::class . ' Starting... Check Talkdesk Report File ', ['Checking Date' => $checkingDate]);
        dump('Process by scheduler...');
        self::checkReportFile();
    }

    public static function checkReportFile()
    {
        Log::debug(self::class . ' checkReportFile... Checking for files in directories', ['Checking Date' => Carbon::now()]);
        try {
            $dailyDirectory = storage_path('talkdesk/daily');
            $monthlyDirectory = storage_path('talkdesk/monthly');
            $adhocDirectory = storage_path('talkdesk/adhoc');

            $dailyFiles = glob($dailyDirectory . '/poms_report_talkdesk_*');
            $monthlyFiles = glob($monthlyDirectory . '/poms_report_talkdesk_*');
            $adhocFiles = glob($adhocDirectory . '/poms_report_talkdesk_*');

            Log::debug(self::class . ' checkReportFile... Found ' . count($dailyFiles) . ' daily files and ' . count($monthlyFiles) . ' monthly files', ['Checking Date' => Carbon::now()]);
            dump('checkReportFile... Found ' . count($dailyFiles) . ' daily files and ' . count($monthlyFiles) . ' monthly files');

            if (empty($dailyFiles) && empty($monthlyFiles) && empty($adhocFiles)) {
                dump('Files not found.. sending alert...');
                self::sendAlert("*POMS | Talkdesk* - No files found, please check your email or process the files manually. Process date: " . Carbon::now());
            } else {
                foreach ($dailyFiles as $file) {
                    if (is_file($file)) {
                        Log::debug(self::class . ' checkReportFile... Processing daily file: ' . $file, ['Checking Date' => Carbon::now()]);
                        dump('checkReportFile... Processing daily file: ' . $file);

                        self::insertDataFromFile($file);

                        rename($file, $dailyDirectory . '/done/' . basename($file));
                        Log::debug(self::class . ' checkReportFile... Moved daily file to done directory: ' . $file, ['Checking Date' => Carbon::now()]);
                    }
                }

                foreach ($monthlyFiles as $file) {
                    if (is_file($file)) {
                        Log::debug(self::class . ' checkReportFile... Processing monthly file: ' . $file, ['Checking Date' => Carbon::now()]);
                        dump('checkReportFile... Processing monthly file: ' . $file);

                        self::insertDataFromFileMonthly($file);

                        rename($file, $monthlyDirectory . '/done/' . basename($file));
                        Log::debug(self::class . ' checkReportFile... Moved monthly file to done directory: ' . $file, ['Checking Date' => Carbon::now()]);
                    }
                }

                foreach ($adhocFiles as $file) {
                    if (is_file($file)) {
                        Log::debug(self::class . ' checkReportFile... Processing adhoc file: ' . $file, ['Checking Date' => Carbon::now()]);
                        dump('checkReportFile... Processing adhoc file: ' . $file);

                        self::insertDataFromFileAdhoc($file);

                        rename($file, $adhocDirectory . '/done/' . basename($file));
                        Log::debug(self::class . ' checkReportFile... Moved adhoc file to done directory: ' . $file, ['Checking Date' => Carbon::now()]);
                    }
                }
            }
        } catch (\Exception $e) {
            // Handle the exception here
            self::sendAlert("*POMS | Talkdesk* - An error occurred during the processing of the Talkdesk report. Please check your email or manually process the files. Process date: " . Carbon::now());
            Log::debug(self::class . ' Error... ' . $e->getMessage(), ['Checking Date' => Carbon::now()]);
            throw new \Exception($e->getMessage());
        }
    }

    public static function insertDataFromFile($file)
    {
        $csvData = array_map('str_getcsv', file($file));
        array_shift($csvData);

        foreach ($csvData as $row) {
            $date_call = Carbon::parse($row[1])->format('Y-m-d');
            $existingData = DB::connection('mysql')->table('sla_mitel')->where('date_call', $date_call)->first();
            if (!$existingData) {
                DB::connection('mysql')->table('sla_mitel')->insert([
                    'date_call' => $date_call,
                    'acd_call_offer' => $row[2],
                    'acd_call_handle' => $row[5],
                    'call_WI_lvl' => $row[6],
                    'call_abandon_short' => $row[4],
                    'call_abandon_long' => $row[3],
                    'abandon_percentage' => number_format((float)str_replace('%', '', $row[7]), 2),
                    'answer_percentage' => number_format((float)str_replace('%', '', $row[8]), 2),
                    'service_level' => number_format((float)str_replace('%', '', $row[9]), 2),
                    'mitel_insert_data_datetime' => Carbon::now(),
                    'filename' => basename($file),
                    'periodicity' => 'daily',
                    'created_by' => 'scheduler'
                ]);

                Log::debug(self::class . ' checkReportFile... Inserted data for date_call: ' . $date_call, ['Checking Date' => Carbon::now()]);
                dump('checkReportFile... Inserted data for date_call: ' . $date_call);
            } else {
                Log::debug(self::class . ' checkReportFile... Data already exists for date_call: ' . $date_call, ['Checking Date' => Carbon::now()]);
                dump('checkReportFile... Data already exists for date_call: ' . $date_call);
            }
        }
    }

    public static function insertDataFromFileMonthly($file)
    {
        $csvData = array_map('str_getcsv', file($file));
        array_shift($csvData);

        $firstDateCall = null;

        foreach ($csvData as $row) {
            $date_call = $row[1];
            if ($firstDateCall === null) {
                $firstDateCall = Carbon::parse($date_call)->startOfMonth(); // Set to the first day of the month
            }

            if (empty($date_call)) {
                $existingData = DB::connection('mysql')->table('sla_talkdesk_monthly')->where('date_call', $firstDateCall)->first();
                if (!$existingData) {
                    DB::connection('mysql')->table('sla_talkdesk_monthly')->insert([
                        'date_call' => $firstDateCall,
                        'acd_call_offer' => $row[2],
                        'acd_call_handle' => $row[5],
                        'call_WI_lvl' => $row[6],
                        'call_abandon_short' => $row[4],
                        'call_abandon_long' => $row[3],
                        'abandon_percentage' => number_format((float)str_replace('%', '', $row[7]), 2),
                        'answer_percentage' => number_format((float)str_replace('%', '', $row[8]), 2),
                        'service_level' => number_format((float)str_replace('%', '', $row[9]), 2),
                        'mitel_insert_data_datetime' => Carbon::now(),
                        'filename' => basename($file),
                        'periodicity' => 'daily',
                        'created_by' => 'scheduler'
                    ]);

                    Log::debug(self::class . ' checkReportFile... Inserted data for date_call: ' . $firstDateCall, ['Checking Date' => Carbon::now()]);
                    dump('checkReportFile... Inserted data for date_call: ' . $firstDateCall);
                } else {
                    Log::debug(self::class . ' checkReportFile... Data already exists for date_call: ' . $firstDateCall, ['Checking Date' => Carbon::now()]);
                    dump('checkReportFile... Data already exists for date_call: ' . $firstDateCall);
                }
            }
        }
    }

    public static function insertDataFromFileAdhoc($file)
    {
        $csvData = array_map('str_getcsv', file($file));
        array_shift($csvData);

        foreach ($csvData as $row) {
            $date_call = Carbon::parse($row[1])->format('Y-m-d');
            $existingData = DB::connection('mysql')->table('sla_talkdesk')->where('date_call', $date_call)->first();
            if (!$existingData) {
                DB::connection('mysql')->table('sla_talkdesk')->insert([
                    'date_call' => $date_call,
                    'acd_call_offer' => $row[2],
                    'acd_call_handle' => $row[5],
                    'call_WI_lvl' => $row[6],
                    'call_abandon_short' => $row[4],
                    'call_abandon_long' => $row[3],
                    'abandon_percentage' => number_format((float)str_replace('%', '', $row[7]), 2),
                    'answer_percentage' => number_format((float)str_replace('%', '', $row[8]), 2),
                    'service_level' => number_format((float)str_replace('%', '', $row[9]), 2),
                    'mitel_insert_data_datetime' => Carbon::now(),
                    'filename' => basename($file),
                    'periodicity' => 'daily',
                    'created_by' => 'scheduler'
                ]);

                Log::debug(self::class . ' checkReportFile... Inserted data for date_call: ' . $date_call, ['Checking Date' => Carbon::now()]);
                dump('checkReportFile... Inserted data for date_call: ' . $date_call);
            } else {
                Log::debug(self::class . ' checkReportFile... Data already exists for date_call: ' . $date_call, ['Checking Date' => Carbon::now()]);
                dump('checkReportFile... Data already exists for date_call: ' . $date_call);
            }
        }
    }

    public static function sendAlert($message)
    {
        $apiUrl = 'http://192.168.68.159:778/api/alertnotification';

        $client = new Client();

        $data = [
            'token' => 'EPNOTIFY2022',
            'notify_code' => 'POMS_TALKDESK',
            'ws_receiver' => ['60132965282'],
            'notify_type' => 'ALERT',
            'duplicate_alert' => 'YES',
            'message' => $message,
            'remarks' => 'POMS SLA Talkdesk - File Missing Alert'
        ];

        $response = $client->post($apiUrl, [
            'json' => $data,
            'headers' => [
                'Content-type' => 'application/json'
            ]
        ]);

        $statusCode = $response->getStatusCode();
        $body = (string) $response->getBody();

        if ($statusCode === 200) {
            Log::debug(self::class . " Alert sent successfully: {$body}\n", ['Checking Date' => Carbon::now()]);
            dump("Alert sent successfully: {$body}\n");
        } else {
            Log::debug(self::class . " Failed to send alert: {$statusCode}\n", ['Checking Date' => Carbon::now()]);
            dump("Failed to send alert: {$body}\n");
        }
    }
}
