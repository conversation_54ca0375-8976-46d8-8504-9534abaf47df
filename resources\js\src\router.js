/*=========================================================================================
  File Name: router.js
  Description: Routes for vue-router. Lazy loading is enabled.
  Object Strucutre:
                    path => router path
                    name => router name
                    component(lazy loading) => component to load
                    meta : {
                      rule => which user can have access (ACL)
                      breadcrumb => Add breadcrumb to specific page
                      pageTitle => Display title besides breadcrumb
                    }
  ----------------------------------------------------------------------------------------
  Item Name: Vuesax Admin - VueJS Dashboard Admin Template
  Author: Pixinvent
  Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/


import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

const router = new Router({
    mode: 'history',
    base: '/',
    routes: [

        {
            // =============================================================================
            // MAIN LAYOUT ROUTES
            // =============================================================================
            path: '',
            component: () => import('./layouts/main/Main.vue'),
            children: [
                // =============================================================================
                // Theme Routes
                // =============================================================================
                {
                    path: '/',
                    name: 'home',
                    component: () => import('./views/Home.vue'),
                    meta: {
                        pageTitle: 'Home',
                        auth: true
                    }
                },
                {
                    path: '/sla-dashboard',
                    name: 'slaDashboard',
                    component: () => import('./views/SlaDashboard.vue'),
                    meta: {
                        breadcrumb: [
                            { title: 'Home', url: '/' },
                            { title: 'SLA Dashboard', active: true },
                        ],
                        pageTitle: 'SLA Dashboard',
                        auth: true
                    }
                },
                {
                    path: '/tech-dashboard',
                    name: 'technicalDashboard',
                    component: () => import('./views/TechDashboard.vue'),
                    meta: {
                        breadcrumb: [
                            { title: 'Home', url: '/' },
                            { title: 'Integration Service', active: true },
                        ],
                        pageTitle: 'Integration Service',
                        auth: true
                    }
                },
                {
                    path: '/sla-report',
                    name: 'slaReport',
                    component: () => import('./views/SlaReport.vue'),
                    meta: {
                        breadcrumb: [
                            { title: 'Home', url: '/' },
                            { title: 'SLA Report', active: true },
                        ],
                        pageTitle: 'SLA Report',
                        auth: true
                    }
                },
                {
                    path: '/inventory-report',
                    name: 'inventoryReport',
                    component: () => import('./views/inventoryReport.vue'),
                    meta: {
                        pageTitle: 'Inventory Report',
                        auth: true
                    }
                },
                {
                    path: '/crud-demo',
                    name: 'crudDemo',
                    component: () => import('./views/CrudDemo.vue'),
                },
                {
                    path: '/admin',
                    name: 'adminPage',
                    // meta: {auth: {role: ['admin', 'support']}},
                    component: () => import('./views/Administrator.vue'),
                    meta: {
                        breadcrumb: [
                            { title: 'Home', url: '/' },
                            { title: 'Administrator', active: true },
                        ],
                        pageTitle: 'Administration',
                        auth: true
                    }
                },
                // {
                //     path: '/list-crm-status',
                //     name: 'crmStatusList',
                //     component: () => import('./views/datalist/CrmStatusList.vue'),
                // },
                {
                    path: '/list-param-sla',
                    name: 'paramSettingSla',
                    component: () => import('./views/datalist/ParameterSetting.vue'),
                },
                {
                    path: '/list-users',
                    name: 'userList',
                    component: () => import('./views/datalist/UserList.vue'),
                    //meta: {auth: ['1', '2'], redirect: {name: 'login'}, forbiddenRedirect: '/403'},
                    // meta: {
                    //     auth: {roles: 2, redirect: {name: 'login'}, forbiddenRedirect: '/403'}
                    // }
                },
                {
                    path: '/ep-process-dashboard',
                    name: 'epProcessDashboard',
                    component: () => import('./views/techDashboard/KpiEpProcessDashboard.vue'),
                    meta: {
                        breadcrumb: [
                            { title: 'Home', url: '/' },
                            { title: 'KPI / eP Process Dashboard', active: true },
                        ],
                        pageTitle: 'KPI / eP Process Dashboard',
                        auth: true
                    }
                },
                {
                    path: '/profile',
                    name: 'profile',
                    component: () => import('./views/Profile.vue'),
                    meta: {
                        breadcrumb: [
                            { title: 'Home', url: '/' },
                            { title: 'Profile', active: true },
                        ],
                        pageTitle: 'Profile',
                        auth: true
                    }
                },
            ],
        },
        // =============================================================================
        // FULL PAGE LAYOUTS
        // =============================================================================
        {
            path: '',
            component: () => import('@/layouts/full-page/FullPage.vue'),
            children: [
                // =============================================================================
                // PAGES
                // =============================================================================
                {
                    path: '/login',
                    name: 'pageLogin',
                    component: () => import('@/views/pages/Login.vue')
                },
                {
                    path: '/pages/error-404',
                    name: 'pageError404',
                    component: () => import('@/views/pages/Error404.vue')
                },
                {
                    path: '/403',
                    name: 'pageError403',
                    component: () => import('@/views/pages/NotAuthorized.vue')
                },
            ]
        },
        // Redirect to 404 page, if no match found
        {
            path: '*',
            redirect: '/pages/error-404'
        }
    ],
})

console.log(router);

export default router
