(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[11],{

/***/ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/src/views/SlaReport.vue?vue&type=script&lang=js&":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib??ref--4-0!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/src/views/SlaReport.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ "./node_modules/jspdf/dist/jspdf.min.js");
/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jspdf__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var jspdf_autotable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jspdf-autotable */ "./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.js");
/* harmony import */ var jspdf_autotable__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jspdf_autotable__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var vuejs_datepicker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! vuejs-datepicker */ "./node_modules/vuejs-datepicker/dist/vuejs-datepicker.esm.js");
/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! date-fns */ "./node_modules/date-fns/index.js");
/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(date_fns__WEBPACK_IMPORTED_MODULE_3__);
function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }

function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance"); }

function _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === "[object Arguments]") return Array.from(iter); }

function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _typeof(obj) { if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



 //---------------------------------------------------

function separator(list) {
  if (Array.isArray(list) && list.length > 0) {
    var head = [Object.keys(list[0])];
    var body = list.map(function (e) {
      if (_typeof(e) === 'object' && e !== null) {
        return Object.keys(e).map(function (k) {
          return e[k];
        });
      }
    });
    return {
      head: head,
      body: body
    };
  }

  return {
    head: [[]],
    body: [[]]
  };
}

function inverse(l) {
  var key = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'name';

  if (Array.isArray(l) && l.length > 0) {
    var first = l[0];

    if (_typeof(first) === 'object' && first !== null) {
      var keys = Object.keys(first);
      var k = keys[0];
      return keys.slice(1).map(function (e, i) {
        var entry = _defineProperty({}, key, e);

        l.forEach(function (f, j) {
          entry[f[k]] = f[e];
        });
        return entry;
      });
    }
  }

  return [];
}

function friendlyTimeName(s) {
  if (typeof s === 'number' && !isNaN(s)) {
    if (s < 60) {
      if (s < 0) return "-(".concat(friendlyTimeName(Math.abs(s)), ")");
      return "".concat(s, "s");
    }

    if (s < 60 * 60) {
      var _m = parseInt(s / 60);

      var _ss = s - _m * 60;

      if (_ss <= 0) return "".concat(_m, "m");
      return "".concat(_m, "m ").concat(_ss, "s");
    }

    if (s < 60 * 60 * 60) {
      var _h = parseInt(s / 60 / 60);

      var _m2 = s - _h * 60 * 60;

      var _mm = parseInt(_m2 / 60);

      var _ss2 = s - _h * 60 * 60 - _mm * 60;

      if (_mm <= 0 && _ss2 <= 0) return "".concat(_h, "h");
      if (_ss2 <= 0) return "".concat(_h, "h ").concat(_mm, "m");
      if (_mm <= 0) return "".concat(_h, "h ").concat(_ss2, "s");
      return "".concat(_h, "h ").concat(_mm, "m ").concat(_ss2, "s");
    }

    var d = parseInt(s / 24 / 60 / 60);
    var h = s - d * 24 * 60 * 60;
    var hh = parseInt(h / 60 / 60);
    var m = s - d * 24 * 60 * 60 - hh * 60 * 60;
    var mm = parseInt(m / 60);
    var ss = s - d * 24 * 60 * 60 - hh * 60 * 60 - mm * 60;
    return "".concat(d, "d ").concat(hh, "h ").concat(mm, "m ").concat(ss, "s");
  }

  return s;
}

function pad(str, max) {
  str = str.toString();
  return str.length < max ? pad(" " + str, max) : str;
}

/* harmony default export */ __webpack_exports__["default"] = ({
  data: function data() {
    return {
      value1: '',
      mitelServiceLevel: '',
      mitelAbandonPercentage: '',
      mitelanswerPercentage: '',
      csPerformance: '',
      slaReportData: '',
      count_itcoord_within_sla: '',
      count_itcoord_exceed_sla: '',
      p_itcoord_sla: '',
      p_s1_within_sla: '',
      p_s2_within_sla: '',
      p_s3_within_sla: '',
      itserv_exceed_sla: '',
      reportdate: '',
      p_mitel_sla: '',
      p_mitel_abandon_call: '',
      count_cs_within_sla: '',
      count_cs_exceed_sla: '',
      p_cs_sla: '',
      count_itspec_within_sla: '',
      count_itspec_exceed_sla: '',
      p_ITSpec_SLA: '',
      count_within_sla_RequestNoteSO_submitRNForApproval: '',
      count_exceed_sla_RequestNoteSO_submitRNForApproval: '',
      p_within_sla_RequestNoteSO_submitRNForApproval: '',
      p_exceed_sla_RequestNoteSO_submitRNForApproval: '',
      count_within_sla_resPerfProxiesSLA_ContractSO_saveContractVer: '',
      count_exceed_sla_resPerfProxiesSLA_ContractSO_saveContractVer: '',
      p_within_sla_resPerfProxiesSLA_ContractSO_saveContractVer: '',
      p_exceed_sla_resPerfProxiesSLA_ContractSO_saveContractVer: '',
      count_within_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote: '',
      count_exceed_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote: '',
      p_within_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote: '',
      p_exceed_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote: '',
      count_within_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest: '',
      count_exceed_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest: '',
      p_within_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest: '',
      p_exceed_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest: '',
      count_within_sla_InvoiceSO_saveInvoice: '',
      count_exceed_sla_InvoiceSO_saveInvoice: '',
      p_within_sla_InvoiceSO_saveInvoice: '',
      p_exceed_sla_InvoiceSO_saveInvoice: '',
      count_within_sla_MofRegistrationSO_initiateSmApplicationTask: '',
      count_exceed_MofRegistrationSO_initiateSmApplicationTask: '',
      p_within_sla_MofRegistrationSO_initiateSmApplicationTask: '',
      p_exceed_sla_MofRegistrationSO_initiateSmApplicationTask: '',
      count_within_sla_VirtualCertBackingBean_viewVirtualCert: '',
      count_exceed_VirtualCertBackingBean_viewVirtualCert: '',
      p_within_sla_VirtualCertBackingBean_viewVirtualCert: '',
      p_exceed_sla_VirtualCertBackingBean_viewVirtualCert: '',
      // SM
      count_within_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile: '',
      count_exceed_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile: '',
      p_within_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile: '',
      p_exceed_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile: '',
      //QT- Proposal Submission
      count_within_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal: '',
      count_exceed_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal: '',
      p_within_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal: '',
      p_exceed_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal: '',
      //(QT) - Catalogue Search 
      count_within_sla_SC_SE_SpecificationBackingBean_pageChangeListener: '',
      count_exceed_sla_SC_SE_SpecificationBackingBean_pageChangeListener: '',
      p_within_sla_SC_SE_SpecificationBackingBean_pageChangeListener: '',
      p_exceed_sla_SC_SE_SpecificationBackingBean_pageChangeListener: '',
      //CT - Contract Management
      count_within_sla_CT_AC_AgreementSO_saveAgreement: '',
      count_exceed_sla_CT_AC_AgreementSO_saveAgreement: '',
      p_within_sla_CT_AC_AgreementSO_saveAgreement: '',
      p_exceed_sla_CT_AC_AgreementSO_saveAgreement: '',
      //Online Bidding
      count_within_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank: '',
      count_exceed_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank: '',
      p_within_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank: '',
      p_exceed_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank: '',
      //PP- Procumant Plan Submission $resPerfProxiesSLA
      count_within_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile: '',
      count_exceed_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile: '',
      p_within_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile: '',
      p_exceed_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile: '',
      p_total_system_performance_proxies: '',
      p_sum_system_performance_proxies_penality: '',
      total_sla_internal_fac: '',
      diff_sla_internal_fac: '',
      r_sec_dataintg_penality: '',
      pen_amt_sla_internal_fac: '',
      r_penality_10: '',
      c_cs_w_call_in_sla: '',
      c_cs_x_call_in_sla: '',
      p_cs_w_call_in_sla: '',
      p_cs_x_call_in_sla: '',
      c_cs_w_letter_sla: '',
      c_cs_x_letter_sla: '',
      p_cs_w_letter_sla: '',
      p_cs_x_letter_sla: '',
      c_cs_w_online_sla: '',
      c_cs_x_online_sla: '',
      p_cs_w_online_sla: '',
      p_cs_x_online_sla: '',
      c_cs_w_email_sla: '',
      c_cs_x_email_sla: '',
      p_cs_w_email_sla: '',
      p_cs_x_email_sla: '',
      itcoord_details: '',
      itspec_details: '',
      sla_caseapprover_details: '',
      itserv_exceed_details: '',
      securityintegrity_details: '',
      cMonthYear: new Date(),
      selected: [],
      log: [],
      users: []
    };
  },
  components: {
    Datepicker: vuejs_datepicker__WEBPACK_IMPORTED_MODULE_2__["default"]
  },
  mounted: function mounted() {
    var vm = this;
    vm.fetchReportList();
  },
  methods: {
    fetchMitelData: function fetchMitelData() {
      var _this = this;

      //let url = '/api/mitel/dashboard';
      var url = "/api/crm/slareportperformance/".concat(this.cMonthYear.getFullYear(), "/").concat(this.cMonthYear.getMonth() + 1);
      console.log(this.cMonthYear.getMonth() + 1);
      console.log(this.cMonthYear.getFullYear()); //pMonth=this.cMonthYear.getMonth();
      //pYear=this.cMonthYear.getFullYear();
      //sla_report_data

      this.$vs.loading();
      fetch(url).then(function (res) {
        return res.json();
      }).then(function (res) {
        _this.fetchCSPerformance(function () {
          //this.mitelData = res.mitel_data;
          _this.slaReportData = res.sla_report_data;
          console.log(_this.slaReportData);
          _this.count_itcoord_within_sla = _this.slaReportData.count_itcoord_within_sla;
          _this.mitelAbandonPercentage = _this.slaReportData.mitelAbandonPercentage;
          _this.mitelanswerPercentage = _this.slaReportData.mitelanswerPercentage;
          _this.count_itcoord_exceed_sla = _this.slaReportData.count_itcoord_exceed_sla;
          _this.p_itcoord_sla = _this.slaReportData.p_itcoord_sla; //S1 S2 S3

          _this.p_s1_within_sla = parseInt(_this.slaReportData.p_s1_within_sla) ? parseInt(_this.slaReportData.p_s1_within_sla) : 0;
          _this.p_s2_within_sla = parseInt(_this.slaReportData.p_s2_within_sla) ? parseInt(_this.slaReportData.p_s2_within_sla) : 0;
          _this.p_s3_within_sla = parseInt(_this.slaReportData.p_s3_within_sla) ? parseInt(_this.slaReportData.p_s3_within_sla) : 0; //IT Service

          _this.itserv_exceed_sla = _this.slaReportData.itserv_exceed_sla; //reportmonth + reportyear

          _this.reportdate = 'Reporting Month ' + _this.slaReportData.reportmonth + ' ' + _this.slaReportData.reportyear;
          _this.p_mitel_sla = _this.slaReportData.p_mitel_sla;
          _this.p_mitel_abandon_call = _this.slaReportData.p_mitel_abandon_call;
          _this.count_cs_within_sla = _this.slaReportData.count_cs_within_sla;
          _this.count_cs_exceed_sla = _this.slaReportData.count_cs_exceed_sla;
          _this.p_cs_sla = _this.slaReportData.p_cs_sla;
          _this.count_itspec_within_sla = _this.slaReportData.count_itspec_within_sla;
          _this.count_itspec_exceed_sla = _this.slaReportData.count_itspec_exceed_sla;
          _this.p_ITSpec_SLA = _this.slaReportData.p_ITSpec_SLA;
          _this.count_within_sla_RequestNoteSO_submitRNForApproval = _this.slaReportData.count_within_sla_RequestNoteSO_submitRNForApproval;
          _this.count_exceed_sla_RequestNoteSO_submitRNForApproval = _this.slaReportData.count_exceed_sla_RequestNoteSO_submitRNForApproval;
          _this.p_within_sla_RequestNoteSO_submitRNForApproval = _this.slaReportData.p_within_sla_RequestNoteSO_submitRNForApproval;
          _this.p_exceed_sla_RequestNoteSO_submitRNForApproval = _this.slaReportData.p_exceed_sla_RequestNoteSO_submitRNForApproval;
          _this.count_within_sla_resPerfProxiesSLA_ContractSO_saveContractVer = _this.slaReportData.count_within_sla_resPerfProxiesSLA_ContractSO_saveContractVer;
          _this.count_exceed_sla_resPerfProxiesSLA_ContractSO_saveContractVer = _this.slaReportData.count_exceed_sla_resPerfProxiesSLA_ContractSO_saveContractVer;
          _this.p_within_sla_resPerfProxiesSLA_ContractSO_saveContractVer = _this.slaReportData.p_within_sla_resPerfProxiesSLA_ContractSO_saveContractVer;
          _this.p_exceed_sla_resPerfProxiesSLA_ContractSO_saveContractVer = _this.slaReportData.p_exceed_sla_resPerfProxiesSLA_ContractSO_saveContractVer;
          _this.count_within_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote = _this.slaReportData.count_within_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote;
          _this.count_exceed_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote = _this.slaReportData.count_exceed_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote;
          _this.p_within_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote = _this.slaReportData.p_within_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote;
          _this.p_exceed_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote = _this.slaReportData.p_exceed_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote;
          _this.count_within_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest = _this.slaReportData.count_within_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest;
          _this.count_exceed_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest = _this.slaReportData.count_exceed_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest;
          _this.p_within_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest = _this.slaReportData.p_within_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest;
          _this.p_exceed_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest = _this.slaReportData.p_exceed_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest;
          _this.count_within_sla_InvoiceSO_saveInvoice = _this.slaReportData.count_within_sla_InvoiceSO_saveInvoice;
          _this.count_exceed_sla_InvoiceSO_saveInvoice = _this.slaReportData.count_exceed_sla_InvoiceSO_saveInvoice;
          _this.p_within_sla_InvoiceSO_saveInvoice = _this.slaReportData.p_within_sla_InvoiceSO_saveInvoice;
          _this.p_exceed_sla_InvoiceSO_saveInvoice = _this.slaReportData.p_exceed_sla_InvoiceSO_saveInvoice;
          _this.count_within_sla_MofRegistrationSO_initiateSmApplicationTask = _this.slaReportData.count_within_sla_MofRegistrationSO_initiateSmApplicationTask;
          _this.count_exceed_MofRegistrationSO_initiateSmApplicationTask = _this.slaReportData.count_exceed_MofRegistrationSO_initiateSmApplicationTask;
          _this.p_within_sla_MofRegistrationSO_initiateSmApplicationTask = _this.slaReportData.p_within_sla_MofRegistrationSO_initiateSmApplicationTask;
          _this.p_exceed_sla_MofRegistrationSO_initiateSmApplicationTask = _this.slaReportData.p_exceed_sla_MofRegistrationSO_initiateSmApplicationTask;
          _this.count_within_sla_VirtualCertBackingBean_viewVirtualCert = _this.slaReportData.count_within_sla_VirtualCertBackingBean_viewVirtualCert;
          _this.count_exceed_VirtualCertBackingBean_viewVirtualCert = _this.slaReportData.count_exceed_VirtualCertBackingBean_viewVirtualCert;
          _this.p_within_sla_VirtualCertBackingBean_viewVirtualCert = _this.slaReportData.p_within_sla_VirtualCertBackingBean_viewVirtualCert;
          _this.p_exceed_sla_VirtualCertBackingBean_viewVirtualCert = _this.slaReportData.p_exceed_sla_VirtualCertBackingBean_viewVirtualCert;
          _this.count_within_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = _this.slaReportData.count_within_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile;
          _this.count_exceed_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = _this.slaReportData.count_exceed_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile;
          _this.p_within_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = _this.slaReportData.p_within_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile;
          _this.p_exceed_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = _this.slaReportData.p_exceed_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile; //QT Proposal Submission

          _this.count_within_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal = _this.slaReportData.count_within_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal;
          _this.count_exceed_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal = _this.slaReportData.count_exceed_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal;
          _this.p_within_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal = _this.slaReportData.p_within_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal;
          _this.p_exceed_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal = _this.slaReportData.p_exceed_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal; //QT Catalogue Search count_within_sla_SC_SE_SpecificationBackingBean_pageChangeListener

          _this.count_within_sla_SC_SE_SpecificationBackingBean_pageChangeListener = _this.slaReportData.count_within_sla_SC_SE_SpecificationBackingBean_pageChangeListener;
          _this.count_exceed_sla_SC_SE_SpecificationBackingBean_pageChangeListener = _this.slaReportData.count_exceed_sla_SC_SE_SpecificationBackingBean_pageChangeListener;
          _this.p_within_sla_SC_SE_SpecificationBackingBean_pageChangeListener = _this.slaReportData.p_within_sla_SC_SE_SpecificationBackingBean_pageChangeListener;
          _this.p_exceed_sla_SC_SE_SpecificationBackingBean_pageChangeListener = _this.slaReportData.p_exceed_sla_SC_SE_SpecificationBackingBean_pageChangeListener; //CT - Contract Management count_within_sla_CT_AC_AgreementSO_saveAgreement

          _this.count_within_sla_CT_AC_AgreementSO_saveAgreement = _this.slaReportData.count_within_sla_CT_AC_AgreementSO_saveAgreement;
          _this.count_exceed_sla_CT_AC_AgreementSO_saveAgreement = _this.slaReportData.count_exceed_sla_CT_AC_AgreementSO_saveAgreement;
          _this.p_within_sla_CT_AC_AgreementSO_saveAgreement = _this.slaReportData.p_within_sla_CT_AC_AgreementSO_saveAgreement;
          _this.p_exceed_sla_CT_AC_AgreementSO_saveAgreement = _this.slaReportData.p_exceed_sla_CT_AC_AgreementSO_saveAgreement; //Online Bidding SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank

          _this.count_within_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank = _this.slaReportData.count_within_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank;
          _this.count_exceed_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank = _this.slaReportData.count_exceed_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank;
          _this.p_within_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank = _this.slaReportData.p_within_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank;
          _this.p_exceed_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank = _this.slaReportData.p_exceed_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank; //PP Procument Plan Submission PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile

          _this.count_within_sla_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = _this.slaReportData.count_within_sla_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile;
          _this.count_exceed_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = _this.slaReportData.count_exceed_sla_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile;
          _this.p_within_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = _this.slaReportData.p_within_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile;
          _this.p_exceed_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = _this.slaReportData.p_exceed_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile;
          _this.p_total_system_performance_proxies = _this.slaReportData.p_total_system_performance_proxies;
          _this.p_sum_system_performance_proxies_penality = _this.slaReportData.p_sum_system_performance_proxies_penality; //////total_sla_internal_fac,diff_sla_internal_fac, r_sec_dataintg_penality, pen_amt_sla_internal_fac

          _this.total_sla_internal_fac = _this.slaReportData.total_sla_internal_fac;
          _this.diff_sla_internal_fac = _this.slaReportData.diff_sla_internal_fac;
          _this.r_sec_dataintg_penality = _this.slaReportData.r_sec_dataintg_penality;
          _this.pen_amt_sla_internal_fac = _this.slaReportData.pen_amt_sla_internal_fac; //panelty rate 10.00

          _this.r_penality_10 = _this.slaReportData.r_penality_10;
          _this.c_cs_w_call_in_sla = _this.slaReportData.c_cs_w_call_in_sla;
          _this.c_cs_x_call_in_sla = _this.slaReportData.c_cs_x_call_in_sla;
          _this.p_cs_w_call_in_sla = _this.slaReportData.p_cs_w_call_in_sla;
          _this.p_cs_x_call_in_sla = _this.slaReportData.p_cs_x_call_in_sla;
          _this.c_cs_w_letter_sla = _this.slaReportData.c_cs_w_letter_sla;
          _this.c_cs_x_letter_sla = _this.slaReportData.c_cs_w_letter_sla;
          _this.p_cs_w_letter_sla = _this.slaReportData.p_cs_w_letter_sla;
          _this.p_cs_x_letter_sla = _this.slaReportData.p_cs_x_letter_sla;
          _this.c_cs_w_online_sla = _this.slaReportData.c_cs_w_online_sla;
          _this.c_cs_x_online_sla = _this.slaReportData.c_cs_x_online_sla;
          _this.p_cs_w_online_sla = _this.slaReportData.p_cs_w_online_sla;
          _this.p_cs_x_online_sla = _this.slaReportData.p_cs_x_online_sla;
          _this.c_cs_w_email_sla = _this.slaReportData.c_cs_w_email_sla;
          _this.c_cs_x_email_sla = _this.slaReportData.c_cs_x_email_sla;
          _this.p_cs_w_email_sla = _this.slaReportData.p_cs_w_email_sla;
          _this.p_cs_x_email_sla = _this.slaReportData.p_cs_x_email_sla;
          var total_pen_amt = 0;
          var total_itcoord_diff = 0;
          var headerItcoord = ['itcoord_name', 'itcoord_available_duration', 'itcoord_actual_duration', 'itcoord_diff', 'pen_rate', 'pen_amt'];
          console.log(_typeof(_this.slaReportData.itcoord_details), _this.slaReportData.itcoord_details);
          _this.itcoord_details = _this.slaReportData.itcoord_details.map(function (e, i) {
            return [''].concat(_toConsumableArray(headerItcoord.map(function (k) {
              if (k === 'itcoord_diff') {
                total_itcoord_diff += e[k];
              }

              if (k === 'pen_amt') {
                total_pen_amt += e[k];
              }

              if (k === 'itcoord_name') {
                return "".concat(i + 1, ". ").concat(e[k]);
              }

              if (k === 'itcoord_actual_duration' || k === 'itcoord_diff') {
                // return ((e[k] / 60 )).toFixed(2)
                return friendlyTimeName(parseFloat(e[k].toFixed(2)));
              }

              if (k === 'itcoord_available_duration') {
                return (e[k] / 60).toFixed(2);
              }

              if (k === 'pen_amt') {
                //return ((((e[k]) /60) /60).toFixed(2))
                return ' ';
              }

              if (k === 'pen_rate') {
                return ' ';
              }
              /*if (k==='pen_amt') {
                 return ((e[k])* 10)
              } */


              return e[k];
            })));
          });
          console.log('total_pen_amt', total_pen_amt); ///this.itcoord_details.push(['','','','','','',total_pen_amt])

          _this.itcoord_details.push(['', 'IT - Coordinatoor Total in Hour', '', '', {
            content: (total_itcoord_diff / 60 / 60).toFixed(2),
            styles: {
              halign: 'right'
            }
          }, {
            content: _this.r_penality_10.toFixed(2),
            styles: {
              halign: 'right'
            }
          }, {
            content: (total_pen_amt / 60 / 60).toFixed(2),
            styles: {
              halign: 'right'
            }
          }]); //{content: ((total_itspec_diff / 60) / 60).toFixed(2),styles: {halign: 'right'}}, 
          ///------------------------------------------------------------------------------------------------


          var total_pen_amt_itspec = 0;
          var total_itspec_diff = 0;
          var headerItspec = ['itspec_name', 'itspec_available_duration', 'itspec_actual_duration', 'itspec_diff', 'pen_rate', 'pen_amt'];
          console.log(_typeof(_this.slaReportData.itspec_details), _this.slaReportData.itspec_details);
          _this.itspec_details = _this.slaReportData.itspec_details.map(function (e, i) {
            return [''].concat(_toConsumableArray(headerItspec.map(function (k) {
              if (k === 'itspec_diff') {
                total_itspec_diff += e[k];
              }

              if (k === 'pen_amt') {
                total_pen_amt_itspec += e[k];
                return ' ';
              }

              if (k === 'itspec_name') {
                return "".concat(i + 1, ". ").concat(e[k]);
              }

              if (k === 'itspec_available_duration' || k === 'itspec_actual_duration' || k === 'itspec_diff' || k === 'pen_amt') {
                return pad((e[k] / 60 / 60).toFixed(2), 12);
              }

              if (k === 'pen_rate') {
                return ' ';
              }
              /* if (k==='pen_amt') {
                return ((e[k])* 10)
              } */


              return e[k];
            })));
          });
          console.log('total_pen_amt_itspec', total_pen_amt_itspec);
          console.log('this.itspec_details', _this.itspec_details); ///this.itcoord_details.push(['','','','','','',total_pen_amt])

          _this.itspec_details.push(['', 'IT - Specialist Total in Hour', '', '', {
            content: (total_itspec_diff / 60 / 60).toFixed(2),
            styles: {
              halign: 'right'
            }
          }, {
            content: _this.r_penality_10.toFixed(2),
            styles: {
              halign: 'right'
            }
          }, {
            content: (total_pen_amt_itspec / 60 / 60).toFixed(2),
            styles: {
              halign: 'right'
            }
          }]); ///---------------------------------------------------------------------------------------------------------------


          var total_pen_amt_secint = 0;
          var headerPenSecInt = ['itcoord_name', 'SLA', 'SCORE', 'DIFF', 'PEN_AMT'];
          console.log(_typeof(_this.slaReportData.securityintegrity_details), _this.slaReportData.securityintegrity_details);
          _this.securityintegrity_details = _this.slaReportData.securityintegrity_details.map(function (e, i) {
            return [''].concat(_toConsumableArray(headerPenSecInt.map(function (k) {
              if (k === 'PEN_AMT') {
                total_pen_amt_secint += parseInt(e[k]);
              }

              if (k === 'itcoord_name') {
                return "".concat(i + 1, ". ").concat(e[k]);
              }

              return e[k];
            })));
          });

          _this.securityintegrity_details.push(['', '', '', '', '0', '1000.00', total_pen_amt_secint.toFixed(2)]); ///------------------------sla_caseapprover_details--------------------------------------------------------------------------------


          var total_pen_amt_caseapprover = 0;
          var total_caseapprover_diff = 0;
          var headerItapprover = ['itapprover_name', 'itapprover_available_duration', 'itapprover_actual_duration', 'itapprover_diff', 'pen_rate', 'pen_amt'];
          console.log(_typeof(_this.sla_caseapprover_details), _this.slaReportData.sla_caseapprover_details);
          _this.sla_caseapprover_details = _this.slaReportData.sla_caseapprover_details.map(function (e, i) {
            return [''].concat(_toConsumableArray(headerItapprover.map(function (k) {
              if (k === 'itapprover_diff') {
                total_caseapprover_diff += e[k];
              }

              if (k === 'pen_amt') {
                total_pen_amt_caseapprover += e[k];
                return ' ';
              }

              if (k === 'itapprover_name') {
                return "".concat(i + 1, ". ").concat(e[k]);
              }

              if (k === 'itapprover_available_duration' || k === 'itapprover_actual_duration' || k === 'itapprover_diff' || k === 'pen_amt') {
                return pad((e[k] / 60 / 60).toFixed(2), 12);
              }

              if (k === 'pen_rate') {
                return ' ';
              }
              /* if (k==='pen_amt') {
                return ((e[k])* 10)
              } */


              return e[k];
            })));
          });
          console.log('total_pen_amt_caseapprover', total_pen_amt_caseapprover);
          console.log('this.itapprover_details', _this.sla_caseapprover_details);

          _this.sla_caseapprover_details.push(['', 'RT Resolution Time (S4) - Total in Hour', '', '', {
            content: (total_caseapprover_diff / 60 / 60).toFixed(2),
            styles: {
              halign: 'right'
            }
          }, {
            content: _this.r_penality_10.toFixed(2),
            styles: {
              halign: 'right'
            }
          }, {
            content: (total_pen_amt_caseapprover / 60 / 60).toFixed(2),
            styles: {
              halign: 'right'
            }
          }]); ///-------------------------------------------------------------------------------------------------------------
          ///------------------------itserv_exceed_details--------------------------------------------------------------------------------


          var total_pen_amt_itserv = 0;
          var total_itserv_diff = 0;
          var headerItServ = ['itserv_name', 'itserv_available_duration', 'itserv_actual_duration', 'itserv_diff', 'pen_rate', 'pen_amt'];
          console.log(_typeof(_this.itserv_exceed_details), _this.slaReportData.itserv_exceed_details);
          _this.itserv_exceed_details = _this.slaReportData.itserv_exceed_details.map(function (e, i) {
            return [''].concat(_toConsumableArray(headerItServ.map(function (k) {
              if (k === 'itserv_diff') {
                total_itserv_diff += e[k];
              }

              if (k === 'pen_amt') {
                total_pen_amt_itserv += e[k];
                return ' ';
              }

              if (k === 'itserv_name') {
                return "".concat(i + 1, ". ").concat(e[k]);
              }

              if (k === 'itserv_available_duration' || k === 'itserv_actual_duration' || k === 'itserv_diff' || k === 'pen_amt') {
                return pad((e[k] / 60 / 60).toFixed(2), 12);
              }

              if (k === 'pen_rate') {
                return ' ';
              }
              /* if (k==='pen_amt') {
                return ((e[k])* 10)
              } */


              return e[k];
            })));
          });
          console.log('total_pen_amt_itserv', total_pen_amt_itserv); ///this.itcoord_details.push(['','','','','','',total_pen_amt])

          _this.itserv_exceed_details.push(['', 'Service IT Request Exceed - Total in Hour', '', '', {
            content: (total_itserv_diff / 60 / 60).toFixed(2),
            styles: {
              halign: 'right'
            }
          }, {
            content: _this.r_penality_10.toFixed(2),
            styles: {
              halign: 'right'
            }
          }, {
            content: (total_pen_amt_itserv / 60 / 60).toFixed(2),
            styles: {
              halign: 'right'
            }
          }]); ///-------------------------------------------------------------------------------------------------------------


          _this.createPDF();

          _this.$vs.loading.close();
        });
      }).catch(function (err) {
        return console.log(err);
      });
    },
    fetchCSPerformance: function fetchCSPerformance(cb) {
      var _this2 = this;

      //let url = '/api/mitel/dashboard';
      var url = "/api/crm/slareportperformance/".concat(this.cMonthYear.getFullYear(), "/").concat(this.cMonthYear.getMonth() + 1);
      fetch(url).then(function (res) {
        return res.json();
      }).then(function (data) {
        // console.log(data);
        var d = inverse(Object.keys(data).map(function (k) {
          return data[k];
        })); //console.log(d);
        ///this.csPerformance=separator(d);

        _this2.csPerformance = separator(Object.keys(data).map(function (k) {
          return data[k];
        })); // this.csPerformance=separator(Object.keys(data).map(k=>JSON.stringify(data[k])));

        if (typeof cb === 'function') cb();
      }).catch(function (err) {
        return console.log(err);
      });
    },
    createPDF: function createPDF() {
      var _doc$autoTable, _doc$autoTable2, _doc$autoTable3, _doc$autoTable4, _doc$autoTable5;

      var currentDate = Object(date_fns__WEBPACK_IMPORTED_MODULE_3__["format"])(new Date(), 'yyyyMMddHHmmss');
      console.log(this.cMonthYear);
      console.log(currentDate);
      var pdfName = 'sla_report_' + currentDate;
      var doc = new jspdf__WEBPACK_IMPORTED_MODULE_0___default.a('', '', 'a4'); ///var pdf = new jsPDF('p', 'pt', 'letter');

      var faker = window.faker;
      doc.autoTable({
        styles: {
          lineColor: [169, 169, 169],
          lineWidth: 0.3
        },
        headStyles: {
          fontSize: 14,
          halign: 'center'
        },
        head: [['Ministry of Finance Between Commerce Dot Com Sdn Bhd', this.reportdate]],
        body: [['CONTRACT', ''], [{
          content: 'IT Department',
          styles: {
            halign: 'right'
          }
        }, ''], [{
          content: 'Operation Department',
          styles: {
            halign: 'right'
          }
        }, ''], ['APPROVALS', ''], [{
          content: 'IT Department',
          styles: {
            halign: 'right'
          }
        }, ''], [{
          content: 'Operation Department',
          styles: {
            halign: 'right'
          }
        }, ''], [{
          content: 'Date Generated',
          styles: {
            halign: 'right'
          }
        }, '']]
      });
      doc.autoTable({
        styles: {
          lineColor: [169, 169, 169],
          lineWidth: 0.3
        },
        head: [['Monthly Summary']],
        body: [['Remarks :']]
      });
      doc.autoTable({
        styles: {
          lineColor: [169, 169, 169],
          lineWidth: 0.3
        },
        head: [['No.', 'SLA Item', 'Treshold', 'Score', 'Amount']],
        body: [['1', 'ePerolehan Overall Avaliability', '', '', ''], ['1.1', 'System Availiability (Infra)', {
          content: '99.6%',
          styles: {
            halign: 'right'
          }
        }, '', ''], ['2', 'System Security & Data Integrity ', {
          content: '0',
          styles: {
            halign: 'right'
          }
        }, '', ''], ['3', 'System Performance (Proxies Response Time(max 3 sec)', {
          content: '> 95%',
          styles: {
            halign: 'right'
          }
        }, '', ''], ['4', 'Customer Service Management', '', '', ''], ['4.1', 'Call Center (Mitel)', '', '', ''], ['4.1.1', 'Answered Call Within 10 sec', {
          content: '80.0%',
          styles: {
            halign: 'right'
          }
        }, {
          content: this.p_mitel_sla.toFixed(2),
          styles: {
            halign: 'right'
          }
        }, ''], ['4.1.2', 'Abandoned Call', {
          content: '< 10%',
          styles: {
            halign: 'right'
          }
        }, {
          content: this.p_mitel_abandon_call.toFixed(2),
          styles: {
            halign: 'right'
          }
        }, ''], ['4.2', 'Other Channels', '', '', ''], ['4.2.1', 'Letters (working days)', {
          content: '2',
          styles: {
            halign: 'right'
          }
        }, {
          content: this.p_cs_w_letter_sla.toFixed(2),
          styles: {
            halign: 'right'
          }
        }, ''], ['4.2.2', 'Facsimile (working days)', {
          content: '2',
          styles: {
            halign: 'right'
          }
        }, '', ''], ['4.2.3', 'Telephone (minutes)', {
          content: '15',
          styles: {
            halign: 'right'
          }
        }, {
          content: this.p_cs_w_call_in_sla.toFixed(2),
          styles: {
            halign: 'right'
          }
        }, ''], ['4.2.4', 'Online (minutes)', '', {
          content: this.p_cs_w_online_sla.toFixed(2),
          styles: {
            halign: 'right'
          }
        }, ''], ['4.2.5', 'E-mails (minutes)', {
          content: '15',
          styles: {
            halign: 'right'
          }
        }, {
          content: this.p_cs_w_email_sla.toFixed(2),
          styles: {
            halign: 'right'
          }
        }, ''], ['5', 'Incident Management', '', '', ''], ['5.1', 'IT Coordinator (minutes)', {
          content: '15',
          styles: {
            halign: 'right'
          }
        }, {
          content: this.p_itcoord_sla.toFixed(2),
          styles: {
            halign: 'right'
          }
        }, ''], ['5.2', 'IT Specialist (hours)', {
          content: '4',
          styles: {
            halign: 'right'
          }
        }, {
          content: this.p_ITSpec_SLA.toFixed(2),
          styles: {
            halign: 'right'
          }
        }, ''], ['5.3', 'RT Resolution Time (S1)', {
          content: '1 Days',
          styles: {
            halign: 'right'
          }
        }, {
          content: this.p_s1_within_sla.toFixed(2),
          styles: {
            halign: 'right'
          }
        }, ''], ['5.4', 'RT Resolution Time (S2)', {
          content: '3 Days',
          styles: {
            halign: 'right'
          }
        }, {
          content: this.p_s2_within_sla.toFixed(2),
          styles: {
            halign: 'right'
          }
        }, ''], ['5.5', 'RT Resolution Time (S3)', {
          content: '5 Days',
          styles: {
            halign: 'right'
          }
        }, {
          content: this.p_s3_within_sla.toFixed(2),
          styles: {
            halign: 'right'
          }
        }, ''], ['5.6', 'RT Resolution Time (S4)', {
          content: 'Varies',
          styles: {
            halign: 'right'
          }
        }, '', ''], ['6', 'Service Request (agreed by both parties)', {
          content: 'Varies',
          styles: {
            halign: 'right'
          }
        }, {
          content: this.itserv_exceed_sla.toFixed(2),
          styles: {
            halign: 'right'
          }
        }, '']]
      });
      doc.text("Service Level Agreement (SLA)", 14, 10);
      doc.autoTable({
        styles: {
          lineColor: [169, 169, 169],
          lineWidth: 0.3
        },
        head: [['Details Report                                                                                               Total Penalty (RM)']],
        body: [[{
          content: 'Note : Following report details contains one month accumulated problem cases.',
          styles: {
            fontSize: 8
          }
        }]]
      });
      doc.autoTable((_doc$autoTable = {
        styles: {
          overflow: 'hidden'
        }
      }, _defineProperty(_doc$autoTable, "styles", {
        lineColor: [169, 169, 169],
        lineWidth: 0.3
      }), _defineProperty(_doc$autoTable, "allSectionHooks", true), _defineProperty(_doc$autoTable, "margin", {
        top: 10
      }), _defineProperty(_doc$autoTable, "margin", {
        right: 15
      }), _defineProperty(_doc$autoTable, "head", [['1', {
        content: 'ePerolehan Overall Availability : ',
        colSpan: 6,
        styles: {
          halign: 'left'
        }
      }]]), _defineProperty(_doc$autoTable, "body", [['1.1', {
        content: 'System Availability (Infra)',
        colSpan: 6,
        styles: {
          halign: 'left'
        }
      }], ['', 'Summary', 'SLA', 'SCORE', 'DIFF', 'PEN RATE', 'PEN AMT'], ['', 'Total Downtime Duration (minutes)', '', '', '', '', ''], ['', '', '', '', '', 'Grand Total : ', '']]), _doc$autoTable));
      doc.autoTable((_doc$autoTable2 = {
        styles: {
          overflow: 'hidden'
        }
      }, _defineProperty(_doc$autoTable2, "styles", {
        lineColor: [169, 169, 169],
        lineWidth: 0.3
      }), _defineProperty(_doc$autoTable2, "allSectionHooks", true), _defineProperty(_doc$autoTable2, "margin", {
        top: 10
      }), _defineProperty(_doc$autoTable2, "margin", {
        right: 15
      }), _defineProperty(_doc$autoTable2, "head", [['2', {
        content: 'System Security/Data Integrity',
        colSpan: 6,
        styles: {
          halign: 'left'
        }
      }]]), _defineProperty(_doc$autoTable2, "body", [['', '', 'SLA', 'SCORE', 'DIFF', 'PEN RATE', 'PEN AMT']].concat(_toConsumableArray(this.securityintegrity_details))), _doc$autoTable2));
      doc.autoTable((_doc$autoTable3 = {
        styles: {
          overflow: 'hidden'
        }
      }, _defineProperty(_doc$autoTable3, "styles", {
        lineColor: [169, 169, 169],
        lineWidth: 0.3,
        halign: 'right'
      }), _defineProperty(_doc$autoTable3, "allSectionHooks", true), _defineProperty(_doc$autoTable3, "margin", {
        top: 10
      }), _defineProperty(_doc$autoTable3, "columnStyles", {
        2: {
          cellWidth: 20,
          styles: {
            halign: 'right'
          }
        },
        3: {
          cellWidth: 20,
          styles: {
            halign: 'right'
          }
        },
        4: {
          cellWidth: 20,
          styles: {
            halign: 'right'
          }
        },
        5: {
          cellWidth: 20
        },
        6: {
          cellWidth: 20
        } // etc

      }), _defineProperty(_doc$autoTable3, "head", [['3', {
        content: 'System Performance Proxies Respond Time (max 3 sec)',
        styles: {
          halign: 'left'
        }
      }, {
        content: 'SLA',
        styles: {
          halign: 'left'
        }
      }, {
        content: 'ACTUAL',
        styles: {
          halign: 'left'
        }
      }, {
        content: 'DIFF',
        styles: {
          halign: 'left'
        }
      }, {
        content: 'PEN RATE',
        styles: {
          halign: 'left'
        }
      }, {
        content: 'PEN AMT',
        styles: {
          halign: 'left'
        }
      }]]), _defineProperty(_doc$autoTable3, "body", [['', {
        content: '1.  Login',
        styles: {
          halign: 'left'
        }
      }, '95%', '', '', '', ''], ['', {
        content: '2.  Procurement Plan Submission',
        styles: {
          halign: 'left'
        }
      }, '95%', this.p_within_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile, this.p_exceed_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile, '', ''], ['', {
        content: '3.  Proposal Submission',
        styles: {
          halign: 'left'
        }
      }, '95%', this.p_within_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal, this.p_exceed_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal, '', ''], ['', {
        content: '4.  Request Note Submission',
        styles: {
          halign: 'left'
        }
      }, '95%', this.p_within_sla_RequestNoteSO_submitRNForApproval, this.p_exceed_sla_RequestNoteSO_submitRNForApproval, '', ''], ['', {
        content: '5.  Online Bidding',
        styles: {
          halign: 'left'
        }
      }, '95%', this.p_within_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank, this.p_exceed_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank, '', ''], ['', {
        content: '6.  Fulfilment Details (FD) Submission',
        styles: {
          halign: 'left'
        }
      }, '95%', this.p_within_sla_resPerfProxiesSLA_ContractSO_saveContractVer, this.p_exceed_sla_resPerfProxiesSLA_ContractSO_saveContractVer, '', ''], ['', {
        content: '7.  Request Final Agrement',
        styles: {
          halign: 'left'
        }
      }, '95%', this.p_within_sla_CT_AC_AgreementSO_saveAgreement, this.p_exceed_sla_CT_AC_AgreementSO_saveAgreement, '', ''], ['', {
        content: '8.  Purchase Request (PR) Submission',
        styles: {
          halign: 'left'
        }
      }, '95%', this.p_within_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote, this.p_exceed_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote, '', ''], ['', {
        content: '9.  FRN Creation',
        styles: {
          halign: 'left'
        }
      }, '95%', this.p_within_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest, this.p_exceed_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest, '', ''], ['', {
        content: '10. Invoice Creation',
        styles: {
          halign: 'left'
        }
      }, '95%', this.p_within_sla_InvoiceSO_saveInvoice, this.p_exceed_sla_InvoiceSO_saveInvoice, '', ''], ['', {
        content: '11. Submit Supplier Application',
        styles: {
          halign: 'left'
        }
      }, '95%', this.p_within_sla_MofRegistrationSO_initiateSmApplicationTask, this.p_exceed_sla_MofRegistrationSO_initiateSmApplicationTask, '', ''], ['', {
        content: '12. Catalogue Search',
        styles: {
          halign: 'left'
        }
      }, '95%', '', '', '', ''], ['', {
        content: '13. Search for Template',
        styles: {
          halign: 'left'
        }
      }, '95%', this.p_within_sla_SC_SE_SpecificationBackingBean_pageChangeListener, this.p_exceed_sla_SC_SE_SpecificationBackingBean_pageChangeListener, '', ''], ['', {
        content: '14. Load Virtual Cert',
        styles: {
          halign: 'left'
        }
      }, '95%', this.p_within_sla_VirtualCertBackingBean_viewVirtualCert, this.p_exceed_sla_VirtualCertBackingBean_viewVirtualCert, '', ''], ['', {
        content: '15. Processing Officier view detail Apps',
        styles: {
          halign: 'left'
        }
      }, '95%', '', '', '', ''], ['', {
        content: '16. View Company Profile Apps',
        styles: {
          halign: 'left'
        }
      }, '95%', this.p_within_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile, this.p_exceed_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile, '', ''], [{
        content: 'System Performance Total :',
        colSpan: 4,
        styles: {
          halign: 'right'
        }
      }, this.p_total_system_performance_proxies.toFixed(2), '1000', this.p_sum_system_performance_proxies_penality]]), _doc$autoTable3));
      doc.autoTable((_doc$autoTable4 = {
        styles: {
          overflow: 'hidden'
        }
      }, _defineProperty(_doc$autoTable4, "styles", {
        lineColor: [169, 169, 169],
        lineWidth: 0.3,
        halign: 'right'
      }), _defineProperty(_doc$autoTable4, "allSectionHooks", true), _defineProperty(_doc$autoTable4, "margin", {
        top: 10
      }), _defineProperty(_doc$autoTable4, "margin", {
        right: 15
      }), _defineProperty(_doc$autoTable4, "margin", {
        width: 522
      }), _defineProperty(_doc$autoTable4, "head", [['4', {
        content: 'Customer Service Management',
        colSpan: 6,
        styles: {
          halign: 'left'
        }
      }]]), _defineProperty(_doc$autoTable4, "body", [['', 'Call Center (Mitel)', 'SLA', 'SCORE', 'DIFF', 'PEN RATE', 'PEN AMT'], ['', {
        content: 'Call Answered Rate',
        styles: {
          halign: 'left'
        }
      }, 80, this.p_mitel_sla, '', '', ''], ['', {
        content: 'Call Abandoned Rate',
        styles: {
          halign: 'left'
        }
      }, 10, this.p_mitel_abandon_call, '', '', ''], [{
        content: 'Call Center Total : ',
        colSpan: 6,
        styles: {
          halign: 'right'
        }
      }, ''], ['', 'Other Channels', 'SLA', 'Within SLA', 'DIFF', 'PEN RATE', 'PEN AMT'], ['', {
        content: 'Letters (working days)',
        styles: {
          halign: 'left'
        }
      }, 80, '', '', '', ''], ['', {
        content: 'Facsimile (working days)',
        styles: {
          halign: 'left'
        }
      }, 80, '', '', '', ''], ['', {
        content: 'Telephone (minutes)',
        styles: {
          halign: 'left'
        }
      }, 80, this.p_cs_w_call_in_sla, '', '', ''], ['', {
        content: 'Online (minutes)',
        styles: {
          halign: 'left'
        }
      }, 80, this.p_cs_w_online_sla, '', '', ''], ['', {
        content: 'Email (minutes)',
        styles: {
          halign: 'left'
        }
      }, 80, this.p_cs_w_call_in_sla, '', '', ''], [{
        content: 'Other Channels Total : ',
        colSpan: 6,
        styles: {
          halign: 'right'
        }
      }, ''], [{
        content: 'Grand Total : ',
        colSpan: 6,
        styles: {
          halign: 'right'
        }
      }, '']]), _doc$autoTable4));
      doc.autoTable((_doc$autoTable5 = {
        styles: {
          overflow: 'hidden'
        }
      }, _defineProperty(_doc$autoTable5, "styles", {
        lineColor: [169, 169, 169],
        lineWidth: 0.3,
        halign: 'left'
      }), _defineProperty(_doc$autoTable5, "allSectionHooks", true), _defineProperty(_doc$autoTable5, "columnStyles", {
        2: {
          cellWidth: 20
        },
        3: {
          cellWidth: 20
        },
        4: {
          cellWidth: 20
        },
        5: {
          cellWidth: 20
        },
        6: {
          cellWidth: 20
        } // etc

      }), _defineProperty(_doc$autoTable5, "head", [['5', {
        content: 'Incident Management',
        colSpan: 6,
        styles: {
          halign: 'left'
        }
      }]]), _defineProperty(_doc$autoTable5, "body", [['', 'IT Coordinator', {
        content: ' SLA (Min) ',
        styles: {
          halign: 'right'
        }
      }, 'ACTUAL', ' DIFF ', {
        content: ' PEN RATE (RM)',
        width: 50,
        styles: {
          halign: 'right'
        }
      }, {
        content: ' PEN AMT (Hours) ',
        styles: {
          cellwidth: 'wrap',
          halign: 'right'
        }
      }]].concat(_toConsumableArray(this.itcoord_details), [['', '', '', '', '', '', ''], ['', 'IT Specialist', ' SLA (Hour) ', 'ACTUAL', ' DIFF ', ' PEN RATE ', ' PEN AMT ']], _toConsumableArray(this.itspec_details), [['', '', '', '', '', '', ''], ['', 'RT Resolution Time (S4) ', ' SLA (Hour) ', 'ACTUAL', ' DIFF ', ' PEN RATE ', ' PEN AMT ']], _toConsumableArray(this.sla_caseapprover_details), [['', '', '', '', '', '', ''], ['', 'Service IT Request', '', '', '', '', '']], _toConsumableArray(this.itserv_exceed_details))), _doc$autoTable5)); //-------------dynamic table-----------------------
      /// const {head,body} =this.csPerformance;
      ///doc.autoTable ({head,body})
      //-------------------------------------

      this.storeGeneratedReport(pdfName + '.pdf');
      doc.save(pdfName + '.pdf');
    },
    handleSearch: function handleSearch(searching) {
      console.log("The user searched for: ".concat(searching));
    },
    handleChangePage: function handleChangePage(page) {
      console.log("The user changed the page to: ".concat(page));
    },
    handleSort: function handleSort(key, active) {
      console.log("the user ordered: ".concat(key, " ").concat(active));
    },
    storeGeneratedReport: function storeGeneratedReport(filename) {
      var _this3 = this;

      // get current user
      this.$http({
        url: "user",
        method: 'GET'
      }).then(function (res) {
        // Store
        fetch('api/report/store', {
          method: 'post',
          body: JSON.stringify({
            file_name: filename,
            created_by: res.data.name
          }),
          headers: {
            'content-type': 'application/json'
          }
        }).then(function (res) {
          return res.json();
        }).then(function (data) {
          _this3.$vs.notify({
            color: 'success',
            title: 'Success!',
            text: 'Report successfully generated',
            iconPack: 'feather',
            icon: 'icon-check-circle'
          });

          _this3.fetchReportList();
        }).catch(function (err) {
          return console.log(err);
        });
      }).catch(function (err) {
        return console.log(err);
      });
    },
    fetchReportList: function fetchReportList() {
      var _this4 = this;

      fetch('api/report/list').then(function (res) {
        return res.json();
      }).then(function (res) {
        _this4.users = res.data;
      }).catch(function (err) {
        return console.log(err);
      });
    }
  }
});

/***/ }),

/***/ "./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/src/views/SlaReport.vue?vue&type=template&id=751f76c2&":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/src/views/SlaReport.vue?vue&type=template&id=751f76c2& ***!
  \***********************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
var render = function() {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  return _c(
    "div",
    { staticClass: "vx-col w-full mb-base" },
    [
      _c("vx-card", { attrs: { title: "SLA Report" } }, [
        _c("div", { staticClass: "vx-row mb-6" }, [
          _c("div", { staticClass: "vx-col sm:w-1/3 w-full" }, [
            _c("span", [_vm._v("Report Month/Year")])
          ]),
          _vm._v(" "),
          _c(
            "div",
            { staticClass: "vx-col sm:w-2/3 w-full" },
            [
              _c("datepicker", {
                attrs: {
                  format: "MMMM yyyy",
                  minimumView: "month",
                  maximumView: "year"
                },
                model: {
                  value: _vm.cMonthYear,
                  callback: function($$v) {
                    _vm.cMonthYear = $$v
                  },
                  expression: "cMonthYear"
                }
              })
            ],
            1
          )
        ]),
        _vm._v(" "),
        _c("div", { staticClass: "vx-row" }, [
          _c(
            "div",
            { staticClass: "vx-col w-full" },
            [
              _c(
                "vs-button",
                { staticClass: "mb-2", on: { click: _vm.fetchMitelData } },
                [_vm._v("Generate Report")]
              )
            ],
            1
          )
        ])
      ]),
      _vm._v(" "),
      _c(
        "vx-card",
        {
          staticClass: "mt-8",
          attrs: { title: "List of Generate SLA Report" }
        },
        [
          _c(
            "vs-table",
            {
              attrs: {
                sst: true,
                pagination: "",
                "max-items": "10",
                search: "",
                data: _vm.users
              },
              on: {
                search: _vm.handleSearch,
                "change-page": _vm.handleChangePage,
                sort: _vm.handleSort
              },
              scopedSlots: _vm._u([
                {
                  key: "default",
                  fn: function(ref) {
                    var data = ref.data
                    return _vm._l(data, function(tr, indextr) {
                      return _c(
                        "vs-tr",
                        { key: indextr, attrs: { data: tr } },
                        [
                          _c("vs-td", { attrs: { data: data[indextr].id } }, [
                            _vm._v(
                              "\n                        " +
                                _vm._s(data[indextr].id) +
                                "\n                    "
                            )
                          ]),
                          _vm._v(" "),
                          _c(
                            "vs-td",
                            { attrs: { data: data[indextr].file_name } },
                            [
                              _vm._v(
                                "\n                        " +
                                  _vm._s(data[indextr].file_name) +
                                  "\n                    "
                              )
                            ]
                          ),
                          _vm._v(" "),
                          _c(
                            "vs-td",
                            { attrs: { data: data[indextr].created_at } },
                            [
                              _vm._v(
                                "\n                        " +
                                  _vm._s(data[indextr].created_at) +
                                  "\n                    "
                              )
                            ]
                          ),
                          _vm._v(" "),
                          _c(
                            "vs-td",
                            { attrs: { data: data[indextr].created_by } },
                            [
                              _vm._v(
                                "\n                        " +
                                  _vm._s(data[indextr].created_by) +
                                  "\n                    "
                              )
                            ]
                          ),
                          _vm._v(" "),
                          _c("vs-td", { attrs: { data: data[indextr].id } }, [
                            _vm._v(
                              "\n                        " +
                                _vm._s(data[indextr].action) +
                                "\n                    "
                            )
                          ])
                        ],
                        1
                      )
                    })
                  }
                }
              ]),
              model: {
                value: _vm.selected,
                callback: function($$v) {
                  _vm.selected = $$v
                },
                expression: "selected"
              }
            },
            [
              _c(
                "template",
                { slot: "thead" },
                [
                  _c("vs-th", { attrs: { "sort-key": "id" } }, [_vm._v("ID")]),
                  _vm._v(" "),
                  _c("vs-th", { attrs: { "sort-key": "filename" } }, [
                    _vm._v("File Name")
                  ]),
                  _vm._v(" "),
                  _c("vs-th", { attrs: { "sort-key": "created_date" } }, [
                    _vm._v("Created Date")
                  ]),
                  _vm._v(" "),
                  _c("vs-th", { attrs: { "sort-key": "created_by" } }, [
                    _vm._v("Created By")
                  ]),
                  _vm._v(" "),
                  _c("vs-th", { attrs: { "sort-key": "action" } }, [
                    _vm._v("Action")
                  ])
                ],
                1
              )
            ],
            2
          )
        ],
        1
      )
    ],
    1
  )
}
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ "./resources/js/src/views/SlaReport.vue":
/*!**********************************************!*\
  !*** ./resources/js/src/views/SlaReport.vue ***!
  \**********************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _SlaReport_vue_vue_type_template_id_751f76c2___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SlaReport.vue?vue&type=template&id=751f76c2& */ "./resources/js/src/views/SlaReport.vue?vue&type=template&id=751f76c2&");
/* harmony import */ var _SlaReport_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SlaReport.vue?vue&type=script&lang=js& */ "./resources/js/src/views/SlaReport.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport *//* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");





/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _SlaReport_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _SlaReport_vue_vue_type_template_id_751f76c2___WEBPACK_IMPORTED_MODULE_0__["render"],
  _SlaReport_vue_vue_type_template_id_751f76c2___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/js/src/views/SlaReport.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./resources/js/src/views/SlaReport.vue?vue&type=script&lang=js&":
/*!***********************************************************************!*\
  !*** ./resources/js/src/views/SlaReport.vue?vue&type=script&lang=js& ***!
  \***********************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SlaReport_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib??ref--4-0!../../../../node_modules/vue-loader/lib??vue-loader-options!./SlaReport.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/src/views/SlaReport.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SlaReport_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/js/src/views/SlaReport.vue?vue&type=template&id=751f76c2&":
/*!*****************************************************************************!*\
  !*** ./resources/js/src/views/SlaReport.vue?vue&type=template&id=751f76c2& ***!
  \*****************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_SlaReport_vue_vue_type_template_id_751f76c2___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../node_modules/vue-loader/lib??vue-loader-options!./SlaReport.vue?vue&type=template&id=751f76c2& */ "./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/src/views/SlaReport.vue?vue&type=template&id=751f76c2&");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_SlaReport_vue_vue_type_template_id_751f76c2___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_SlaReport_vue_vue_type_template_id_751f76c2___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });



/***/ })

}]);