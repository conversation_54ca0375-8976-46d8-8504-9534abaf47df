<?php

namespace App\Http\Controllers;

use App\Services\PomsService;
use Carbon\Carbon;
use DB;
use Illuminate\Support\Facades\Cache;

class CallCenterSlaController extends Controller
{
    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    public static function pomsService() {
        return new PomsService();
    }

    public function getMitelDashboardData() {

        $currentDate = Carbon::now()->format('Y/m/d');
        $startDate = $endDate = $currentDate;

        $resMitelData = self::pomsService()->getMitelSlaData($startDate, $endDate)[0];
        if($resMitelData) {
            $resMitelData->call_offered = intval($resMitelData->call_offered);
            $resMitelData->call_answered = intval($resMitelData->call_answered);
            $resMitelData->call_abandon = intval($resMitelData->call_abandon);
            $resMitelData->call_abandoned_long = intval($resMitelData->call_abandoned_long);
            $resMitelData->call_abandoned_short = intval($resMitelData->call_abandoned_short);
            $resMitelData->call_handled = intval($resMitelData->call_handled);
            $resMitelData->abandon_perc = round($resMitelData->abandon_perc, 1);
            $resMitelData->answer_perc = round($resMitelData->answer_perc, 1);
            $resMitelData->service_level = round($resMitelData->service_level, 1);
        }

        return response()->json([
            'mitel_data' => $resMitelData
        ]);
    }

    public function getAspectDashboardData() {

        $currentDate = Carbon::now()->setHour(0)->setMinute(0)->setSecond(0)->setMicrosecond(0);

        $result = self::pomsService()->getNewAspectSlaData($currentDate)[0];

        // $abandonPercentage = ($result->call_long / $result->call_offer) * 100;
        // $answerPercentage = ($result->call_handle / $result->call_offer) * 100;

        if($result) {
            $result->call_offered = intval($result->acd_call_offer);
            $result->call_abandoned_long = intval($result->call_abandon_long);
            $result->call_handled = intval($result->acd_call_handle);
            $result->abandon_perc = round($result->abandon_percentage, 1);
            $result->answer_perc = round($result->answer_percentage, 1);
            $result->service_level = round($result->service_level, 1);
        }

        return response()->json([
            'data' => $result
        ]);
    }

    public function getTalkdeskDashboardData() {

        $cacheKey = 'call_center_performance_data';
        $cacheDuration = now()->addHours(24);

        // Check if data exists in the cache
        if (Cache::has($cacheKey)) {
            $cachedData = Cache::get($cacheKey);
            return response()->json($cachedData);
        }

        $result = self::pomsService()->getTalkdeskDashboardSlaData();

        // $abandonPercentage = ($result->call_long / $result->call_offer) * 100;
        // $answerPercentage = ($result->call_handle / $result->call_offer) * 100;

        if($result) {
            $result->call_offered = intval($result->acd_call_offer);
            $result->call_abandoned_long = intval($result->call_abandon_long);
            $result->call_handled = intval($result->acd_call_handle);
            $result->abandon_perc = round($result->abandon_percentage, 1);
            $result->answer_perc = round($result->answer_percentage, 1);
            $result->service_level = round($result->service_level, 1);
        }

        $resultData = [
            'data' => $result
        ];

        // Store the data in the cache
        Cache::put($cacheKey, $resultData, $cacheDuration);

        return response()->json($resultData);
    }
}
