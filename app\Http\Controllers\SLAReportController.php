<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use DB;
use PhpPars<PERSON>\Node\Stmt\Else_;

class SLAReportController extends Controller
{
    //public function store(Request $request)

    public function getSLAReportData($p_year, $p_month) {
    //public function getSLAReportData(Request $request) { 
 
        $currentDate = Carbon::now()->format('d/m/Y');
        //$p_year = 2019;
        //$p_month = 9;
        //dd($p_year);
        //dd($p_month);
        $it_ack_duration=900;
        $cs_ack_duration=900;
        $itspec_rit_duration=14400;
        $performance_proxies_limit=3000;
        $cs_call_in_duration=900;
        $cs_letter_duration=172800;
        $cs_online_duration=900;
        $cs_email_duration=900;
        $treshold_sla_proxies=95;
        $treshold_sla_call_answer_rate=80;
        $treshold_sla_call_abandoned_rate=10;
        $r_system_performance_proxies_penality=1000;
        $r_itcoord_penality=10.00;
        $r_itspec_penality=10.00;
        $r_csm_penality=100;
        $r_sec_dataintg_penality=10000;
        $r_penality_10=10.00;
        // service availibility
        $s_portal = 12;
        $s_web = 7;
        $s_database=12;
        $s_network =10;
        $s_sso=4;
        $s_solr=2;
        $s_bpm=9;

        $nagios_interval=10;


 
        
        
        $monthName = date('F', mktime(0, 0, 0, $p_month, 10)); // March

       //------- Total It_Coord 
        $queryITCoordWithinSLA = "SELECT COUNT(1) as count_itcoord_within_sla FROM sla_itcoord WHERE MONTH(itcoord_case_created)=$p_month 
        AND YEAR(itcoord_case_created)=$p_year AND itcoord_actual_duration <= $it_ack_duration;";

        $queryITCoordExceedSLA = "SELECT COUNT(1) as count_itcoord_exceed_sla FROM sla_itcoord WHERE MONTH(itcoord_case_created)=$p_month 
        AND YEAR(itcoord_case_created)=$p_year AND itcoord_actual_duration > $it_ack_duration;";
        //details itcoord
        $queryITCoordExceedDetails = "SELECT CONCAT(case_number, ' - ' , itcoord_name) AS itcoord_name,  itcoord_available_duration, itcoord_actual_duration, 
        (SELECT  itcoord_available_duration - itcoord_actual_duration) AS itcoord_diff,  (SELECT '10.00') AS pen_rate, (SELECT itcoord_diff * $r_itcoord_penality) AS pen_amt
        FROM sla_itcoord WHERE MONTH(itcoord_case_created)=$p_month  AND YEAR(itcoord_case_created)=$p_year 
        AND itcoord_actual_duration >$it_ack_duration;";

        //--------Total It_Service
        $queryITServExceedSLA = "SELECT
        (SELECT COUNT(*) FROM sla_itserv WHERE MONTH(itserv_insert_data_datetime) = $p_month AND YEAR(itserv_insert_data_datetime)=2019 AND itserv_actual_duration > itserv_available_duration)
        AS ITServExceedSLA;";
        //-------details IT Service
        $queryITServExceedDetails = "SELECT CONCAT(case_number, ' - ' , itserv_name) AS itserv_name, itserv_available_duration, itserv_actual_duration,
        (SELECT  itserv_actual_duration - itserv_available_duration) AS itserv_diff, (SELECT '10.00') AS PEN_RATE, (SELECT itserv_diff * 10) AS pen_amt
         FROM sla_itserv WHERE (SELECT  itserv_available_duration - itserv_actual_duration) < 0 AND MONTH(itserv_insert_data_datetime) = $p_month AND YEAR(itserv_insert_data_datetime)=$p_year;";

        //details security_integrity
        $querySecurityIntegrityDetails = "SELECT CONCAT(case_number, ' - ' , SUBJECT) AS itcoord_name, 
        (SELECT '0') AS SLA, (SELECT '0') AS SCORE, (SELECT '0') AS DIFF, (SELECT '1') AS SLA, (SELECT $r_system_performance_proxies_penality) AS PEN_AMT
        FROM sla_internal_fac
        WHERE MONTH(action_date_by_top) = $p_month  AND YEAR(action_date_by_top) = $p_year;";
        
        // summary S1 
        
        $queryITSpecS1SLA ="SELECT 
        (SELECT COUNT(*) FROM sla_itseverity WHERE MONTH(itseverity_completed_datetime) = $p_month  AND YEAR(itseverity_completed_datetime) = $p_year  
        AND itseverity_sla_flag = 's1' AND itseverity_available_duration  > itseverity_actual_duration ) AS c_s1_within_sla,
        (SELECT COUNT(*) FROM sla_itseverity WHERE MONTH(itseverity_completed_datetime) = $p_month AND YEAR(itseverity_completed_datetime) = $p_year     
        AND itseverity_sla_flag = 's1' AND itseverity_available_duration  < itseverity_actual_duration ) AS c_s1_exceed_sla,
        (SELECT (c_s1_within_sla) / (c_s1_within_sla + c_s1_exceed_sla) * 100) AS p_s1_within_sla,
        (SELECT (100 - p_s1_within_sla)) AS p_s1_exceed_sla;";

        // summary S2

        $queryITSpecS2SLA ="SELECT 
        (SELECT COUNT(*) FROM sla_itseverity WHERE MONTH(itseverity_completed_datetime) = $p_month  AND YEAR(itseverity_completed_datetime) = $p_year   
        AND itseverity_sla_flag = 's2' AND itseverity_available_duration  > itseverity_actual_duration ) AS c_s2_within_sla,
        (SELECT COUNT(*) FROM sla_itseverity WHERE MONTH(itseverity_completed_datetime) = $p_month AND YEAR(itseverity_completed_datetime) = $p_year    
        AND itseverity_sla_flag = 's2' AND itseverity_available_duration  < itseverity_actual_duration ) AS c_s2_exceed_sla,
        (SELECT (c_s2_within_sla) / (c_s2_within_sla + c_s2_exceed_sla) * 100) AS p_s2_within_sla,
        (SELECT (100 - p_s2_within_sla)) AS p_s2_exceed_sla;";

        // summary S3

        $queryITSpecS3SLA ="SELECT 
        (SELECT COUNT(*) FROM sla_itseverity WHERE MONTH(itseverity_completed_datetime) = $p_month  AND YEAR(itseverity_completed_datetime) = $p_year   
        AND itseverity_sla_flag = 's3' AND itseverity_available_duration  > itseverity_actual_duration ) AS c_s3_within_sla,
        (SELECT COUNT(*) FROM sla_itseverity WHERE MONTH(itseverity_completed_datetime) = $p_month AND YEAR(itseverity_completed_datetime) = $p_year   
        AND itseverity_sla_flag = 's3' AND itseverity_available_duration  < itseverity_actual_duration ) AS c_s3_exceed_sla,
        (SELECT (c_s3_within_sla) / (c_s3_within_sla + c_s3_exceed_sla) * 100) AS p_s3_within_sla,
        (SELECT (100 - p_s3_within_sla)) AS p_s3_exceed_sla;";



        //------- Total It_Specialist
        $queryITSpecWithinSLA = "SELECT COUNT(1) as count_itspec_within_sla FROM sla_itspec WHERE MONTH(itspec_case_created)=$p_month 
        AND YEAR(itspec_case_created)=$p_year AND itspec_actual_duration <= $itspec_rit_duration;";
   
        $queryITSpecExceedSLA = "SELECT COUNT(1) as count_itspec_exceed_sla FROM sla_itspec WHERE MONTH(itspec_case_created)=$p_month 
        AND YEAR(itspec_case_created)=$p_year AND itspec_actual_duration > $itspec_rit_duration;";
        //details
        $queryITSpecExceedDetails = "SELECT CONCAT(case_number, ' - ' , itspec_name) AS itspec_name,  itspec_available_duration, itspec_actual_duration, 
        (SELECT '10.00') AS pen_rate, (SELECT  itspec_available_duration - itspec_actual_duration) AS itspec_diff,  (SELECT itspec_diff * $r_itspec_penality) AS pen_amt
        FROM sla_itspec WHERE itspec_actual_duration >$itspec_rit_duration  AND MONTH(itspec_case_created) = $p_month AND YEAR(itspec_case_created) = $p_year;";

        //S4 IT Spec details
        $queryByApproverSLA ="SELECT CONCAT(case_number, ' - ' , itapprover_name) AS itapprover_name, 
        itapprover_available_duration, itapprover_actual_duration, (SELECT '10.00') AS pen_rate,
        (SELECT  itapprover_available_duration - itapprover_actual_duration) AS itapprover_diff,
        (SELECT itapprover_diff * 10) AS pen_amt
        FROM sla_byapprover WHERE (SELECT  itapprover_actual_duration - itapprover_available_duration) < 0
        AND MONTH(itapprover_insert_data_datetime) = $p_month AND YEAR(itapprover_insert_data_datetime) = $p_year;"; 
        
     //------- Total CS 

        // Letter Correspondence        
        $queryCSLetterSLA ="SELECT (SELECT COUNT(*) FROM sla_cs WHERE  MONTH(created_date) = $p_month AND YEAR(created_date) = $p_year 
        AND contact_mode = 'Letter Correspondence' AND cs_actual_duration <  $cs_letter_duration) AS c_cs_w_letter_sla,
        (SELECT COUNT(*) FROM sla_cs WHERE  MONTH(created_date) = $p_month AND YEAR(created_date) = $p_year AND contact_mode = 'Letter Correspondence'
        AND cs_actual_duration >  $cs_letter_duration) AS c_cs_x_letter_sla,
        (SELECT (c_cs_w_letter_sla) / (c_cs_w_letter_sla + c_cs_x_letter_sla) * 100) AS p_cs_w_letter_sla,   
        (SELECT (100 - p_cs_w_letter_sla)) AS p_cs_x_letter_sla";


        // Call-In (Telephone)
        $queryCSCallInSLA ="SELECT (SELECT COUNT(*) FROM sla_cs WHERE  MONTH(created_date) = $p_month AND YEAR(created_date) = $p_year 
        AND contact_mode = 'Call-in' AND cs_actual_duration <  $cs_call_in_duration) AS c_cs_w_call_in_sla,
        (SELECT COUNT(*) FROM sla_cs WHERE  MONTH(created_date) = $p_month AND YEAR(created_date) = $p_year AND contact_mode = 'Call-in'
        AND cs_actual_duration >  $cs_call_in_duration) AS c_cs_x_call_in_sla,
        (SELECT (c_cs_w_call_in_sla) / (c_cs_w_call_in_sla + c_cs_x_call_in_sla) * 100) AS p_cs_w_call_in_sla,   
        (SELECT (100 - p_cs_w_call_in_sla)) AS p_cs_x_call_in_sla";

        // 'Open Portal'
        $queryCSOnLineSLA ="SELECT (SELECT COUNT(*) FROM sla_cs WHERE  MONTH(created_date) = $p_month AND YEAR(created_date) = $p_year 
        AND contact_mode = 'Open Portal' AND cs_actual_duration <  $cs_online_duration) AS c_cs_w_online_sla,
        (SELECT COUNT(*) FROM sla_cs WHERE  MONTH(created_date) = $p_month AND YEAR(created_date) = $p_year AND contact_mode = 'Open Portal'
        AND cs_actual_duration >  $cs_online_duration) AS c_cs_x_online_sla,
        (SELECT (c_cs_w_online_sla) / (c_cs_w_online_sla + c_cs_x_online_sla) * 100) AS p_cs_w_online_sla,   
        (SELECT (100 - p_cs_w_online_sla)) AS p_cs_x_online_sla";

        // 'Email'
        $queryCSEmailSLA ="SELECT (SELECT COUNT(*) FROM sla_cs WHERE  MONTH(created_date) = $p_month AND YEAR(created_date) = $p_year 
        AND contact_mode = 'Email' AND cs_actual_duration <  $cs_email_duration) AS c_cs_w_email_sla,
        (SELECT COUNT(*) FROM sla_cs WHERE  MONTH(created_date) = $p_month AND YEAR(created_date) = $p_year AND contact_mode = 'Email'
        AND cs_actual_duration >  $cs_email_duration) AS c_cs_x_email_sla,
        (SELECT (c_cs_w_email_sla) / (c_cs_w_email_sla + c_cs_x_email_sla) * 100) AS p_cs_w_email_sla,   
        (SELECT (100 - p_cs_w_email_sla)) AS p_cs_x_email_sla";

        // System Security Data Integrity (SLA Internal Factor)

        $queryInternalFacSLA ="SELECT 
        (SELECT COUNT(*) FROM sla_internal_fac WHERE MONTH(action_date_by_top) = $p_month AND YEAR(action_date_by_top) = $p_year) AS total_sla_internal_fac,
        (SELECT 0 - total_sla_internal_fac) AS diff_sla_internal_fac,
        (SELECT (total_sla_internal_fac * $r_sec_dataintg_penality)) AS pen_amt_sla_internal_fac";

        //------- Total Mitel 
    
        $queryMitelwithinSLA ="SELECT  CAST(SUM(acd_call_handle) AS FLOAT) / (CAST(SUM(acd_call_offer) AS FLOAT) - CAST(SUM(call_abandon_short) AS FLOAT)) * 100 AS service_level
        FROM sla_mitel
        WHERE MONTH(date_call)=$p_month AND YEAR(date_call)=$p_year";

        $queryMitelAbandonCall ="SELECT  CAST(SUM(call_abandon_long) AS FLOAT) / (CAST(SUM(acd_call_offer) AS FLOAT) - CAST(SUM(call_abandon_short) AS FLOAT)) * 100 AS service_level
        FROM sla_mitel
        WHERE MONTH(date_call)=$p_month AND YEAR(date_call)=$p_year";

        // Service Availibility
        $min_in_month= 60 * 24 * (cal_days_in_month(CAL_GREGORIAN,$p_month,$p_year)); // calculate minutes in month for sla calculation
        $querySADown = "SELECT
        count(b.counter) AS num_of_sa_down
    FROM
        (
            SELECT
                a.*,
                (
                    CASE
                        WHEN a.host_group  = 'portal'
                            AND a.counter >= $s_portal
                            THEN 'YES'
                        WHEN a.host_group  = 'bpm'
                            AND a.counter >= $s_bpm
                            THEN 'YES'
                        WHEN a.host_group  = 'database'
                            AND a.counter >= $s_database
                            THEN 'YES'
                        WHEN a.host_group  = 'web'
                            AND a.counter >= $s_web
                            THEN 'YES'
                        WHEN a.host_group  = 'network'
                            AND a.counter >= $s_network
                            THEN 'YES'
                        WHEN a.host_group  = 'sso'
                            AND a.counter >= $s_sso
                            THEN 'YES'
                        WHEN a.host_group  = 'solr'
                            AND a.counter >= $s_solr
                            THEN 'YES'
                            ELSE 'NO'
                    END
                )
                AS all_down_flag
            FROM
                (
                    SELECT
                        host_group, DATE_FORMAT(updated_at, '%Y-%m-%d %H-%i-%s') AS downtime, COUNT(1) AS counter
                    FROM
                        cdc_poms.sla_nagios
                    WHERE
                        host_group IN ('sso', 'portal', 'database', 'bpm', 'solr', 'web', 'network')
                        AND service_status   <> '2'
                        AND MONTH(updated_at) = $p_month
                        AND YEAR (updated_at) = $p_year
                        AND
                        (
                            DAYOFWEEK(updated_at) NOT IN (6, 7)
                            OR
                            (
                                DAYOFWEEK(updated_at) IN (6)
                                AND TIME(updated_at) NOT BETWEEN TIME('22:00:00') AND TIME('23:59:00')
                            )
                            OR
                            (
                                DAYOFWEEK(updated_at) IN (7)
                                AND TIME(updated_at) NOT BETWEEN TIME('00:00:00') AND TIME('06:00:00')
                            )
                        )
                    GROUP BY
                        host_group, DATE_FORMAT(updated_at, '%Y-%m-%d %H-%i-%s')
                )
                a
            ORDER BY
                a.host_group asc, a.downtime DESC
        )
        b
    WHERE
        b.all_down_flag = 'YES'";
       
    //-------- Performance Proxies

    // 'RequestNoteSO.submitRNForApproval.executedTime'

    $queryProxies_RequestNoteSO_submitRNForApproval = "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'RequestNoteSO.submitRNForApproval.executedTime'
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'RequestNoteSO.submitRNForApproval.executedTime'
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";

    // 'ContractSO.saveContractVer.executedTime'

    $queryProxies_ContractSO_saveContractVer = "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'ContractSO.saveContractVer.executedTime'
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'ContractSO.saveContractVer.executedTime'
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";


    // 'ReceivedNoteSO.saveReceivedNote.executedTime'

    $queryProxies_ReceivedNoteSO_saveReceivedNote = "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'ReceivedNoteSO.saveReceivedNote.executedTime'
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'ReceivedNoteSO.saveReceivedNote.executedTime'
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";

    // 'FulfilmentRequestSO.saveFulfillmentRequest.executedTime'

    $queryProxies_FulfilmentRequestSO_saveFulfillmentRequest = "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'FulfilmentRequestSO.saveFulfillmentRequest.executedTime'
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'FulfilmentRequestSO.saveFulfillmentRequest.executedTime'
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";


     // 'InvoiceSO.saveInvoice.executedTime..'

     $queryProxies_InvoiceSO_saveInvoice = "SELECT 
     (SELECT COUNT(*) FROM performance_proxies
     WHERE MONTH(date_time) = $p_month 
     AND YEAR(date_time) = $p_year    
     AND TRANSACTION = 'InvoiceSO.saveInvoice.executedTime'
     AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
     (SELECT COUNT(*) FROM performance_proxies
     WHERE MONTH(date_time) = $p_month 
     AND YEAR(date_time) = $p_year     
     AND TRANSACTION = 'InvoiceSO.saveInvoice.executedTime'
     AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
     (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
     AS percentage_proxies_within_sla,
     (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
     AS percentage_proxies_exceed_sla";


     // 'MofRegistrationSO.initiateSmApplicationTask.executedTime'

     $queryProxies_MofRegistrationSO_initiateSmApplicationTask =  "SELECT 
     (SELECT COUNT(*) FROM performance_proxies
     WHERE MONTH(date_time) = $p_month 
     AND YEAR(date_time) = $p_year    
     AND TRANSACTION = 'MofRegistrationSO.initiateSmApplicationTask.executedTime'
     AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
     (SELECT COUNT(*) FROM performance_proxies
     WHERE MONTH(date_time) = $p_month 
     AND YEAR(date_time) = $p_year     
     AND TRANSACTION = 'MofRegistrationSO.initiateSmApplicationTask.executedTime'
     AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
     (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
     AS percentage_proxies_within_sla,
     (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
     AS percentage_proxies_exceed_sla";


    // 'VirtualCertBackingBean.viewVirtualCert.executedTime'

    $queryProxies_VirtualCertBackingBean_viewVirtualCert =  "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'VirtualCertBackingBean.viewVirtualCert.executedTime'
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'VirtualCertBackingBean.viewVirtualCert.executedTime'
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";


// ----------------------------------------------------------------------------------

    // 'SLA-SM-SR	ViewSuppProfileBackingBean.preRenderViewSupplierProfile.executedTime
    
    $queryProxies_SLA_SM_SR_ViewSuppProfileBackingBean_preRenderViewSupplierProfile =  "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'ViewSuppProfileBackingBean.preRenderViewSupplierProfile.executedTime'
    AND MODULE = 'SLA-SM-SR'
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'ViewSuppProfileBackingBean.preRenderViewSupplierProfile.executedTime'
    AND MODULE in ('SLA-SM-SR','SLA-SM-','SLA-SM-VA')
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";


    // 'SLA-SM-	ViewSuppProfileBackingBean.preRenderViewSupplierProfile.executedTime

    $queryProxies_SLA_CM_TrackingDiaryBackingBean =  "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'CmTrackingDiaryBackingBean.search.executedTime'
    AND MODULE = 'SLA-CM-SC'
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'CmTrackingDiaryBackingBean.search.executedTime'
    AND MODULE = 'SLA-CM-SC'
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";

    // SLA-PORTAL-LOGIN     my.ep.web.portal.login.LoginPortlet.executedTime

    $queryProxies_SLA_PORTAL_LOGIN =  "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'my.ep.web.portal.login.LoginPortlet.executedTime'
    AND MODULE = 'SLA-PORTAL-LOGIN'
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'my.ep.web.portal.login.LoginPortlet.executedTime'
    AND MODULE = 'SLA-PORTAL-LOGIN'
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";




    // 'SLA-SC-PN	SPDetailBackingBean.submitSupplierProposal.executedTime

    $queryProxies_SLA_SC_PN_SPDetailBackingBean_submitSupplierProposal =  "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'SPDetailBackingBean.submitSupplierProposal.executedTime'
    AND MODULE = 'SLA-SC-PN'
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'SPDetailBackingBean.submitSupplierProposal.executedTime'
    AND MODULE = 'SLA-SC-PN'
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";

    // 'SLA-SC-SE	SpecificationBackingBean.pageChangeListener.executedTime
    
    $queryProxies_SLA_SC_SE_SpecificationBackingBean_pageChangeListener =  "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'SpecificationBackingBean.pageChangeListener.executedTime'
    AND MODULE = 'SLA-SC-SE'
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'SpecificationBackingBean.pageChangeListener.executedTime'
    AND MODULE = 'SLA-SC-SE'
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";

    // 'SLA-CT-AC	AgreementSO.saveAgreement.executedTime
    
    $queryProxies_SLA_CT_AC_AgreementSO_saveAgreement =  "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'AgreementSO.saveAgreement.executedTime'
    AND MODULE IN ('SLA-CT-AC','SLA-CT-SA', 'SLA-CT-FD')
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'AgreementSO.saveAgreement.executedTime'
    AND MODULE IN ('SLA-CT-AC','SLA-CT-SA', 'SLA-CT-FD')
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";
 
    // 'SLA-SM-VA	CommonApplBackingBean.preRenderView
    ///queryProxies_SLA_CT_SA_AgreementSO_saveAgreement

    $queryProxies_SLA_SM_VA_CommonApplBackingBean_PreRenderView =  "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'CommonApplBackingBean.preRenderView.executedTime'
    AND MODULE = 'SLA-SM-VA'
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'CommonApplBackingBean.preRenderView.executedTime'
    AND MODULE = 'SLA-SM-VA'
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";
    
    // 'SLA-PM-PS	ViewSuppProfileBackingBean.preRenderViewSupplierProfile.executedTime

    $queryProxies_SLA_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile =  "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'ViewSuppProfileBackingBean.preRenderViewSupplierProfile.executedTime'
    AND MODULE = 'SLA-PM-PS'
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'ViewSuppProfileBackingBean.preRenderViewSupplierProfile.executedTime'
    AND MODULE = 'SLA-PM-PS'
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";

    //'SLA-SC-BD  OnlineBiddingBackingBean.updateBidPriceAndCalculateRank.executedTime 
    
    $queryProxies_SLA_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank =  "SELECT 
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year    
    AND TRANSACTION = 'OnlineBiddingBackingBean.updateBidPriceAndCalculateRank.executedTime'
    AND MODULE = 'SLA-SC-BD'
    AND duration_result < $performance_proxies_limit) AS performance_proxies_within_sla,
    (SELECT COUNT(*) FROM performance_proxies
    WHERE MONTH(date_time) = $p_month 
    AND YEAR(date_time) = $p_year     
    AND TRANSACTION = 'OnlineBiddingBackingBean.updateBidPriceAndCalculateRank.executedTime'
    AND MODULE = 'SLA-SC-BD'
    AND duration_result > $performance_proxies_limit) AS performance_proxies_exceed_sla,
    (SELECT (performance_proxies_within_sla) / (performance_proxies_within_sla + performance_proxies_exceed_sla) * 100)
    AS percentage_proxies_within_sla,
    (SELECT ($treshold_sla_proxies - percentage_proxies_within_sla))
    AS percentage_proxies_exceed_sla";



     //-------Start DB Connection ----------------
        $resSLAReportData = (object)array();
        $resITCoordWithinData = DB::connection('mysql')->select($queryITCoordWithinSLA)[0];
        //details
        $resITCoordDetails = DB::connection('mysql')->select($queryITCoordExceedDetails);
        $resITSpecDetails = DB::connection('mysql')->select($queryITSpecExceedDetails);
        $resSecurityIntegrityDetails=DB::connection('mysql')->select($querySecurityIntegrityDetails);
        //dd($resITCoordWithinData);

        $resByApproverSLA= DB::connection('mysql')->select($queryByApproverSLA);
        //dd($resByApproverSLA);
        //security data integrity internal fac
        $resInternalFacSLA= DB::connection('mysql')->select($queryInternalFacSLA)[0];
        //dd($resInternalFacSLA);
        $resITCoordExceedData = DB::connection('mysql')->select($queryITCoordExceedSLA)[0];
        //itserv
        $resITServExceedSLA = DB::connection('mysql')->select($queryITServExceedSLA)[0];
        $resITServExceedDetails=DB::connection('mysql')->select($queryITServExceedDetails);
        //S1 
        $resITSpecS1SLA = DB::connection('mysql')->select($queryITSpecS1SLA)[0];
        //S2 
        $resITSpecS2SLA = DB::connection('mysql')->select($queryITSpecS2SLA)[0];
        //S3 
        $resITSpecS3SLA = DB::connection('mysql')->select($queryITSpecS3SLA)[0];

        $resMitelwithinSLA = DB::connection('mysql')->select($queryMitelwithinSLA)[0];
        $resMitelExceedSLA = DB::connection('mysql')->select($queryMitelAbandonCall)[0]; //need to change to $resMitelAbandonSLA more meaningful
        $resCSCallIn = DB::connection('mysql')->select($queryCSCallInSLA)[0];
        $resCSLetter = DB::connection('mysql')->select($queryCSLetterSLA)[0];
        $resCSOnLineSLA = DB::connection('mysql')->select($queryCSOnLineSLA)[0];
        $resCSEmailSLA = DB::connection('mysql')->select($queryCSEmailSLA)[0];
      
        //Service  Availibility
        $resSADown = DB::connection('mysql')->select($querySADown)[0];
        //dd($resSADown);
        
        //dd($resCSEmailSLA);
        $resITSpecwithinSLA = DB::connection('mysql')->select($queryITSpecWithinSLA)[0];
        $resITSpecExceedSLA = DB::connection('mysql')->select($queryITSpecExceedSLA)[0];
        $resPerfProxiesSLA_RequestNoteSO_submitRNForApproval = DB::connection('mysql')->select($queryProxies_RequestNoteSO_submitRNForApproval)[0];
        $resPerfProxiesSLA_ContractSO_saveContractVer = DB::connection('mysql')->select($queryProxies_ContractSO_saveContractVer)[0];
        $resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote = DB::connection('mysql')->select($queryProxies_ReceivedNoteSO_saveReceivedNote)[0];
        $resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest = DB::connection('mysql')->select($queryProxies_FulfilmentRequestSO_saveFulfillmentRequest)[0];
        //InvoiceSO.saveInvoice
        $resPerfProxiesSLA_InvoiceSO_saveInvoice = DB::connection('mysql')->select($queryProxies_InvoiceSO_saveInvoice)[0];
        $resPerfProxiesSLA_MofRegistrationSO_initiateSmApplicationTask = DB::connection('mysql')->select($queryProxies_MofRegistrationSO_initiateSmApplicationTask)[0];
        $resPerfProxiesSLA_VirtualCertBackingBean_viewVirtualCert = DB::connection('mysql')->select($queryProxies_VirtualCertBackingBean_viewVirtualCert)[0];
        
        //SM 
        $resPerfProxiesSLA_ViewSuppProfileBackingBean_preRenderViewSupplierProfile =DB::connection('mysql')->select($queryProxies_SLA_SM_SR_ViewSuppProfileBackingBean_preRenderViewSupplierProfile)[0];
        $resPerfProxiesSLA_SM_VA_CommonApplBackingBean_PreRenderView=DB::connection('mysql')->select($queryProxies_SLA_SM_VA_CommonApplBackingBean_PreRenderView)[0];
        ///$resPerfProxiesSLA_CT_SA_AgreementSO_saveAgreement
        //QT 
        $resPerfProxiesSLA_SC_PN_SPDetailBackingBean_submitSupplierProposal =DB::connection('mysql')->select($queryProxies_SLA_SC_PN_SPDetailBackingBean_submitSupplierProposal)[0];
        //QT Catalogue Search
        $resPerfProxiesSLA_SC_SE_SpecificationBackingBean_pageChangeListener=DB::connection('mysql')->select($queryProxies_SLA_SC_SE_SpecificationBackingBean_pageChangeListener)[0];
        $resPerfProxiesSLA_SLA_CM_TrackingDiaryBackingBean=DB::connection('mysql')->select($queryProxies_SLA_CM_TrackingDiaryBackingBean)[0];
        //LOGIN
        // queryProxies_SLA_PORTAL_LOGIN
        $resPerfProxiesSLA_PORTAL_LOGIN=DB::connection('mysql')->select($queryProxies_SLA_PORTAL_LOGIN)[0];
              
        //CT
        $resPerfProxiesSLA_CT_AC_AgreementSO_saveAgreement=DB::connection('mysql')->select($queryProxies_SLA_CT_AC_AgreementSO_saveAgreement)[0];
        //queryProxies_SLA_CT_SA_AgreementSO_saveAgreement
        //PP
        $resPerfProxiesSLA_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile =DB::connection('mysql')->select($queryProxies_SLA_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile)[0];
        //Online Bidding
        $resPerfProxiesSLA_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank=DB::connection('mysql')->select($queryProxies_SLA_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank)[0];

    //-------End DB Connection ----------------
    
    //-------------ITCoord percentage 
        $p_ITCoord_SLA=0;
        
        if($resITCoordWithinData && $resITCoordWithinData->count_itcoord_within_sla > 0) {
            $p_ITCoord_SLA=$resITCoordWithinData->count_itcoord_within_sla /
            ($resITCoordWithinData->count_itcoord_within_sla + $resITCoordExceedData->count_itcoord_exceed_sla) * 100;
        }
        //dd($resITCoordWithinData);

    //-------------ITSpec percentage
        $p_ITSpec_SLA=0;
        if($resITSpecwithinSLA  && $resITSpecwithinSLA->count_itspec_within_sla > 0) {
            $p_ITSpec_SLA=$resITSpecwithinSLA->count_itspec_within_sla /
            ($resITSpecwithinSLA->count_itspec_within_sla + $resITSpecExceedSLA->count_itspec_exceed_sla) * 100;
        }
    
            
        
        if(null != $resITCoordWithinData && null != $resITCoordExceedData) {
            $resSLAReportData->count_itcoord_within_sla = $resITCoordWithinData->count_itcoord_within_sla;
            $resSLAReportData->count_itcoord_exceed_sla = $resITCoordExceedData->count_itcoord_exceed_sla;
            $resSLAReportData->p_itcoord_sla = round($p_ITCoord_SLA, 2);
            //mitel answered call
            $resSLAReportData->p_mitel_sla = round($resMitelwithinSLA->service_level,2);
            $resSLAReportData->treshold_sla_mitel=$treshold_sla_call_answer_rate;
            $resSLAReportData->diff_sla_mitel=round(round($resMitelwithinSLA->service_level,2) -$treshold_sla_call_answer_rate,2);
            if( round(round($resMitelwithinSLA->service_level,2) -$treshold_sla_call_answer_rate,2) < 0) {
                $resSLAReportData->pen_amt_sla_mitel = round(round($resMitelwithinSLA->service_level,2) -$treshold_sla_call_answer_rate,2) * $r_csm_penality;
            }
            else {
                $resSLAReportData->pen_amt_sla_mitel = 0;
            }

            $resSLAReportData->reportmonth =$monthName;
            $resSLAReportData->reportyear =$p_year;
            //mitel abandoned call
            $resSLAReportData->p_mitel_abandon_call = round($resMitelExceedSLA->service_level,2);
            $resSLAReportData->treshold_sla_call_abandoned_rate=$treshold_sla_call_abandoned_rate;
            //$resSLAReportData->diff_sla_abandon_mitel=round(round($resMitelwithinSLA->service_level,2) -$treshold_sla_call_answer_rate,2);

            $resSLAReportData->itcoord_details =   $resITCoordDetails;
            $resSLAReportData->itspec_details =   $resITSpecDetails;
            $resSLAReportData->sla_caseapprover_details = $resByApproverSLA;
            //IT Service
            $resSLAReportData->itserv_exceed_sla = $resITServExceedSLA->ITServExceedSLA;
            $resSLAReportData->itserv_exceed_details =$resITServExceedDetails;

            // S1 S2 S3
            $resSLAReportData->p_s1_within_sla = $resITSpecS1SLA->p_s1_within_sla;
            $resSLAReportData->p_s2_within_sla = $resITSpecS2SLA->p_s2_within_sla;
            $resSLAReportData->p_s3_within_sla = $resITSpecS3SLA->p_s3_within_sla;
            $resSLAReportData->r_penality_10 = $r_penality_10;

            //resSecurityIntegrityDetails
            $resSLAReportData->securityintegrity_details =  $resSecurityIntegrityDetails;

            //total_sla_internal_fac,diff_sla_internal_fac, r_sec_dataintg_penality, pen_amt_sla_internal_fac
            $resSLAReportData->total_sla_internal_fac= $resInternalFacSLA->total_sla_internal_fac;
            $resSLAReportData->diff_sla_internal_fac= $resInternalFacSLA->diff_sla_internal_fac;
            $resSLAReportData->r_sec_dataintg_penality=$r_sec_dataintg_penality;
            $resSLAReportData->pen_amt_sla_internal_fac= $resInternalFacSLA->pen_amt_sla_internal_fac;
            
            //$resCSCallIn
            $resSLAReportData->c_cs_w_call_in_sla = $resCSCallIn->c_cs_w_call_in_sla;
            $resSLAReportData->c_cs_x_call_in_sla = $resCSCallIn->c_cs_x_call_in_sla;
            $resSLAReportData->p_cs_w_call_in_sla = round($resCSCallIn->p_cs_w_call_in_sla, 2);
            $resSLAReportData->p_cs_x_call_in_sla = round($resCSCallIn->p_cs_x_call_in_sla, 2);

            //$resCSLetter
            $resSLAReportData->c_cs_w_letter_sla = $resCSLetter->c_cs_w_letter_sla; 
            $resSLAReportData->c_cs_x_letter_sla = $resCSLetter->c_cs_x_letter_sla;
            $resSLAReportData->p_cs_w_letter_sla = round($resCSLetter->p_cs_w_letter_sla, 2);
            $resSLAReportData->p_cs_x_letter_sla = round($resCSLetter->p_cs_x_letter_sla, 2);

             //$resCSOnLineSLA
             $resSLAReportData->c_cs_w_online_sla = $resCSOnLineSLA->c_cs_w_online_sla; 
             $resSLAReportData->c_cs_x_online_sla = $resCSOnLineSLA->c_cs_x_online_sla;
             $resSLAReportData->p_cs_w_online_sla = round($resCSOnLineSLA->p_cs_w_online_sla, 2);
             $resSLAReportData->p_cs_x_online_sla = round($resCSOnLineSLA->p_cs_x_online_sla, 2);

             //$resCSEmailSLA
             $resSLAReportData->c_cs_w_email_sla = $resCSEmailSLA->c_cs_w_email_sla; 
             $resSLAReportData->c_cs_x_email_sla = $resCSEmailSLA->c_cs_x_email_sla;
             $resSLAReportData->p_cs_w_email_sla = round($resCSEmailSLA->p_cs_w_email_sla, 2);
             $resSLAReportData->p_cs_x_email_sla = round($resCSEmailSLA->p_cs_x_email_sla, 2); 

             //Service Availibility
             // $resSLAReportData->c_sa_down =($resSADown->num_of_sa_down) * $nagios_interval;
             $resSLAReportData->c_sa_sla = round((($min_in_month - (($resSADown->num_of_sa_down) * $nagios_interval))/$min_in_month) * 100,2);
             $resSLAReportData->c_sa_sla_diff = round(99.6 -  ($resSLAReportData->c_sa_sla),2);   

            $resSLAReportData->count_itspec_within_sla = $resITSpecwithinSLA->count_itspec_within_sla;
            $resSLAReportData->count_itspec_exceed_sla = $resITSpecExceedSLA->count_itspec_exceed_sla;
            $resSLAReportData->p_ITSpec_SLA = round($p_ITSpec_SLA, 2);

            
            $resSLAReportData->count_within_sla_RequestNoteSO_submitRNForApproval = $resPerfProxiesSLA_RequestNoteSO_submitRNForApproval->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_RequestNoteSO_submitRNForApproval = $resPerfProxiesSLA_RequestNoteSO_submitRNForApproval->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_RequestNoteSO_submitRNForApproval = round($resPerfProxiesSLA_RequestNoteSO_submitRNForApproval->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_RequestNoteSO_submitRNForApproval = round($resPerfProxiesSLA_RequestNoteSO_submitRNForApproval->percentage_proxies_exceed_sla,2);
           

            $resSLAReportData->count_within_sla_resPerfProxiesSLA_ContractSO_saveContractVer = $resPerfProxiesSLA_ContractSO_saveContractVer->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_resPerfProxiesSLA_ContractSO_saveContractVer = $resPerfProxiesSLA_ContractSO_saveContractVer->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_resPerfProxiesSLA_ContractSO_saveContractVer = round($resPerfProxiesSLA_ContractSO_saveContractVer->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_resPerfProxiesSLA_ContractSO_saveContractVer = round($resPerfProxiesSLA_ContractSO_saveContractVer->percentage_proxies_exceed_sla,2);

            //resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote 
            $resSLAReportData->count_within_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote = $resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote = $resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote = round($resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote = round($resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote->percentage_proxies_exceed_sla,2);

            //$resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest
            $resSLAReportData->count_within_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest = $resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest = $resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest = round($resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest = round($resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest->percentage_proxies_exceed_sla,2);

            //$resPerfProxiesSLA_InvoiceSO_saveInvoice
            $resSLAReportData->count_within_sla_InvoiceSO_saveInvoice = $resPerfProxiesSLA_InvoiceSO_saveInvoice->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_InvoiceSO_saveInvoice = $resPerfProxiesSLA_InvoiceSO_saveInvoice->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_InvoiceSO_saveInvoice = round($resPerfProxiesSLA_InvoiceSO_saveInvoice->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_InvoiceSO_saveInvoice = round($resPerfProxiesSLA_InvoiceSO_saveInvoice->percentage_proxies_exceed_sla,2);

            //$resPerfProxiesSLA_MofRegistrationSO_initiateSmApplicationTask
            $resSLAReportData->count_within_sla_MofRegistrationSO_initiateSmApplicationTask = $resPerfProxiesSLA_MofRegistrationSO_initiateSmApplicationTask->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_MofRegistrationSO_initiateSmApplicationTask = $resPerfProxiesSLA_MofRegistrationSO_initiateSmApplicationTask->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_MofRegistrationSO_initiateSmApplicationTask = round($resPerfProxiesSLA_MofRegistrationSO_initiateSmApplicationTask->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_MofRegistrationSO_initiateSmApplicationTask = round($resPerfProxiesSLA_MofRegistrationSO_initiateSmApplicationTask->percentage_proxies_exceed_sla,2);

            //$resPerfProxiesSLA_VirtualCertBackingBean_viewVirtualCert
            $resSLAReportData->count_within_sla_VirtualCertBackingBean_viewVirtualCert = $resPerfProxiesSLA_VirtualCertBackingBean_viewVirtualCert->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_VirtualCertBackingBean_viewVirtualCert = $resPerfProxiesSLA_VirtualCertBackingBean_viewVirtualCert->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_VirtualCertBackingBean_viewVirtualCert = round($resPerfProxiesSLA_VirtualCertBackingBean_viewVirtualCert->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_VirtualCertBackingBean_viewVirtualCert = round($resPerfProxiesSLA_VirtualCertBackingBean_viewVirtualCert->percentage_proxies_exceed_sla,2);

            //(SM) $resPerfProxiesSLA_ViewSuppProfileBackingBean_preRenderViewSupplierProfile
            $resSLAReportData->count_within_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = $resPerfProxiesSLA_ViewSuppProfileBackingBean_preRenderViewSupplierProfile->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = $resPerfProxiesSLA_ViewSuppProfileBackingBean_preRenderViewSupplierProfile->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = round($resPerfProxiesSLA_ViewSuppProfileBackingBean_preRenderViewSupplierProfile->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile = round($resPerfProxiesSLA_ViewSuppProfileBackingBean_preRenderViewSupplierProfile->percentage_proxies_exceed_sla,2);
            
            //(SM) $resPerfProxiesSLA_SM_VA_CommonApplBackingBean_PreRenderView
            $resSLAReportData->count_within_sla_SM_VA_CommonApplBackingBean_PreRenderView= $resPerfProxiesSLA_SM_VA_CommonApplBackingBean_PreRenderView->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_SM_VA_CommonApplBackingBean_PreRenderView= $resPerfProxiesSLA_SM_VA_CommonApplBackingBean_PreRenderView->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_SM_VA_CommonApplBackingBean_PreRenderView= round($resPerfProxiesSLA_SM_VA_CommonApplBackingBean_PreRenderView->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_SM_VA_CommonApplBackingBean_PreRenderView= round($resPerfProxiesSLA_SM_VA_CommonApplBackingBean_PreRenderView->percentage_proxies_exceed_sla,2);


            //(QT) - Proposal Submission $resPerfProxiesSLA_SC_PN_SPDetailBackingBean_submitSupplierProposal
            $resSLAReportData->count_within_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal = $resPerfProxiesSLA_SC_PN_SPDetailBackingBean_submitSupplierProposal->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal = $resPerfProxiesSLA_SC_PN_SPDetailBackingBean_submitSupplierProposal->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal = round($resPerfProxiesSLA_SC_PN_SPDetailBackingBean_submitSupplierProposal->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_SC_PN_SPDetailBackingBean_submitSupplierProposal = round($resPerfProxiesSLA_SC_PN_SPDetailBackingBean_submitSupplierProposal->percentage_proxies_exceed_sla,2);
            //(QT) - Catalogue Search $resPerfProxiesSLA_SC_SE_SpecificationBackingBean_pageChangeListener
            $resSLAReportData->count_within_sla_SC_SE_SpecificationBackingBean_pageChangeListener = $resPerfProxiesSLA_SC_SE_SpecificationBackingBean_pageChangeListener->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_SC_SE_SpecificationBackingBean_pageChangeListener = $resPerfProxiesSLA_SC_SE_SpecificationBackingBean_pageChangeListener->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_SC_SE_SpecificationBackingBean_pageChangeListener = round($resPerfProxiesSLA_SC_SE_SpecificationBackingBean_pageChangeListener->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_SC_SE_SpecificationBackingBean_pageChangeListener = round($resPerfProxiesSLA_SC_SE_SpecificationBackingBean_pageChangeListener->percentage_proxies_exceed_sla,2);
            
            //QT - Catalogue Search $resPerfProxiesSLA_SLA_CM_TrackingDiaryBackingBean
            $resSLAReportData->count_within_sla_CM_TrackingDiaryBackingBean = $resPerfProxiesSLA_SLA_CM_TrackingDiaryBackingBean->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_CM_TrackingDiaryBackingBean = $resPerfProxiesSLA_SLA_CM_TrackingDiaryBackingBean->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_CM_TrackingDiaryBackingBean = round($resPerfProxiesSLA_SLA_CM_TrackingDiaryBackingBean->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_CM_TrackingDiaryBackingBean = round($resPerfProxiesSLA_SLA_CM_TrackingDiaryBackingBean->percentage_proxies_exceed_sla,2);

            //LOGIN SLA-PORTAL-LOGIN  resPerfProxiesSLA_PORTAL_LOGIN 
            $resSLAReportData->count_within_sla_portal_login = $resPerfProxiesSLA_PORTAL_LOGIN->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_portal_login = $resPerfProxiesSLA_PORTAL_LOGIN->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_portal_login = round($resPerfProxiesSLA_PORTAL_LOGIN->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_portal_login = round($resPerfProxiesSLA_PORTAL_LOGIN->percentage_proxies_exceed_sla,2);

            //(CT) - Request Final Agrement $resPerfProxiesSLA_CT_AC_AgreementSO_saveAgreement
            $resSLAReportData->count_within_sla_CT_AC_AgreementSO_saveAgreement = $resPerfProxiesSLA_CT_AC_AgreementSO_saveAgreement->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_CT_AC_AgreementSO_saveAgreement = $resPerfProxiesSLA_CT_AC_AgreementSO_saveAgreement->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_CT_AC_AgreementSO_saveAgreement = round($resPerfProxiesSLA_CT_AC_AgreementSO_saveAgreement->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_CT_AC_AgreementSO_saveAgreement = round($resPerfProxiesSLA_CT_AC_AgreementSO_saveAgreement->percentage_proxies_exceed_sla,2);
            
            //(PP) - Procumant Plan Submission $resPerfProxiesSLA_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile
            $resSLAReportData->count_within_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile=$resPerfProxiesSLA_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile= $resPerfProxiesSLA_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile= round($resPerfProxiesSLA_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_SC_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile=round($resPerfProxiesSLA_PM_PS_ViewSuppProfileBackingBean_preRenderViewSupplierProfile->percentage_proxies_exceed_sla,2);

            //Online Bidding resPerfProxiesSLA_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank
            $resSLAReportData->count_within_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank=$resPerfProxiesSLA_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank->performance_proxies_within_sla;
            $resSLAReportData->count_exceed_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank= $resPerfProxiesSLA_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank->performance_proxies_exceed_sla;
            $resSLAReportData->p_within_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank= round($resPerfProxiesSLA_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank->percentage_proxies_within_sla,2);
            $resSLAReportData->p_exceed_sla_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank=round($resPerfProxiesSLA_SC_BD_OnlineBiddingBackingBean_updateBidPriceAndCalculateRank->percentage_proxies_exceed_sla,2);

            //total percentage performance proxies
            $resSLAReportData->p_total_system_performance_proxies = $resSLAReportData->p_exceed_sla_RequestNoteSO_submitRNForApproval 
                                                                + $resSLAReportData->p_exceed_sla_resPerfProxiesSLA_ContractSO_saveContractVer
                                                                + $resSLAReportData->p_exceed_sla_resPerfProxiesSLA_ReceivedNoteSO_saveReceivedNote
                                                                + $resSLAReportData->p_exceed_sla_resPerfProxiesSLA_FulfilmentRequestSO_saveFulfillmentRequest
                                                                + $resSLAReportData->p_exceed_sla_InvoiceSO_saveInvoice
                                                                + $resSLAReportData->p_exceed_sla_MofRegistrationSO_initiateSmApplicationTask
                                                                + $resSLAReportData->p_exceed_sla_VirtualCertBackingBean_viewVirtualCert
                                                                + $resSLAReportData->p_exceed_sla_ViewSuppProfileBackingBean_preRenderViewSupplierProfile;

            if($resSLAReportData->p_total_system_performance_proxies > 0) {
                $resSLAReportData->p_sum_system_performance_proxies_penality=
                ($resSLAReportData->p_total_system_performance_proxies * $r_system_performance_proxies_penality) ;
            }
            else {
                $resSLAReportData->p_sum_system_performance_proxies_penality=0;

            } 
                   


        }

        return response()->json([
            'sla_report_data' => $resSLAReportData
        ]);
    }
}
