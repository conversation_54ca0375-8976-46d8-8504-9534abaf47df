import type { VPickerSlots } from "../../labs/VPicker/VPicker.js";
import type { GenericProps } from "../../util/index.js";
export type VDatePickerSlots = Omit<VPickerSlots, 'header'> & {
    header: {
        header: string;
        transition: string;
    };
};
export declare const makeVDatePickerProps: <Defaults extends {
    modelValue?: unknown;
    theme?: unknown;
    tag?: unknown;
    rounded?: unknown;
    tile?: unknown;
    position?: unknown;
    location?: unknown;
    elevation?: unknown;
    height?: unknown;
    maxHeight?: unknown;
    maxWidth?: unknown;
    minHeight?: unknown;
    minWidth?: unknown;
    width?: unknown;
    class?: unknown;
    style?: unknown;
    border?: unknown;
    color?: unknown;
    bgColor?: unknown;
    divided?: unknown;
    landscape?: unknown;
    title?: unknown;
    hideHeader?: unknown;
    max?: unknown;
    min?: unknown;
    year?: unknown;
    disabled?: unknown;
    month?: unknown;
    showAdjacentMonths?: unknown;
    weekdays?: unknown;
    weeksInMonth?: unknown;
    firstDayOfWeek?: unknown;
    allowedDates?: unknown;
    hideWeekdays?: unknown;
    multiple?: unknown;
    showWeek?: unknown;
    transition?: unknown;
    reverseTransition?: unknown;
    active?: unknown;
    controlHeight?: unknown;
    nextIcon?: unknown;
    prevIcon?: unknown;
    modeIcon?: unknown;
    text?: unknown;
    viewMode?: unknown;
    header?: unknown;
    headerColor?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    modelValue: unknown extends Defaults["modelValue"] ? null : {
        type: import("vue").PropType<unknown extends Defaults["modelValue"] ? any : any>;
        default: unknown extends Defaults["modelValue"] ? any : any;
    };
    theme: unknown extends Defaults["theme"] ? StringConstructor : {
        type: import("vue").PropType<unknown extends Defaults["theme"] ? string : string | Defaults["theme"]>;
        default: unknown extends Defaults["theme"] ? string : string | Defaults["theme"];
    };
    tag: unknown extends Defaults["tag"] ? {
        type: import("vue").PropType<string | import("../../util/index.js").JSXComponent>;
        default: string;
    } : Omit<{
        type: import("vue").PropType<string | import("../../util/index.js").JSXComponent>;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["tag"] ? string | import("../../util/index.js").JSXComponent : string | import("../../util/index.js").JSXComponent | Defaults["tag"]>;
        default: unknown extends Defaults["tag"] ? string | import("../../util/index.js").JSXComponent : NonNullable<string | import("../../util/index.js").JSXComponent> | Defaults["tag"];
    };
    rounded: unknown extends Defaults["rounded"] ? {
        type: (StringConstructor | BooleanConstructor | NumberConstructor)[];
        default: undefined;
    } : Omit<{
        type: (StringConstructor | BooleanConstructor | NumberConstructor)[];
        default: undefined;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["rounded"] ? string | number | boolean : string | number | boolean | Defaults["rounded"]>;
        default: unknown extends Defaults["rounded"] ? string | number | boolean : NonNullable<string | number | boolean> | Defaults["rounded"];
    };
    tile: unknown extends Defaults["tile"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["tile"] ? boolean : boolean | Defaults["tile"]>;
        default: unknown extends Defaults["tile"] ? boolean : boolean | Defaults["tile"];
    };
    position: unknown extends Defaults["position"] ? {
        type: import("vue").PropType<"fixed" | "absolute" | "relative" | "static" | "sticky">;
        validator: (v: any) => boolean;
    } : Omit<{
        type: import("vue").PropType<"fixed" | "absolute" | "relative" | "static" | "sticky">;
        validator: (v: any) => boolean;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["position"] ? "fixed" | "absolute" | "relative" | "static" | "sticky" : "fixed" | "absolute" | "relative" | "static" | "sticky" | Defaults["position"]>;
        default: unknown extends Defaults["position"] ? "fixed" | "absolute" | "relative" | "static" | "sticky" : NonNullable<"fixed" | "absolute" | "relative" | "static" | "sticky"> | Defaults["position"];
    };
    location: unknown extends Defaults["location"] ? import("vue").PropType<import("../../util/index.js").Anchor | null> : {
        type: import("vue").PropType<unknown extends Defaults["location"] ? import("../../util/index.js").Anchor | null : import("../../util/index.js").Anchor | Defaults["location"] | null>;
        default: unknown extends Defaults["location"] ? import("../../util/index.js").Anchor | null : NonNullable<import("../../util/index.js").Anchor | null> | Defaults["location"];
    };
    elevation: unknown extends Defaults["elevation"] ? {
        type: (StringConstructor | NumberConstructor)[];
        validator(v: any): boolean;
    } : Omit<{
        type: (StringConstructor | NumberConstructor)[];
        validator(v: any): boolean;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["elevation"] ? string | number : string | number | Defaults["elevation"]>;
        default: unknown extends Defaults["elevation"] ? string | number : NonNullable<string | number> | Defaults["elevation"];
    };
    height: unknown extends Defaults["height"] ? (StringConstructor | NumberConstructor)[] : {
        type: import("vue").PropType<unknown extends Defaults["height"] ? string | number : string | number | Defaults["height"]>;
        default: unknown extends Defaults["height"] ? string | number : NonNullable<string | number> | Defaults["height"];
    };
    maxHeight: unknown extends Defaults["maxHeight"] ? (StringConstructor | NumberConstructor)[] : {
        type: import("vue").PropType<unknown extends Defaults["maxHeight"] ? string | number : string | number | Defaults["maxHeight"]>;
        default: unknown extends Defaults["maxHeight"] ? string | number : NonNullable<string | number> | Defaults["maxHeight"];
    };
    maxWidth: unknown extends Defaults["maxWidth"] ? (StringConstructor | NumberConstructor)[] : {
        type: import("vue").PropType<unknown extends Defaults["maxWidth"] ? string | number : string | number | Defaults["maxWidth"]>;
        default: unknown extends Defaults["maxWidth"] ? string | number : NonNullable<string | number> | Defaults["maxWidth"];
    };
    minHeight: unknown extends Defaults["minHeight"] ? (StringConstructor | NumberConstructor)[] : {
        type: import("vue").PropType<unknown extends Defaults["minHeight"] ? string | number : string | number | Defaults["minHeight"]>;
        default: unknown extends Defaults["minHeight"] ? string | number : NonNullable<string | number> | Defaults["minHeight"];
    };
    minWidth: unknown extends Defaults["minWidth"] ? (StringConstructor | NumberConstructor)[] : {
        type: import("vue").PropType<unknown extends Defaults["minWidth"] ? string | number : string | number | Defaults["minWidth"]>;
        default: unknown extends Defaults["minWidth"] ? string | number : NonNullable<string | number> | Defaults["minWidth"];
    };
    width: unknown extends Defaults["width"] ? (StringConstructor | NumberConstructor)[] : {
        type: import("vue").PropType<unknown extends Defaults["width"] ? string | number : string | number | Defaults["width"]>;
        default: unknown extends Defaults["width"] ? string | number : NonNullable<string | number> | Defaults["width"];
    };
    class: unknown extends Defaults["class"] ? import("vue").PropType<any> : {
        type: import("vue").PropType<unknown extends Defaults["class"] ? any : any>;
        default: unknown extends Defaults["class"] ? any : any;
    };
    style: unknown extends Defaults["style"] ? {
        type: import("vue").PropType<import("vue").StyleValue>;
        default: null;
    } : Omit<{
        type: import("vue").PropType<import("vue").StyleValue>;
        default: null;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["style"] ? import("vue").StyleValue : import("vue").StyleValue | Defaults["style"]>;
        default: unknown extends Defaults["style"] ? import("vue").StyleValue : NonNullable<import("vue").StyleValue> | Defaults["style"];
    };
    border: unknown extends Defaults["border"] ? (StringConstructor | BooleanConstructor | NumberConstructor)[] : {
        type: import("vue").PropType<unknown extends Defaults["border"] ? string | number | boolean : string | number | boolean | Defaults["border"]>;
        default: unknown extends Defaults["border"] ? string | number | boolean : NonNullable<string | number | boolean> | Defaults["border"];
    };
    color: unknown extends Defaults["color"] ? StringConstructor : {
        type: import("vue").PropType<unknown extends Defaults["color"] ? string : string | Defaults["color"]>;
        default: unknown extends Defaults["color"] ? string : string | Defaults["color"];
    };
    bgColor: unknown extends Defaults["bgColor"] ? StringConstructor : {
        type: import("vue").PropType<unknown extends Defaults["bgColor"] ? string : string | Defaults["bgColor"]>;
        default: unknown extends Defaults["bgColor"] ? string : string | Defaults["bgColor"];
    };
    divided: unknown extends Defaults["divided"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["divided"] ? boolean : boolean | Defaults["divided"]>;
        default: unknown extends Defaults["divided"] ? boolean : boolean | Defaults["divided"];
    };
    landscape: unknown extends Defaults["landscape"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["landscape"] ? boolean : boolean | Defaults["landscape"]>;
        default: unknown extends Defaults["landscape"] ? boolean : boolean | Defaults["landscape"];
    };
    title: unknown extends Defaults["title"] ? {
        type: import("vue").PropType<string>;
        default: string;
    } : Omit<{
        type: import("vue").PropType<string>;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["title"] ? string : string | Defaults["title"]>;
        default: unknown extends Defaults["title"] ? string : string | Defaults["title"];
    };
    hideHeader: unknown extends Defaults["hideHeader"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["hideHeader"] ? boolean : boolean | Defaults["hideHeader"]>;
        default: unknown extends Defaults["hideHeader"] ? boolean : boolean | Defaults["hideHeader"];
    };
    max: unknown extends Defaults["max"] ? import("vue").PropType<unknown> : {
        type: import("vue").PropType<unknown extends Defaults["max"] ? unknown : unknown>;
        default: unknown extends Defaults["max"] ? unknown : {} | Defaults["max"];
    };
    min: unknown extends Defaults["min"] ? import("vue").PropType<unknown> : {
        type: import("vue").PropType<unknown extends Defaults["min"] ? unknown : unknown>;
        default: unknown extends Defaults["min"] ? unknown : {} | Defaults["min"];
    };
    year: unknown extends Defaults["year"] ? NumberConstructor : {
        type: import("vue").PropType<unknown extends Defaults["year"] ? number : number | Defaults["year"]>;
        default: unknown extends Defaults["year"] ? number : number | Defaults["year"];
    };
    disabled: unknown extends Defaults["disabled"] ? {
        type: BooleanConstructor;
        default: null;
    } : Omit<{
        type: BooleanConstructor;
        default: null;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["disabled"] ? boolean : boolean | Defaults["disabled"]>;
        default: unknown extends Defaults["disabled"] ? boolean : boolean | Defaults["disabled"];
    };
    month: unknown extends Defaults["month"] ? (StringConstructor | NumberConstructor)[] : {
        type: import("vue").PropType<unknown extends Defaults["month"] ? string | number : string | number | Defaults["month"]>;
        default: unknown extends Defaults["month"] ? string | number : NonNullable<string | number> | Defaults["month"];
    };
    showAdjacentMonths: unknown extends Defaults["showAdjacentMonths"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["showAdjacentMonths"] ? boolean : boolean | Defaults["showAdjacentMonths"]>;
        default: unknown extends Defaults["showAdjacentMonths"] ? boolean : boolean | Defaults["showAdjacentMonths"];
    };
    weekdays: unknown extends Defaults["weekdays"] ? {
        type: import("vue").PropType<import("../../composables/calendar.js").CalendarWeekdays[]>;
        default: () => number[];
    } : Omit<{
        type: import("vue").PropType<import("../../composables/calendar.js").CalendarWeekdays[]>;
        default: () => number[];
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["weekdays"] ? import("../../composables/calendar.js").CalendarWeekdays[] : import("../../composables/calendar.js").CalendarWeekdays[] | Defaults["weekdays"]>;
        default: unknown extends Defaults["weekdays"] ? import("../../composables/calendar.js").CalendarWeekdays[] : import("../../composables/calendar.js").CalendarWeekdays[] | Defaults["weekdays"];
    };
    weeksInMonth: unknown extends Defaults["weeksInMonth"] ? Omit<{
        type: import("vue").PropType<"dynamic" | "static">;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<"static" | "dynamic">;
        default: NonNullable<"static" | "dynamic">;
    } : Omit<Omit<{
        type: import("vue").PropType<"dynamic" | "static">;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<"static" | "dynamic">;
        default: NonNullable<"static" | "dynamic">;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["weeksInMonth"] ? "static" | "dynamic" : "static" | "dynamic" | Defaults["weeksInMonth"]>;
        default: unknown extends Defaults["weeksInMonth"] ? "static" | "dynamic" : NonNullable<"static" | "dynamic"> | Defaults["weeksInMonth"];
    };
    firstDayOfWeek: unknown extends Defaults["firstDayOfWeek"] ? {
        type: (StringConstructor | NumberConstructor)[];
        default: undefined;
    } : Omit<{
        type: (StringConstructor | NumberConstructor)[];
        default: undefined;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["firstDayOfWeek"] ? string | number : string | number | Defaults["firstDayOfWeek"]>;
        default: unknown extends Defaults["firstDayOfWeek"] ? string | number : NonNullable<string | number> | Defaults["firstDayOfWeek"];
    };
    allowedDates: unknown extends Defaults["allowedDates"] ? import("vue").PropType<unknown[] | ((date: unknown) => boolean)> : {
        type: import("vue").PropType<unknown extends Defaults["allowedDates"] ? unknown[] | ((date: unknown) => boolean) : unknown[] | ((date: unknown) => boolean) | Defaults["allowedDates"]>;
        default: unknown extends Defaults["allowedDates"] ? unknown[] | ((date: unknown) => boolean) : NonNullable<unknown[] | ((date: unknown) => boolean)> | Defaults["allowedDates"];
    };
    hideWeekdays: unknown extends Defaults["hideWeekdays"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["hideWeekdays"] ? boolean : boolean | Defaults["hideWeekdays"]>;
        default: unknown extends Defaults["hideWeekdays"] ? boolean : boolean | Defaults["hideWeekdays"];
    };
    multiple: unknown extends Defaults["multiple"] ? import("vue").PropType<number | boolean | (string & {}) | "range"> : {
        type: import("vue").PropType<unknown extends Defaults["multiple"] ? number | boolean | (string & {}) | "range" : number | boolean | (string & {}) | "range" | Defaults["multiple"]>;
        default: unknown extends Defaults["multiple"] ? number | boolean | (string & {}) | "range" : NonNullable<number | boolean | (string & {}) | "range"> | Defaults["multiple"];
    };
    showWeek: unknown extends Defaults["showWeek"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["showWeek"] ? boolean : boolean | Defaults["showWeek"]>;
        default: unknown extends Defaults["showWeek"] ? boolean : boolean | Defaults["showWeek"];
    };
    transition: unknown extends Defaults["transition"] ? {
        type: StringConstructor;
        default: string;
    } : Omit<{
        type: StringConstructor;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["transition"] ? string : string | Defaults["transition"]>;
        default: unknown extends Defaults["transition"] ? string : string | Defaults["transition"];
    };
    reverseTransition: unknown extends Defaults["reverseTransition"] ? {
        type: StringConstructor;
        default: string;
    } : Omit<{
        type: StringConstructor;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["reverseTransition"] ? string : string | Defaults["reverseTransition"]>;
        default: unknown extends Defaults["reverseTransition"] ? string : string | Defaults["reverseTransition"];
    };
    active: unknown extends Defaults["active"] ? {
        type: import("vue").PropType<string | string[]>;
        default: undefined;
    } : Omit<{
        type: import("vue").PropType<string | string[]>;
        default: undefined;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["active"] ? string | string[] : string | string[] | Defaults["active"]>;
        default: unknown extends Defaults["active"] ? string | string[] : NonNullable<string | string[]> | Defaults["active"];
    };
    controlHeight: unknown extends Defaults["controlHeight"] ? (StringConstructor | NumberConstructor)[] : {
        type: import("vue").PropType<unknown extends Defaults["controlHeight"] ? string | number : string | number | Defaults["controlHeight"]>;
        default: unknown extends Defaults["controlHeight"] ? string | number : NonNullable<string | number> | Defaults["controlHeight"];
    };
    nextIcon: unknown extends Defaults["nextIcon"] ? {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    } : Omit<{
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["nextIcon"] ? import("../../composables/icons.js").IconValue : import("../../composables/icons.js").IconValue | Defaults["nextIcon"]>;
        default: unknown extends Defaults["nextIcon"] ? import("../../composables/icons.js").IconValue : NonNullable<import("../../composables/icons.js").IconValue> | Defaults["nextIcon"];
    };
    prevIcon: unknown extends Defaults["prevIcon"] ? {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    } : Omit<{
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["prevIcon"] ? import("../../composables/icons.js").IconValue : import("../../composables/icons.js").IconValue | Defaults["prevIcon"]>;
        default: unknown extends Defaults["prevIcon"] ? import("../../composables/icons.js").IconValue : NonNullable<import("../../composables/icons.js").IconValue> | Defaults["prevIcon"];
    };
    modeIcon: unknown extends Defaults["modeIcon"] ? {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    } : Omit<{
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["modeIcon"] ? import("../../composables/icons.js").IconValue : import("../../composables/icons.js").IconValue | Defaults["modeIcon"]>;
        default: unknown extends Defaults["modeIcon"] ? import("../../composables/icons.js").IconValue : NonNullable<import("../../composables/icons.js").IconValue> | Defaults["modeIcon"];
    };
    text: unknown extends Defaults["text"] ? StringConstructor : {
        type: import("vue").PropType<unknown extends Defaults["text"] ? string : string | Defaults["text"]>;
        default: unknown extends Defaults["text"] ? string : string | Defaults["text"];
    };
    viewMode: unknown extends Defaults["viewMode"] ? {
        type: import("vue").PropType<"month" | "months" | "year">;
        default: string;
    } : Omit<{
        type: import("vue").PropType<"month" | "months" | "year">;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["viewMode"] ? "month" | "year" | "months" : "month" | "year" | "months" | Defaults["viewMode"]>;
        default: unknown extends Defaults["viewMode"] ? "month" | "year" | "months" : NonNullable<"month" | "year" | "months"> | Defaults["viewMode"];
    };
    header: unknown extends Defaults["header"] ? {
        type: StringConstructor;
        default: string;
    } : Omit<{
        type: StringConstructor;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["header"] ? string : string | Defaults["header"]>;
        default: unknown extends Defaults["header"] ? string : string | Defaults["header"];
    };
    headerColor: unknown extends Defaults["headerColor"] ? StringConstructor : {
        type: import("vue").PropType<unknown extends Defaults["headerColor"] ? string : string | Defaults["headerColor"]>;
        default: unknown extends Defaults["headerColor"] ? string : string | Defaults["headerColor"];
    };
};
export declare const VDatePicker: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        transition: string;
        header: string;
        style: import("vue").StyleValue;
        title: string;
        disabled: boolean;
        tag: string | import("../../util/index.js").JSXComponent;
        landscape: boolean;
        tile: boolean;
        divided: boolean;
        nextIcon: import("../../composables/icons.js").IconValue;
        prevIcon: import("../../composables/icons.js").IconValue;
        reverseTransition: string;
        hideHeader: boolean;
        modeIcon: import("../../composables/icons.js").IconValue;
        viewMode: "month" | "year" | "months";
        showAdjacentMonths: boolean;
        weekdays: import("../../composables/calendar.js").CalendarWeekdays[];
        weeksInMonth: "static" | "dynamic";
        hideWeekdays: boolean;
        showWeek: boolean;
    } & {
        max?: unknown;
        location?: import("../../util/index.js").Anchor | null | undefined;
        height?: string | number | undefined;
        width?: string | number | undefined;
        active?: string | string[] | undefined;
        min?: unknown;
        border?: string | number | boolean | undefined;
        color?: string | undefined;
        maxHeight?: string | number | undefined;
        maxWidth?: string | number | undefined;
        minHeight?: string | number | undefined;
        minWidth?: string | number | undefined;
        position?: "fixed" | "absolute" | "relative" | "static" | "sticky" | undefined;
        text?: string | undefined;
        month?: string | number | undefined;
        year?: number | undefined;
        class?: any;
        theme?: string | undefined;
        elevation?: string | number | undefined;
        rounded?: string | number | boolean | undefined;
        bgColor?: string | undefined;
        controlHeight?: string | number | undefined;
        headerColor?: string | undefined;
        firstDayOfWeek?: string | number | undefined;
        allowedDates?: unknown[] | ((date: unknown) => boolean) | undefined;
    } & {
        "onUpdate:month"?: ((date: any) => any) | undefined;
        "onUpdate:year"?: ((date: any) => any) | undefined;
        "onUpdate:viewMode"?: ((date: any) => any) | undefined;
    }, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Omit<{
        'update:modelValue': (date: any) => true;
        'update:month': (date: any) => true;
        'update:year': (date: any) => true;
        'update:viewMode': (date: any) => true;
    }, "multiple" | "$children" | "v-slots" | "v-slot:default" | "modelValue" | "update:modelValue" | "v-slot:actions" | "v-slot:title" | "v-slot:header">, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        active: string | string[];
        transition: string;
        header: string;
        style: import("vue").StyleValue;
        title: string;
        disabled: boolean;
        tag: string | import("../../util/index.js").JSXComponent;
        landscape: boolean;
        rounded: string | number | boolean;
        tile: boolean;
        divided: boolean;
        nextIcon: import("../../composables/icons.js").IconValue;
        prevIcon: import("../../composables/icons.js").IconValue;
        reverseTransition: string;
        hideHeader: boolean;
        modeIcon: import("../../composables/icons.js").IconValue;
        viewMode: "month" | "year" | "months";
        showAdjacentMonths: boolean;
        weekdays: import("../../composables/calendar.js").CalendarWeekdays[];
        weeksInMonth: "static" | "dynamic";
        firstDayOfWeek: string | number;
        hideWeekdays: boolean;
        showWeek: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
        title: () => import("vue").VNode[];
        actions: () => import("vue").VNode[];
        header: (arg: {
            header: string;
            transition: string;
        }) => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        transition: string;
        header: string;
        style: import("vue").StyleValue;
        title: string;
        disabled: boolean;
        tag: string | import("../../util/index.js").JSXComponent;
        landscape: boolean;
        tile: boolean;
        divided: boolean;
        nextIcon: import("../../composables/icons.js").IconValue;
        prevIcon: import("../../composables/icons.js").IconValue;
        reverseTransition: string;
        hideHeader: boolean;
        modeIcon: import("../../composables/icons.js").IconValue;
        viewMode: "month" | "year" | "months";
        showAdjacentMonths: boolean;
        weekdays: import("../../composables/calendar.js").CalendarWeekdays[];
        weeksInMonth: "static" | "dynamic";
        hideWeekdays: boolean;
        showWeek: boolean;
    } & {
        max?: unknown;
        location?: import("../../util/index.js").Anchor | null | undefined;
        height?: string | number | undefined;
        width?: string | number | undefined;
        active?: string | string[] | undefined;
        min?: unknown;
        border?: string | number | boolean | undefined;
        color?: string | undefined;
        maxHeight?: string | number | undefined;
        maxWidth?: string | number | undefined;
        minHeight?: string | number | undefined;
        minWidth?: string | number | undefined;
        position?: "fixed" | "absolute" | "relative" | "static" | "sticky" | undefined;
        text?: string | undefined;
        month?: string | number | undefined;
        year?: number | undefined;
        class?: any;
        theme?: string | undefined;
        elevation?: string | number | undefined;
        rounded?: string | number | boolean | undefined;
        bgColor?: string | undefined;
        controlHeight?: string | number | undefined;
        headerColor?: string | undefined;
        firstDayOfWeek?: string | number | undefined;
        allowedDates?: unknown[] | ((date: unknown) => boolean) | undefined;
    } & {
        "onUpdate:month"?: ((date: any) => any) | undefined;
        "onUpdate:year"?: ((date: any) => any) | undefined;
        "onUpdate:viewMode"?: ((date: any) => any) | undefined;
    }, {}, {}, {}, {}, {
        active: string | string[];
        transition: string;
        header: string;
        style: import("vue").StyleValue;
        title: string;
        disabled: boolean;
        tag: string | import("../../util/index.js").JSXComponent;
        landscape: boolean;
        rounded: string | number | boolean;
        tile: boolean;
        divided: boolean;
        nextIcon: import("../../composables/icons.js").IconValue;
        prevIcon: import("../../composables/icons.js").IconValue;
        reverseTransition: string;
        hideHeader: boolean;
        modeIcon: import("../../composables/icons.js").IconValue;
        viewMode: "month" | "year" | "months";
        showAdjacentMonths: boolean;
        weekdays: import("../../composables/calendar.js").CalendarWeekdays[];
        weeksInMonth: "static" | "dynamic";
        firstDayOfWeek: string | number;
        hideWeekdays: boolean;
        showWeek: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    transition: string;
    header: string;
    style: import("vue").StyleValue;
    title: string;
    disabled: boolean;
    tag: string | import("../../util/index.js").JSXComponent;
    landscape: boolean;
    tile: boolean;
    divided: boolean;
    nextIcon: import("../../composables/icons.js").IconValue;
    prevIcon: import("../../composables/icons.js").IconValue;
    reverseTransition: string;
    hideHeader: boolean;
    modeIcon: import("../../composables/icons.js").IconValue;
    viewMode: "month" | "year" | "months";
    showAdjacentMonths: boolean;
    weekdays: import("../../composables/calendar.js").CalendarWeekdays[];
    weeksInMonth: "static" | "dynamic";
    hideWeekdays: boolean;
    showWeek: boolean;
} & {
    max?: unknown;
    location?: import("../../util/index.js").Anchor | null | undefined;
    height?: string | number | undefined;
    width?: string | number | undefined;
    active?: string | string[] | undefined;
    min?: unknown;
    border?: string | number | boolean | undefined;
    color?: string | undefined;
    maxHeight?: string | number | undefined;
    maxWidth?: string | number | undefined;
    minHeight?: string | number | undefined;
    minWidth?: string | number | undefined;
    position?: "fixed" | "absolute" | "relative" | "static" | "sticky" | undefined;
    text?: string | undefined;
    month?: string | number | undefined;
    year?: number | undefined;
    class?: any;
    theme?: string | undefined;
    elevation?: string | number | undefined;
    rounded?: string | number | boolean | undefined;
    bgColor?: string | undefined;
    controlHeight?: string | number | undefined;
    headerColor?: string | undefined;
    firstDayOfWeek?: string | number | undefined;
    allowedDates?: unknown[] | ((date: unknown) => boolean) | undefined;
} & {
    "onUpdate:month"?: ((date: any) => any) | undefined;
    "onUpdate:year"?: ((date: any) => any) | undefined;
    "onUpdate:viewMode"?: ((date: any) => any) | undefined;
}, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Omit<{
    'update:modelValue': (date: any) => true;
    'update:month': (date: any) => true;
    'update:year': (date: any) => true;
    'update:viewMode': (date: any) => true;
}, "multiple" | "$children" | "v-slots" | "v-slot:default" | "modelValue" | "update:modelValue" | "v-slot:actions" | "v-slot:title" | "v-slot:header">, string, {
    active: string | string[];
    transition: string;
    header: string;
    style: import("vue").StyleValue;
    title: string;
    disabled: boolean;
    tag: string | import("../../util/index.js").JSXComponent;
    landscape: boolean;
    rounded: string | number | boolean;
    tile: boolean;
    divided: boolean;
    nextIcon: import("../../composables/icons.js").IconValue;
    prevIcon: import("../../composables/icons.js").IconValue;
    reverseTransition: string;
    hideHeader: boolean;
    modeIcon: import("../../composables/icons.js").IconValue;
    viewMode: "month" | "year" | "months";
    showAdjacentMonths: boolean;
    weekdays: import("../../composables/calendar.js").CalendarWeekdays[];
    weeksInMonth: "static" | "dynamic";
    firstDayOfWeek: string | number;
    hideWeekdays: boolean;
    showWeek: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
    title: () => import("vue").VNode[];
    actions: () => import("vue").VNode[];
    header: (arg: {
        header: string;
        transition: string;
    }) => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & (new <T, Multiple extends boolean | "range" | number | (string & {}) = false, TModel = Multiple extends string | number | true ? T[] : T>(props: {
    modelValue?: TModel;
    "onUpdate:modelValue"?: (value: TModel) => void;
    multiple?: Multiple;
}, slots: VDatePickerSlots) => GenericProps<typeof props, typeof slots>) & import("../../util/index.js").FilterPropsOptions<{
    modelValue: null;
    theme: StringConstructor;
    tag: {
        type: import("vue").PropType<string | import("../../util/index.js").JSXComponent>;
        default: string;
    };
    rounded: {
        type: (StringConstructor | BooleanConstructor | NumberConstructor)[];
        default: undefined;
    };
    tile: BooleanConstructor;
    position: {
        type: import("vue").PropType<"fixed" | "absolute" | "relative" | "static" | "sticky">;
        validator: (v: any) => boolean;
    };
    location: import("vue").PropType<import("../../util/index.js").Anchor | null>;
    elevation: {
        type: (StringConstructor | NumberConstructor)[];
        validator(v: any): boolean;
    };
    height: (StringConstructor | NumberConstructor)[];
    maxHeight: (StringConstructor | NumberConstructor)[];
    maxWidth: (StringConstructor | NumberConstructor)[];
    minHeight: (StringConstructor | NumberConstructor)[];
    minWidth: (StringConstructor | NumberConstructor)[];
    width: (StringConstructor | NumberConstructor)[];
    class: import("vue").PropType<import("../../composables/component.js").ClassValue>;
    style: {
        type: import("vue").PropType<import("vue").StyleValue>;
        default: null;
    };
    border: (StringConstructor | BooleanConstructor | NumberConstructor)[];
    color: StringConstructor;
    bgColor: StringConstructor;
    divided: BooleanConstructor;
    landscape: BooleanConstructor;
    title: {
        type: import("vue").PropType<string>;
        default: string;
    };
    hideHeader: BooleanConstructor;
    max: import("vue").PropType<unknown>;
    min: import("vue").PropType<unknown>;
    year: NumberConstructor;
    disabled: {
        type: BooleanConstructor;
        default: null;
    };
    month: (StringConstructor | NumberConstructor)[];
    showAdjacentMonths: BooleanConstructor;
    weekdays: {
        type: import("vue").PropType<import("../../composables/calendar.js").CalendarWeekdays[]>;
        default: () => number[];
    };
    weeksInMonth: Omit<{
        type: import("vue").PropType<"dynamic" | "static">;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<"static" | "dynamic">;
        default: NonNullable<"static" | "dynamic">;
    };
    firstDayOfWeek: {
        type: (StringConstructor | NumberConstructor)[];
        default: undefined;
    };
    allowedDates: import("vue").PropType<unknown[] | ((date: unknown) => boolean)>;
    hideWeekdays: BooleanConstructor;
    multiple: import("vue").PropType<boolean | "range" | number | (string & {})>;
    showWeek: BooleanConstructor;
    transition: {
        type: StringConstructor;
        default: string;
    };
    reverseTransition: {
        type: StringConstructor;
        default: string;
    };
    active: {
        type: import("vue").PropType<string | string[]>;
        default: undefined;
    };
    controlHeight: (StringConstructor | NumberConstructor)[];
    nextIcon: {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    };
    prevIcon: {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    };
    modeIcon: {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    };
    text: StringConstructor;
    viewMode: {
        type: import("vue").PropType<"month" | "months" | "year">;
        default: string;
    };
    header: {
        type: StringConstructor;
        default: string;
    };
    headerColor: StringConstructor;
}, import("vue").ExtractPropTypes<{
    modelValue: null;
    theme: StringConstructor;
    tag: {
        type: import("vue").PropType<string | import("../../util/index.js").JSXComponent>;
        default: string;
    };
    rounded: {
        type: (StringConstructor | BooleanConstructor | NumberConstructor)[];
        default: undefined;
    };
    tile: BooleanConstructor;
    position: {
        type: import("vue").PropType<"fixed" | "absolute" | "relative" | "static" | "sticky">;
        validator: (v: any) => boolean;
    };
    location: import("vue").PropType<import("../../util/index.js").Anchor | null>;
    elevation: {
        type: (StringConstructor | NumberConstructor)[];
        validator(v: any): boolean;
    };
    height: (StringConstructor | NumberConstructor)[];
    maxHeight: (StringConstructor | NumberConstructor)[];
    maxWidth: (StringConstructor | NumberConstructor)[];
    minHeight: (StringConstructor | NumberConstructor)[];
    minWidth: (StringConstructor | NumberConstructor)[];
    width: (StringConstructor | NumberConstructor)[];
    class: import("vue").PropType<import("../../composables/component.js").ClassValue>;
    style: {
        type: import("vue").PropType<import("vue").StyleValue>;
        default: null;
    };
    border: (StringConstructor | BooleanConstructor | NumberConstructor)[];
    color: StringConstructor;
    bgColor: StringConstructor;
    divided: BooleanConstructor;
    landscape: BooleanConstructor;
    title: {
        type: import("vue").PropType<string>;
        default: string;
    };
    hideHeader: BooleanConstructor;
    max: import("vue").PropType<unknown>;
    min: import("vue").PropType<unknown>;
    year: NumberConstructor;
    disabled: {
        type: BooleanConstructor;
        default: null;
    };
    month: (StringConstructor | NumberConstructor)[];
    showAdjacentMonths: BooleanConstructor;
    weekdays: {
        type: import("vue").PropType<import("../../composables/calendar.js").CalendarWeekdays[]>;
        default: () => number[];
    };
    weeksInMonth: Omit<{
        type: import("vue").PropType<"dynamic" | "static">;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<"static" | "dynamic">;
        default: NonNullable<"static" | "dynamic">;
    };
    firstDayOfWeek: {
        type: (StringConstructor | NumberConstructor)[];
        default: undefined;
    };
    allowedDates: import("vue").PropType<unknown[] | ((date: unknown) => boolean)>;
    hideWeekdays: BooleanConstructor;
    multiple: import("vue").PropType<boolean | "range" | number | (string & {})>;
    showWeek: BooleanConstructor;
    transition: {
        type: StringConstructor;
        default: string;
    };
    reverseTransition: {
        type: StringConstructor;
        default: string;
    };
    active: {
        type: import("vue").PropType<string | string[]>;
        default: undefined;
    };
    controlHeight: (StringConstructor | NumberConstructor)[];
    nextIcon: {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    };
    prevIcon: {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    };
    modeIcon: {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    };
    text: StringConstructor;
    viewMode: {
        type: import("vue").PropType<"month" | "months" | "year">;
        default: string;
    };
    header: {
        type: StringConstructor;
        default: string;
    };
    headerColor: StringConstructor;
}>>;
export type VDatePicker = InstanceType<typeof VDatePicker>;
