<?php

/*
 * Function : Insert data from table in db crm into table sla_itseverity in db poms
 * Date : 02 Dec 2019
 * Author : Nur Asmaa
 */

namespace App\crm\sla;

use Carbon\Carbon;
use DateTime;
use Log;
use DB;
use Ramsey\Uuid\Uuid;
use App\Nagios\MigrateUtils;
use Config;
use App\BatchMonitoringStatistic;

class insertITSeveritySlaData {  
    
    public static function runCheckingSlaITSeverityData($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting ... Checking SLA IT Specialist for Severity Data ', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);

        self::insertITSeveritySlaData($dateStart, $dateEnd);
    }

    /* Read data from Database crm copy into database cdc_poms */

    private static function insertITSeveritySlaData($dateStart, $dateEnd) {

        Log::info(self::class . ' Start : ' . __FUNCTION__ . '     ->> Date Start: ' . $dateStart . ', Date End: ' . $dateEnd);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);
        $dtStartTime = Carbon::now();
        
        $yesterdayData = Carbon::yesterday();
        $getCurr = strtotime($yesterdayData);
        $processDate = date('Y-m-d', $getCurr); 
        
         try{
             
             $result = "SELECT DISTINCT `c`.`case_number` AS `case_number`,
                        CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00') AS `itseverity_case_created`,
                        `c`.`name` AS `itseverity_name`,
                        `itspec_tc`.`task_number_c` AS `itseverity_task_number`,
                        `itspec_tc`.`sla_task_flag_c` AS `itseverity_sla_flag`,
                        CONVERT_TZ(`itspec_t`.`date_start`,'+00:00','+08:00') AS `itseverity_start_datetime`,
                        CONVERT_TZ(`itspec_t`.`date_due`,'+00:00','+08:00') AS `itseverity_due_datetime`,
                        CONVERT_TZ(`itspec_tc`.`sla_start_4hr_c`,'+00:00','+08:00') AS `itseverity_actual_start_datetime`,
                        CONVERT_TZ(`itspec_tc`.`sla_stop_4hr_c`,'+00:00','+08:00') AS `itseverity_completed_datetime`,
                        TIMESTAMPDIFF(SECOND,CONVERT_TZ(`itspec_t`.`date_start`,'+00:00','+08:00'),CONVERT_TZ(`itspec_t`.`date_due`,'+00:00','+08:00')) AS `itseverity_available_duration`,
                        TIMESTAMPDIFF(SECOND,CONVERT_TZ(`itspec_tc`.`sla_start_4hr_c`,'+00:00','+08:00'),CONVERT_TZ(`itspec_tc`.`sla_stop_4hr_c`,'+00:00','+08:00')) AS `itseverity_actual_duration` 
                        FROM ((((`cases` `c` 
                        JOIN `cases_cstm` `cc` ON((`c`.`id` = `cc`.`id_c`))) 
                        LEFT JOIN `tasks` `itspec_t` ON((`c`.`id` = `itspec_t`.`parent_id`))) 
                        LEFT JOIN `tasks_cstm` `itspec_tc` ON(((`itspec_t`.`id` = `itspec_tc`.`id_c`) 
                        AND (`itspec_tc`.`sla_task_flag_c` IN ('s1','s2','s3'))))) 
                        LEFT JOIN `cstm_list_app` `subcat` ON(((`cc`.`sub_category_c` = `subcat`.`value_code`) 
                        AND (`subcat`.`type_code` = 'cdc_sub_category_list') 
                        AND (TRIM(`subcat`.`value_code`) <> '') 
                        AND (`subcat`.`value_code` NOT IN ('10712_15034','10714_15842','10713_15534'))))) 
                        WHERE ((`cc`.`incident_service_type_c` = 'incident_it') 
                        AND (`cc`.`request_type_c` = 'incident')
                        AND (`c`.`deleted` = 0)
                        AND (`itspec_t`.`deleted` = 0) 
                        AND (`c`.`status` IN ('Pending_User_Verification', 'Open_Resolved', 'Closed_Approved', 'Closed_Cancelled_Eaduan', 'Closed_Closed', 'Closed_Rejected', 'Closed_Rejected_Eaduan', 'Closed_Verified_Eaduan'))
                        AND (`itspec_t`.`status` = 'Completed') 
                        AND (STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') >= '2022-01-01')
                        AND ( itspec_t.`assigned_user_id` NOT IN ('15c7bd74-12f3-311b-9e8f-5a810702a1a5','5dac7b92-18d0-4beb-b600-413957aa4c26'))
                        AND (`itspec_tc`.`task_number_c` IS NOT NULL)
                        AND (`itspec_tc`.`sla_stop_4hr_c` IS NOT NULL)
                        AND (STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') <= ?)
                        AND (TRIM(`subcat`.`value_code`) <> ''))
                        ORDER BY `c`.`case_number` DESC";
     
            $results = DB::connection('mysql_crm')->select($result, array($processDate));
                       
            if (is_array($results) || is_object($results)) {
                $counter = 0;
                foreach ($results as $data) {
                    $count = DB::connection('mysql')->table('sla_itseverity')
                            ->where('itseverity_task_number', $data->itseverity_task_number)
                            ->count();

                    $insertedTaskNum = DB::connection('mysql_crm')->table('cases')
                            ->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                            ->join('tasks', 'tasks.parent_id', '=', 'cases_cstm.id_c')
                            ->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
                            ->where('tasks_cstm.task_number_c', $data->itseverity_task_number)
                            ->where('cases_cstm.incident_service_type_c', 'incident_it')
                            ->whereIn ('tasks_cstm.sla_task_flag_c', ['s1','s2','s3'])
                            ->where('tasks_cstm.is_sent', 1)
                            ->count();

                    if ($count == 0 && $insertedTaskNum == 0) {
                        
                        $insertDataDate = Carbon::now();

                        DB::connection('mysql')
                                ->insert('insert into sla_itseverity 
                            (case_number, itseverity_case_created, itseverity_name, itseverity_task_number, itseverity_sla_flag, itseverity_start_datetime, itseverity_due_datetime, itseverity_actual_start_datetime, itseverity_completed_datetime, itseverity_available_duration, itseverity_actual_duration, itseverity_insert_data_datetime) 
                            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                                    $data->case_number,
                                    $data->itseverity_case_created,
                                    $data->itseverity_name,
                                    $data->itseverity_task_number,
                                    $data->itseverity_sla_flag,
                                    $data->itseverity_start_datetime,
                                    $data->itseverity_due_datetime,
                                    $data->itseverity_actual_start_datetime,
                                    $data->itseverity_completed_datetime,
                                    $data->itseverity_available_duration,
                                    $data->itseverity_actual_duration,
                                    $insertDataDate
                        ]);
                        
                        DB::connection('mysql_crm')
                                ->table('cases')
                                ->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                                ->join('tasks', 'tasks.parent_id', '=', 'cases_cstm.id_c')
                                ->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
                                ->where('tasks_cstm.task_number_c', $data->itseverity_task_number)
                                ->where('cases_cstm.incident_service_type_c', 'incident_it')
                                ->whereIn ('tasks_cstm.sla_task_flag_c', ['s1','s2','s3'])
                                ->update([
                                    'tasks_cstm.is_sent' => 1,
                                    'tasks_cstm.poms_inserted_date' => $insertDataDate
                        ]);


                        $counter++;
                        if ($counter == 50) {
                            sleep(1);
                            $counter = 0;
                        }

                        $logsdata = self::class . ' Successfully insert data for SLA IT Specialist (Severity) => Date Start : ' . $dtStartTime . ' -> '
                                . 'Query Date End : ' . Carbon::now(). ' Case Number : ' . $data->case_number . ' ,' . ' Task Number : ' . $data->itseverity_task_number . ' , Completed --- Taken Time : ' .
                                json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

                        Log::info($logsdata);
                        dump($logsdata);
                    }
                }
                
                $total = count($results);
                $note = ' Successfully insert data for SLA IT Severity (s1,s2,s3) => Total : ' . $total;
                $projName = 'POMS Integration';
                $batchName = 'insertITSeveritySlaData';
                $remarks = $note;
                $dateModified = Carbon::now();
                $status = 'Success';
                $list = BatchMonitoringStatistic::getBatchName($batchName);
                $dataList =BatchMonitoringStatistic::where('batch_name', 'insertITSeveritySlaData')
                    ->first();
                log::info('Masuk Success');
                if (count($list) > 0) {
                    log::info('Success Satu');
                      BatchMonitoringStatistic::updateBatchMonitoring($dataList,$status,$remarks,$dateModified);
                } else {
                      BatchMonitoringStatistic::createBatchMonitoring($projName,$batchName,$status,$remarks,$dateModified);
                    log::info('Success Dua');
                }
            }

            return $results;
             
         }catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
                $projName = 'POMS Integration';
                $batchName = 'insertITSeveritySlaData';
                $remarks = $exc->getMessage();
                $dateModified = Carbon::now();
                $status = 'Failed';
                $list = BatchMonitoringStatistic::getBatchName($batchName);
                $dataList =BatchMonitoringStatistic::where('batch_name', 'insertITSeveritySlaData')
                    ->first();
                log::info('Masuk Error');
                if (count($list) > 0) {
                    log::info('Error Satu');
                      BatchMonitoringStatistic::updateBatchMonitoring($dataList,$status,$remarks,$dateModified);
                } else {
                       BatchMonitoringStatistic::createBatchMonitoring($projName,$batchName,$status,$remarks,$dateModified);
                    log::info('Error Dua');
                }
            echo $exc->getTraceAsString();
        }     
        return null;
    }

}
