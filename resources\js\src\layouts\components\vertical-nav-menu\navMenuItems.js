/*=========================================================================================
  File Name: sidebarItems.js
  Description: Sidebar Items list. Add / Remove menu items from here.
  ----------------------------------------------------------------------------------------
  Item Name: Vuexy - Vuejs, HTML & Laravel Admin Dashboard Template
  Author: Pixinvent
  Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/


export default [
  {
    url: "/",
    name: "Home",
    slug: "home",
    icon: "HomeIcon",
  },
  {
    url: "/sla-dashboard",
    name: "SLA Dashboard",
    slug: "slaDashboard",
    icon: "TargetIcon",
  },
  {
    url: "/sla-report",
    name: "SLA Report",
    slug: "slaReport",
    icon: "FileTextIcon",
  },
  {
    url: "/tech-dashboard",
    name: "Technical Dashboard",
    slug: "technicalDashboard",
    icon: "CpuIcon",
  },
  // {
  //   url: "/crud-demo",
  //   name: "Vue-Laravel Demo",
  //   slug: "crudDemo",
  //   icon: "MailIcon",
  // },
  {
    url: "/admin",
    name: "Administrator",
    slug: "adminPage",
    icon: "SettingsIcon",
  },
]
