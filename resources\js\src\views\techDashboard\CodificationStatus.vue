<template>
    <div>
        <div class="vx-row">
            <div id="top-left" class="vx-col w-full sm:w-1/2 md:w-1/2 lg:w-1/4 xl:w-1/2 vs-con-loading__container">
                <statistics-card-line
                    hideChart
                    class="mb-base"
                    icon="ActivityIcon"
                    icon-right
                    :statistic="totalToday"
                    statisticTitle="Codification Request Yesterday"
                    color="warning" />
            </div>
            <div id="top-right" class="vx-col w-full sm:w-1/2 md:w-1/2 lg:w-1/4 xl:w-1/2 vs-con-loading__container">
                <statistics-card-line
                    hideChart
                    class="mb-base"
                    icon="ActivityIcon"
                    icon-right
                    :statistic="totalUntilToday"
                    statisticTitle="Codification Request Until Yesterday"
                    color="success" />
            </div>
        </div>
        <div class="vx-row">
            <div class="vx-col lg:w-1/2 mb-base">
                <vx-card id="bottom-left" title="Codification Request Yesterday (By Status)" class="vs-con-loading__container">
                    <!-- SLOT = ACTION -->
                    <!-- <template slot="actions">
                        <change-time-duration-dropdown />
                    </template> -->

                    <!-- CHART -->
                    <div slot="no-body">
                        <vue-apex-charts type="donut" height="340" class="mb-12 mt-4" :options="codificationRequestByStatusToday.chartOptions" :series="codificationRequestByStatusToday.series" />
                    </div>

                    <!-- CHART DATA -->
                    <ul>
                        <li v-for="codiData in codificationRequestByStatusToday.analyticsData" :key="codiData.device" class="flex mb-3">
                            <feather-icon :icon="codiData.icon" :svgClasses="[`h-5 w-5 stroke-current`]" :style="`color: ${codiData.color}`"></feather-icon>
                            <span class="ml-2 inline-block font-semibold">{{ codiData.status }}</span>
                            <span class="mx-2">-</span>
                            <div class="ml-auto flex -mr-1">
                                <span class="mr-1">{{ codiData.totalRequest }}</span>
                            </div>
                        </li>
                    </ul>
                </vx-card>
            </div>
            <div class="vx-col lg:w-1/2 mb-base">
                <vx-card id="bottom-right" title="Codification Request Until Yesterday (By Status)" class="vs-con-loading__container">
                    <!-- SLOT = ACTION -->
                    <!-- <template slot="actions">
                        <change-time-duration-dropdown />
                    </template> -->

                    <!-- CHART -->
                    <div slot="no-body">
                        <vue-apex-charts type="donut" height="340" class="mb-12 mt-4" :options="codificationRequestByStatusUntilToday.chartOptions" :series="codificationRequestByStatusUntilToday.series" />
                    </div>

                    <!-- CHART DATA -->
                    <ul>
                        <li v-for="codiData in codificationRequestByStatusUntilToday.analyticsData" :key="codiData.device" class="flex mb-3">
                            <feather-icon :icon="codiData.icon" :svgClasses="[`h-5 w-5 stroke-current`]" :style="`color: ${codiData.color}`"></feather-icon>
                            <span class="ml-2 inline-block font-semibold">{{ codiData.status }}</span>
                            <span class="mx-2">-</span>
                            <div class="ml-auto flex -mr-1">
                                <span class="mr-1">{{ codiData.totalRequest }}</span>
                            </div>
                        </li>
                    </ul>
                </vx-card>
            </div>
        </div>
    </div>
    
</template>
<script>

import StatisticsCardLine from '@/components/statistics-cards/StatisticsCardLine.vue'
import VueApexCharts from 'vue-apexcharts'
import axios from "axios";

export default{
    data() {
        return {
            totalToday: 0,
            totalUntilToday: 0,
            codificationRequestByStatusToday: {
                analyticsData: [],
                series: [47,6,48],
                chartOptions: {
                    labels: ["Extension Code Request Completed","Pending Item Code Review","Pending Extension Code Approval"],
                    dataLabels: {
                        enabled: false
                    },
                    legend: { show: false },
                    chart: {
                        offsetY: 30,
                        type: 'donut',
                        toolbar: {
                            show: false
                        }
                    },
                    stroke: { width: 0 },
                    colors: ["#7961F9","#FF9F43","#EA5455"],
                    fill: {
                        type: 'gradient',
                        gradient: {
                            gradientToColors: ["#9c8cfc","#FFC085","#f29292"]
                        }
                    }
                }
            },
            
            codificationRequestByStatusUntilToday: {
                analyticsData: [],
                series: [56849, 1361, 944, 253, 48],
                chartOptions: {
                    labels: ['Extension Code Request Completed', 'Pending Extension Code Approval', 'Pending Item Code Review', 'Pending Extension Code Endorsement', 'Pending Item Code Creation'],
                    dataLabels: {
                        enabled: false
                    },
                    legend: { show: false },
                    chart: {
                        offsetY: 30,
                        type: 'donut',
                        toolbar: {
                            show: false
                        }
                    },
                    stroke: { width: 0 },
                    colors: ['#7961F9', '#FF9F43', '#EA5455', '#9b59b6', '#2ecc71'],
                    fill: {
                        type: 'gradient',
                        gradient: {
                            gradientToColors: ['#9c8cfc', '#FFC085', '#f29292', '#c090d4', '#5ee698']
                        }
                    }
                }
            },
        }
    },
    components: {
        StatisticsCardLine,
        VueApexCharts
    },
    methods: {
        initLoadingSpinner(elementId) {
            this.$vs.loading({
                container: elementId,
                scale: 0.6
            });
        },
        getCodificationData: function () {
            axios.get('/api/kpi/codification/today').then(response => {
                let data = response.data
                // console.log(data)
                /* YESTERDAY */
                this.totalToday = data.today.today_total
                this.codificationRequestByStatusToday.analyticsData = data.today.today_list
                this.codificationRequestByStatusToday.series = data.today.today_series
                this.codificationRequestByStatusToday.chartOptions = {
                    labels: data.today.today_labels,
                    colors: data.today.today_series_color1,
                    fill: {
                        gradient: {
                            gradientToColors: data.today.today_series_color2
                        }
                    }
                }

                /* UNTIL YESTERDAY */
                this.totalUntilToday = data.toDate.todate_total
                this.codificationRequestByStatusUntilToday.analyticsData = data.toDate.todate_list
                this.codificationRequestByStatusUntilToday.series = data.toDate.todate_series
                this.codificationRequestByStatusUntilToday.chartOptions = {
                    labels: data.toDate.todate_labels,
                    colors: data.toDate.todate_series_color1,
                    fill: {
                        gradient: {
                            gradientToColors: data.toDate.todate_series_color2
                        }
                    }
                }
                this.$vs.loading.close('#top-left > .con-vs-loading');
                this.$vs.loading.close('#top-right > .con-vs-loading');
                this.$vs.loading.close('#bottom-left > .con-vs-loading');
                this.$vs.loading.close('#bottom-right > .con-vs-loading');
                VueApexCharts.render()
            })
            .catch(error => {
                err => console.log(error)
            })
        },
    },
    mounted: function () {
        this.initLoadingSpinner('#top-left');
        this.initLoadingSpinner('#top-right');
        this.initLoadingSpinner('#bottom-left');
        this.initLoadingSpinner('#bottom-right');
        this.getCodificationData();
    },
}
</script>
