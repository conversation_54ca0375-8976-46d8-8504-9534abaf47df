<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class EpPerformanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            'date_time' => $this->date_time,
            'module' => $this->module,
            'transaction' => $this->transaction,
            'duration_result' => (int)$this->duration_result,
        ];
    }

    public function with($request)
    {
        return [
            'version' => '1.0.0',
            'author_url' => 'http://www.commercedc.com.my/'
        ];
    }
}
