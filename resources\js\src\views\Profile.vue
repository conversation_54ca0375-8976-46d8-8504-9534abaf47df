<template>
    <div class="profile">
        <!-- Avatar -->
        <div class="avatar-container" @mouseover="hover = true" @mouseleave="hover = false">
            <img class="avatar" :src="photoURL" alt="User Avatar" />
            <a v-if="hover" class="edit-avatar" @click="editAvatar" style="cursor: pointer;">Edit</a>
        </div>
        <!-- User Information -->
        <div class="profile-header">
            <h1>{{ user.name }}</h1>
        </div>
        <div class="profile-info">
            <p><strong>Username:</strong> {{ user.username }}</p>
            <p><strong>Email:</strong> {{ user.email }}</p>
            <p><strong>Last Login:</strong> {{ user.last_login }}</p>
        </div>
        <!-- Buttons -->
        <div class="profile-buttons">
            <vs-button class="mr-2" @click="resetPassword" color="warning">
                <i class="feather icon-lock"></i> Reset Password
            </vs-button>
            <vs-button class="mr-2" @click="logout" color="primary">
                <i class="feather icon-log-out"></i> Logout
            </vs-button>
        </div>

        <form data-vv-scope="form-reset" class="p-6">
            <vs-popup
                classContent="popup-example"
                :title="'Reset User Password - ' + user.name"
                :active.sync="popupResetPass"
            >
                <vs-input
                    label="Existing Password"
                    type="password"
                    v-model="old_password"
                    class="mt-5 w-full"
                    name="old_password"
                    v-validate="'required'"
                />
                <span class="text-danger text-sm" v-show="errors.has('old_password')">{{
                    errors.first("old_password")
                }}</span>

                <vs-input
                    label="New Password"
                    type="password"
                    v-model="new_password"
                    class="mt-5 w-full"
                    name="new_password"
                    v-validate="'required'"
                />
                <span class="text-danger text-sm" v-show="errors.has('new_password')">{{
                    errors.first("new_password")
                }}</span>

                <vs-input
                    label="Confirm New Password"
                    type="password"
                    v-model="new_password_confirm"
                    class="mt-5 w-full"
                    name="new_password_confirm"
                    v-validate="'required'"
                />
                <span class="text-danger text-sm" v-show="errors.has('new_password_confirm')">{{
                    errors.first("new_password_confirm")
                }}</span>

                <vs-button
                    class="mt-6"
                    @click="updateUserPassword"
                    :disabled="!isResetFormValid"
                    color="primary"
                    type="filled"
                >
                    Confirm
                </vs-button>
                <vs-button
                    class="mt-6"
                    @click="popupResetPass = false"
                    color="warning"
                    type="filled"
                >
                    Cancel
                </vs-button>

                <div v-if="resetPasswordStatus" class="mt-6">
                    <p v-if="resetPasswordStatus === 'success'" class="text-success">
                        Password reset successful.
                    </p>
                    <p v-if="resetPasswordStatus === 'error'" class="text-danger">
                        {{ resetPasswordErrorMessage }}
                    </p>
                </div>
            </vs-popup>
        </form>
    </div>
</template>

<script>
import axios from "axios";
export default {
    data() {
        return {
            hover: false,
            user: {},
            photoURL: require("@assets/images/portrait/small/avatar-default.png"),
            popupResetPass: false,
            old_password: "",
            new_password: "",
            new_password_confirm: "",
            resetPasswordStatus: null,
            resetPasswordErrorMessage: "",
        };
    },
    mounted() {
        this.fetchUser();
    },
    computed: {
        isResetFormValid() {
            return (
                !this.errors.any() &&
                this.old_password &&
                this.new_password &&
                this.new_password_confirm &&
                this.new_password === this.new_password_confirm
            );
        },
    },
    methods: {

        initValues() {
            this.old_password = "";
            this.new_password = "";
            this.new_password_confirm = "";
        },

        fetchUser() {
            axios.defaults.headers.common["Authorization"] = `Bearer ${localStorage.getItem(
                "accessToken"
            )}`;
            axios
                .get("/api/user")
                .then((response) => {
                    this.user = response.data;
                    if (this.user.avatar) {
                        this.photoURL = `/images/avatars/${this.user.avatar}`;
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        },

        resetPassword() {
            this.popupResetPass = true;
            this.resetPasswordStatus = null;
            this.resetPasswordErrorMessage = "";
        },

        updateUserPassword() {
            this.$validator.validateAll("form-reset").then((result) => {
                if (result) {
                    const obj = {
                        user_id: this.user.id,
                        old_password: this.old_password,
                        new_password: this.new_password,
                        new_password_confirm: this.new_password_confirm,
                    };

                    axios
                        .post("api/user/reset-password", obj)
                        .then((response) => {
                            if (response.data.status === "success") {
                                console.log('success...');
                                console.log(response.data.message);
                                this.popupResetPass = false;
                                // this.$parent.displayUsers();
                                this.resetPasswordStatus = "success";
                                this.notifyUser(
                                    "success",
                                    "Success!",
                                    response.data.message,
                                    "icon-check-circle"
                                );

                                this.initValues();
                                setTimeout(() => {
                                    this.logout();
                                }, 3000);
                            } else {
                                this.resetPasswordStatus = "error";
                                this.resetPasswordErrorMessage = response.data.message;
                            }
                        })
                        .catch((error) => {
                            console.log(error);
                            this.resetPasswordStatus = "error";
                            this.resetPasswordErrorMessage = error.response.data.message;
                        });
                }
            });
        },

        editAvatar() {
            let fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'image/*';
            fileInput.onchange = e => {
                let file = e.target.files[0];
                if (file.size <= 1024 * 1024) { // Maximum size: 1MB
                    let reader = new FileReader();
                    reader.onloadend = () => {
                        this.photoURL = reader.result;
                        const formData = new FormData();
                        formData.append('avatar', file);
                        formData.append('id', this.user.id); // Include user id in the form data

                        axios.defaults.headers.common["Authorization"] = `Bearer ${localStorage.getItem(
                            "accessToken"
                        )}`;
                        axios
                            .post("api/user/edit-avatar", formData)
                            .then((response) => {
                                console.log('Avatar updated successfully.');
                                this.notifyUser(
                                    "success",
                                    "Success!",
                                    "Avatar updated successfully.",
                                    "icon-check-circle"
                                );
                                location.reload(); // Reload the browser
                            })
                            .catch((error) => {
                                console.log(error);
                                this.notifyUser(
                                    "danger",
                                    "Error!",
                                    "Failed to update avatar.",
                                    "icon-alert-triangle"
                                );
                            });
                    };
                    reader.readAsDataURL(file);
                } else {
                    console.log('The selected image exceeds the maximum size of 1MB.');
                    this.notifyUser(
                        "danger",
                        "Error!",
                        "The selected image exceeds the maximum size of 1MB.",
                        "icon-alert-triangle"
                    );
                }
            };
            fileInput.click();
        },

        notifyUser(color, title, text, icon) {
            this.$vs.notify({
                color: color,
                title: title,
                text: text,
                iconPack: "feather",
                icon: icon,
            });
        },

        logout() {
            if (localStorage.getItem("accessToken")) {
                localStorage.removeItem("accessToken");
                this.$router.push("/login").catch(() => {});
            }
        },
    },
};
</script>

<style scoped>
.profile {
    max-width: 500px;
    margin: 0 auto;
    margin-top: 70px;
    padding: 20px;
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    text-align: center; /* Center align content */
}

.avatar-container {
    margin: auto;
    position: relative;
    margin-top: -70px; /* Adjust this value according to your design */
    width: 100px; /* Adjust to match your avatar size */
    height: 100px; /* Adjust to match your avatar size */
    border-radius: 50%; /* Makes the container circular */
    overflow: hidden; /* Ensures the overlay is also circular */
}

.avatar {
    width: 100px; /* Adjust avatar size */
    height: 100px;
    border-radius: 50%;
    border: 4px solid #fff; /* White border around avatar */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Add shadow effect */
    object-fit: cover; /* Prevents image from stretching */
}

.edit-avatar {
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.avatar-container:hover .edit-avatar {
    opacity: 1;
}

.profile-header {
    /* background-color: #007bff; */
    color: #fff;
    padding: 20px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.profile-info {
    text-align: left;
    margin-top: 20px;
}

.profile-info p {
    margin-bottom: 10px;
    font-size: 16px;
    color: #333;
}

.profile-buttons {
    margin-top: 50px;
}

.reset-password-button,
.logout-button {
    margin-right: 10px;
}
</style>
