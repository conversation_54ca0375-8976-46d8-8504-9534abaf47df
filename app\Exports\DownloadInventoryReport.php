<?php

namespace App\Exports;

//use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromCollection;

use DB;

class DownloadInventoryReport implements FromCollection, WithHeadings, ShouldAutoSize, WithMapping
{
    use Exportable;
    
   //  public $catID = [];
    // public $productNamecatID = [];

    public function __construct(int $catID, string $productName)
    {
        $this->catID = $catID;
        $this->productName = $productName;
    }

    public function collection()
    {
       $queryGetListInv = "SELECT a.`asset_tag` AS AssetTag,b.name AS productDesc, c.`name` AS ManufatureName,f.`name` AS childLocation , b.model_number as modelNumber,e.`name` AS categoryName,e.`id` as cat_id,b.`id` as model_id,
       e.`eula_text` AS detailsCategoryname, a.`serial` AS serialNum, a.`created_at` AS dateCreated
       ,g.`name` AS parentLocation,d.`name` AS companyName,location_id as locID,
       _snipeit_ip_30 as IP,_snipeit_hostname_39 as hostName,_snipeit_location_40 as Location
       FROM assets a
       LEFT JOIN `models` b ON a.`model_id`=b.`id`
       LEFT JOIN `manufacturers` c ON c.`id`=b.`manufacturer_id`
       LEFT JOIN `companies` d ON d.`id`=a.`company_id`
       LEFT JOIN `categories` e ON e.`id`=b.`category_id`
       LEFT JOIN locations f ON f.`id`= a.`location_id`
       LEFT JOIN `locations` g ON g.`id`=f.`parent_id`
       WHERE a.`deleted_at` IS NULL
       and e.`id` = $this->catID 
       AND b.`name` LIKE '$this->productName'";

        $data = DB::connection('mysql_inventory')->select($queryGetListInv);
       // dd($data);
        return collect($data); 
        //return $data;
    }

    
    public function map($data): array
    {
        return [
            $data->AssetTag,
            $data->productDesc,
            $data->ManufatureName,
            $data->childLocation,
            $data->modelNumber,
            $data->categoryName
        ];
    }


    public function headings(): array
    {
        return [
          'Asset Tag',
          'Product Name',
          'Manufaturer Name',
          'Location',
          'Model No',
          'Category Name'
        ];
    }
}