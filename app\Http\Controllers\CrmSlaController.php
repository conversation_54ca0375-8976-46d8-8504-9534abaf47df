<?php

namespace App\Http\Controllers;

use App\Services\CrmSlaService;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class CrmSlaController extends Controller
{

    public static function crmSlaService()
    {
        return new CrmSlaService;
    }

    public function dashboardCrmCount()
    {

        $todayDate = Carbon::today()->toDateString();

        $tasks = DB::connection('mysql_crm')
            ->table('cases AS c')
            ->join('cases_cstm AS cc', 'cc.id_c', '=', 'c.id')
            ->join('tasks AS t', 't.parent_id', '=', 'c.id')
            ->join('tasks_cstm AS tc', 'tc.id_c', '=', 't.id')
            ->select('t.name AS task_name', 'tc.sla_task_flag_c', 't.status AS task_status', DB::raw('COUNT(*) AS total'))
            ->where('c.deleted', 0)
            ->where('t.deleted', 0)
            ->where('cc.incident_service_type_c', 'incident_it')
            ->where(DB::raw("STR_TO_DATE(CONVERT_TZ(c.date_entered, '+00:00', '+08:00'), '%Y-%m-%d')"), '>=', $todayDate)
            ->groupBy('t.name', 'tc.sla_task_flag_c', 't.status')
            ->get();

        // Initialize an array to store the mapped data with three statuses for each item
        $mappedData = [
            ["name" => "Case Acknowledgement", "code" => "acknowledge", "in_progress" => 0, "within_sla" => 0, "exceed_sla" => 0],
            ["name" => "Resolution Identification Time (RIT)", "code" => "rit", "in_progress" => 0, "within_sla" => 0, "exceed_sla" => 0],
            ["name" => "Severity 1 Case", "code" => "s1", "in_progress" => 0, "within_sla" => 0, "exceed_sla" => 0],
            ["name" => "Severity 2 Case", "code" => "s2", "in_progress" => 0, "within_sla" => 0, "exceed_sla" => 0],
            ["name" => "Severity 3 Case", "code" => "s3", "in_progress" => 0, "within_sla" => 0, "exceed_sla" => 0],
            ["name" => "Severity 4 Case", "code" => "s4", "in_progress" => 0, "within_sla" => 0, "exceed_sla" => 0]
        ];

        // Map the fetched data according to the conditions
        foreach ($tasks as $task) {
            switch ($task->task_name) {
                case "Initial Task - IT Incident":
                    if ($task->sla_task_flag_c == 2) {
                        if ($task->task_status == "Pending Acknowledgement") {
                            $mappedData[0]["in_progress"] += $task->total;
                        } elseif ($task->task_status == "Completed") {
                            $mappedData[0]["within_sla"] += $task->total;
                        }
                    }
                    break;
                case "Assigned to Group IT Specialist(Production Support)":
                    if ($task->sla_task_flag_c == 3) {
                        if ($task->task_status == "Pending Acknowledgement" || $task->task_status == "Acknowledge") {
                            $mappedData[1]["in_progress"] += $task->total;
                        } elseif ($task->task_status == "Completed") {
                            $mappedData[1]["within_sla"] += $task->total;
                        }
                    } elseif ($task->sla_task_flag_c == "s1") {
                        if ($task->task_status == "Pending Acknowledgement" || $task->task_status == "Acknowledge") {
                            $mappedData[2]["in_progress"] += $task->total;
                        } elseif ($task->task_status == "Completed") {
                            $mappedData[2]["within_sla"] += $task->total;
                        }
                    } elseif ($task->sla_task_flag_c == "s2") {
                        if ($task->task_status == "Pending Acknowledgement" || $task->task_status == "Acknowledge") {
                            $mappedData[3]["in_progress"] += $task->total;
                        } elseif ($task->task_status == "Completed") {
                            $mappedData[3]["within_sla"] += $task->total;
                        }
                    } elseif ($task->sla_task_flag_c == "s3") {
                        if ($task->task_status == "Pending Acknowledgement" || $task->task_status == "Acknowledge") {
                            $mappedData[4]["in_progress"] += $task->total;
                        } elseif ($task->task_status == "Completed") {
                            $mappedData[4]["within_sla"] += $task->total;
                        }
                    }
                    break;
                case "Assigned to Approver":
                    if ($task->task_status == "Pending Acknowledgement" || $task->task_status == "Acknowledge") {
                        $mappedData[5]["in_progress"] += $task->total;
                    } elseif ($task->task_status == "Completed") {
                        $mappedData[5]["within_sla"] += $task->total;
                    }
                    break;
            }
        }

        return response()->json(['data' => $mappedData, 'request' => ['date' => $todayDate]], 200);
    }

    public function getTaskList(Request $request)
    {
        // Get the JSON content from the request
        $json = $request->getContent();
        $data = json_decode($json, true);
        
        // Map $slaTaskFlag and $taskStatus codes to their corresponding values
        $slaTaskFlagMappings = [
            'acknowledge' => 2,
            'rit' => 3,
            's1' => 's1',
            's2' => 's2',
            's3' => 's3',
            's4' => 'Assigned to Approver'
        ];

        $taskStatusMappings = [
            'in_progress' => 'Pending Acknowledgement',
            'within_sla' => 'Completed',
            'exceed_sla' => 'Completed' // This will be filtered out in the query
        ];

        // Get parameters from the request and map them to their corresponding values
        $slaTaskFlag = $slaTaskFlagMappings[$data['code']] ?? null;
        $taskStatus = $taskStatusMappings[$data['status']] ?? null;

        if (!$slaTaskFlag || !$taskStatus) {
            return response()->json(['error' => 'Invalid code or status'], 400);
        }

        // Get today's date in the required format using Carbon
        $today = Carbon::now()->toDateString();

        // Build the query using Laravel's query builder
        $query = DB::connection('mysql_crm')
            ->table('cases AS c')
            ->select(
                'c.case_number',
                'c.name AS case_name',
                't.name AS task_name',
                DB::raw("CONVERT_TZ(t.date_start, '+00:00', '+08:00') AS date_start"),
                DB::raw("CONVERT_TZ(t.date_due, '+00:00', '+08:00') AS date_due"),
                't.status AS task_status',
                'tc.sla_task_flag_c',
                't.name AS task_name_value'
            )
            ->join('cases_cstm AS cc', 'cc.id_c', '=', 'c.id')
            ->join('tasks AS t', 't.parent_id', '=', 'c.id')
            ->join('tasks_cstm AS tc', 'tc.id_c', '=', 't.id')
            ->where('c.deleted', 0)
            ->where('t.deleted', 0)
            ->where('cc.incident_service_type_c', 'incident_it')
            ->whereRaw("STR_TO_DATE(CONVERT_TZ(c.date_entered, '+00:00', '+08:00'), '%Y-%m-%d') >= ?", [$today])
            ->where(function($query) use ($slaTaskFlag) {
                $query->where('tc.sla_task_flag_c', $slaTaskFlag)
                      ->orWhere('t.name', $slaTaskFlag); // Checking for task name
            });

        // Define which task flags should include both 'Pending Acknowledgement' and 'Acknowledge' statuses
        $multiStatusTaskFlags = [
            3,                     // Resolution Identification Time (RIT)
            's1',                  // Severity 1
            's2',                  // Severity 2
            's3',                  // Severity 3
            'Assigned to Approver' // Approver tasks
        ];

        // Handle status filtering based on task type and status
        if ($data['status'] === 'in_progress') {
            $query->where(function($q) use ($slaTaskFlag, $multiStatusTaskFlags) {
                // For tasks that can be either 'Pending Acknowledgement' or 'Acknowledge'
                if (in_array($slaTaskFlag, $multiStatusTaskFlags)) {
                    $q->where('t.status', 'Pending Acknowledgement')
                      ->orWhere('t.status', 'Acknowledge');
                } else {
                    // For other tasks, only 'Pending Acknowledgement' is considered in progress
                    $q->where('t.status', 'Pending Acknowledgement');
                }
            });
        } else if ($data['status'] === 'within_sla') {
            // For 'within_sla', only 'Completed' status is considered
            $query->where('t.status', 'Completed');
        } else {
            // For other statuses, use the mapped status
            $query->where('t.status', $taskStatus);
        }

        $results = $query->orderBy('c.case_number', 'ASC')
                       ->orderBy('tc.task_number_c', 'ASC')
                       ->get();

        // Return the results in JSON format
        return response()->json($results);
    }

    public function dashboardCrmSlaCount()
    {
        $countItIncidentPendAck = 0;
        $countItIncidentAck = 0;

        $countItIncidentPendAckExceed = 0;
        $countItIncidentAckExceed = 0;

        $countItSpecIncidentPendAck = 0;
        $countItSpecIncidentAck = 0;

        $countItSpecIncidentPendAckExceed = 0;
        $countItSpecIncidentAckExceed = 0;

        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $current = Carbon::now();

        $listTasksItCoord = self::crmSlaService()->getDashboardCRMITCoord();
        // dump($listTasks);
        if (count($listTasksItCoord) > 0) {
            foreach ($listTasksItCoord as $tasks) {

                $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");



                if ($tasks->taskStatus == $PENDING_ACK && $tasks->acknowledgetime == '') {
                    if ($datedue > $current) {
                        $countItIncidentPendAck++;
                    } else {
                        $countItIncidentPendAckExceed++;
                    }
                }
                if ($tasks->taskStatus == $ACKNOWLEDGE && $tasks->acknowledgetime != '') {
                    if ($acknowledgetime <= $datedue) {
                        $countItIncidentAck++;
                    } else {
                        $countItIncidentAckExceed++;
                    }
                }
            }
        }

        $listTasksItSpec = self::crmSlaService()->getDashboardCRMITSpec();
        if (count($listTasksItSpec) > 0) {
            foreach ($listTasksItSpec as $tasks) {

                $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

                if ($tasks->taskStatus == $PENDING_ACK && $tasks->acknowledgetime == '' && $tasks->taskSeverity == null && $tasks->taskFlag == '') {

                    if ($datedue > $current) {
                        $countItSpecIncidentPendAck++;
                    } else {
                        $countItSpecIncidentPendAckExceed++;
                    }
                }
                if ($tasks->taskStatus == $ACKNOWLEDGE && $tasks->acknowledgetime != '' && $tasks->taskSeverity == null && $tasks->taskFlag == 3) {
                    if ($acknowledgetime <= $datedue) {
                        $countItSpecIncidentAck++;
                    } else {
                        $countItSpecIncidentAckExceed++;
                    }
                }
            }
        }

        $incident_pending_ack_it_coord = ([
            'status' => 'Pending Acknowledgement',
            'not_exceed' => $countItIncidentPendAck,
            'exceed' => $countItIncidentPendAckExceed
        ]);

        $incident_ack_it_coord = ([
            'status' => 'Acknowledge',
            'not_exceed' => $countItIncidentAck,
            'exceed' => $countItIncidentAckExceed
        ]);

        $incident_pending_ack_it_spec = ([
            'status' => 'Pending Acknowledgement',
            'not_exceed' => $countItSpecIncidentPendAck,
            'exceed' => $countItSpecIncidentPendAckExceed
        ]);

        $incident_ack_it_spec = ([
            'status' => 'Acknowledge',
            'not_exceed' => $countItSpecIncidentAck,
            'exceed' => $countItSpecIncidentAckExceed
        ]);

        return response()->json([
            // 'incident_itcoordinator' => ['task' => 'IT Coordinator (15 Min)','detail' => [$incident_pending_ack_it_coord, $incident_ack_it_coord]],
            'incident_itcoordinator' => ['task' => 'IT Coordinator (15 Min)', 'detail' => [$incident_pending_ack_it_coord]],
            // 'incident_itspecialist' => ['task' => 'IT Specialist (4 Hour)', 'detail' => [$incident_pending_ack_it_spec, $incident_ack_it_spec]],
            'incident_itspecialist' => ['task' => 'IT Specialist (4 Hour)', 'detail' => [$incident_ack_it_spec]],
        ]);
    }

    public function dashboardCrmSlaList($status)
    {
        $resultList = array();

        /* IT COORDINATOR 15 Minutes */
        if ($status === 'list_ItCoordPendingAck15min') {
            $resultList = self::listPendingAckItCoord();
        }

        if ($status === 'list_ItCoordPendingAck15minExceed') {
            $resultList = self::listPendingAckItCoordExceed();
        }

        if ($status === 'list_ItCoordAck15min') {
            $resultList = self::listAckItCoord();
        }

        if ($status === 'list_ItCoordAck15minExceed') {
            $resultList = self::listAckItCoordExceed();
        }

        /* IT SPECIALIST 4 Hours */
        if ($status === 'list_ItSpecPendingAck4hour') {
            $resultList = self::listPendingAckItSpec();
        }

        if ($status === 'list_ItSpecPendingAck4hourExceed') {
            $resultList = self::listPendingAckItSpecExceed();
        }

        if ($status === 'list_ItSpecAck4hour') {
            $resultList = self::listAckItSpec();
        }

        if ($status === 'list_ItSpecAck4hourExceed') {
            $resultList = self::listAckItSpecExceed();
        }

        return $resultList;
    }

    protected function listPendingAckItCoord()
    {
        $TASK_STATUS = 'Pending Acknowledgement';
        $current = Carbon::now();
        $result = array();

        $list = self::crmSlaService()->getDashboardCRMITCoord();

        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $TASK_STATUS) {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = [
                    'no' => ++$counter,
                    'case_number' => $value->caseNumber,
                    'sub_category' => $value->subCategory,
                    'case_name' => $value->caseName,
                    'task_status' => $value->taskStatus,
                    'date_start' => $datestart,
                    'date_due' => $datedue,
                    'time_remaining' => $timeRemaining
                ];

                array_push($result, $data);
            }
        }

        return $result;
    }

    protected function listPendingAckItCoordExceed()
    {
        $TASK_STATUS = 'Pending Acknowledgement';
        $current = Carbon::now();
        $result = array();

        $list = self::crmSlaService()->getDashboardCRMITCoord();

        $counter = 0;
        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $TASK_STATUS) {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = [
                    'no' => ++$counter,
                    'case_number' => $value->caseNumber,
                    'sub_category' => $value->subCategory,
                    'case_name' => $value->caseName,
                    'task_status' => $value->taskStatus,
                    'date_start' => $datestart,
                    'date_due' => $datedue,
                    'time_exceed' => $timeExceeding
                ];

                array_push($result, $data);
            }
        }
        return $result;
    }

    protected function listAckItCoord()
    {
        $TASK_STATUS = 'Acknowledge';
        $result = array();

        $list = self::crmSlaService()->getDashboardCRMITCoord();

        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->taskStatus == $TASK_STATUS) {
                $data = [
                    'no' => ++$counter,
                    'case_number' => $value->caseNumber,
                    'sub_category' => $value->subCategory,
                    'case_name' => $value->caseName,
                    'task_status' => $value->taskStatus,
                    'date_start' => $datestart,
                    'date_due' => $datedue,
                    'acknowledge_time' => $acknowledgeTime
                ];

                array_push($result, $data);
            }
        }

        return $result;
    }

    protected function listAckItCoordExceed()
    {
        $TASK_STATUS = 'Acknowledge';
        $result = array();

        $list = self::crmSlaService()->getDashboardCRMITCoord();

        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->taskStatus == $TASK_STATUS) {
                $data = [
                    'no' => ++$counter,
                    'case_number' => $value->caseNumber,
                    'sub_category' => $value->subCategory,
                    'case_name' => $value->caseName,
                    'task_status' => $value->taskStatus,
                    'date_start' => $datestart,
                    'date_due' => $datedue,
                    'acknowledge_time' => $acknowledgeTime
                ];

                array_push($result, $data);
            }
        }

        return $result;
    }

    protected function listPendingAckItSpec()
    {
        $TASK_STATUS = 'Pending Acknowledgement';
        $result = array();
        $current = Carbon::now();

        $list = self::crmSlaService()->getDashboardCRMITSpec();

        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $TASK_STATUS) {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';
                $data = [
                    'no' => ++$counter,
                    'case_number' => $value->caseNumber,
                    'sub_category' => $value->subCategory,
                    'case_name' => $value->caseName,
                    'task_status' => $value->taskStatus,
                    'date_start' => $datestart,
                    'date_due' => $datedue,
                    'time_remaining' => $timeRemaining
                ];

                array_push($result, $data);
            }
        }

        return $result;
    }

    protected function listPendingAckItSpecExceed()
    {
        $TASK_STATUS = 'Pending Acknowledgement';
        $result = array();
        $current = Carbon::now();

        $list = self::crmSlaService()->getDashboardCRMITSpec();

        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $TASK_STATUS) {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';
                $data = [
                    'no' => ++$counter,
                    'case_number' => $value->caseNumber,
                    'sub_category' => $value->subCategory,
                    'case_name' => $value->caseName,
                    'task_status' => $value->taskStatus,
                    'date_start' => $datestart,
                    'date_due' => $datedue,
                    'time_exceed' => $timeExceeding
                ];

                array_push($result, $data);
            }
        }

        return $result;
    }

    protected function listAckItSpec()
    {
        $TASK_STATUS = 'Acknowledge';
        $result = array();

        $list = self::crmSlaService()->getDashboardCRMITSpec();

        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->taskStatus == $TASK_STATUS) {
                $data = [
                    'no' => ++$counter,
                    'case_number' => $value->caseNumber,
                    'sub_category' => $value->subCategory,
                    'case_name' => $value->caseName,
                    'task_status' => $value->taskStatus,
                    'date_start' => $datestart,
                    'date_due' => $datedue,
                    'acknowledge_time' => $acknowledgeTime
                ];

                array_push($result, $data);
            }
        }

        return $result;
    }

    protected function listAckItSpecExceed()
    {
        $TASK_STATUS = 'Acknowledge';
        $result = array();

        $list = self::crmSlaService()->getDashboardCRMITSpec();

        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->taskStatus == $TASK_STATUS) {
                $data = [
                    'no' => ++$counter,
                    'case_number' => $value->caseNumber,
                    'sub_category' => $value->subCategory,
                    'case_name' => $value->caseName,
                    'task_status' => $value->taskStatus,
                    'date_start' => $datestart,
                    'date_due' => $datedue,
                    'acknowledge_time' => $acknowledgeTime
                ];

                array_push($result, $data);
            }
        }

        return $result;
    }
}
