<?php

namespace App\Http\Controllers;

use App\ActionLog;
use App\Http\Resources\SlaReportResource;
use App\SlaReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\User;
use Illuminate\Support\Facades\Response;

class ReportController extends Controller
{
        /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Get reports
        $reports = SlaReport::where('deleted', 0)
        ->where('hidden', 0)
        ->orderBy('created_at', 'desc')
        ->get();

        // Return collection of reports as a resource
        return SlaReportResource::collection($reports);
    }

    /**
     * Display a listing of hidden reports.
     *
     * @return \Illuminate\Http\Response
     */
    public function indexAdmin()
    {
        // Get hidden reports
        $reports = SlaReport::where('deleted', 0)
        ->orderBy('created_at', 'desc')
        ->get();

        // Return collection of reports as a resource
        return SlaReportResource::collection($reports);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $report = $request->isMethod('put') ? SlaReport::findOrFail() : new SlaReport;

        // $report->id = $request->input('report_id');
        $report->file_name = $request->input('file_name');
        $report->status = 0;
        $report->created_at = Carbon::now();
        $report->created_by = $request->input('created_by');

        if($report->save()) {
            return new SlaReportResource($report);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function addToQueue(Request $request)
    {
        try {
            $report = $request->isMethod('put') ? SlaReport::findOrFail() : new SlaReport;

            $reportName = 'SLA Report ' . date("F", mktime(0, 0, 0, $request->input('month'), 1)) . ' ' . $request->input('year');

            $user = User::find($request->input('user_id'));

            $report->name = $reportName;
            $report->status = 'queue';
            $report->parameter = json_encode([
                'month' => $request->input('month'),
                'year' => $request->input('year')
            ]);
            $report->created_at = Carbon::now();
            $report->created_by = $user->name;

            if ($request->has('is_hidden') && $request->input('is_hidden')) {
                $report->hidden = 1;
            }

            if ($report->save()) {
                ActionLog::saveActionLog('generateSlaReport', $report, 'success', $user->id); // Update action log with success status
                return response()->json(['success' => true, 'message' => 'Report added to queue.']);
            } else {
                ActionLog::saveActionLog('generateSlaReport', $report, 'failure', $user->id); // Update action log with failure status
                return response()->json(['success' => false, 'message' => 'Failed to add report to queue.'], 500);
            }
        } catch (\Exception $e) {
            ActionLog::saveActionLog('generateSlaReport', $report, 'failure', $user->id); // Update action log with failure status
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Update the deleted field of the report with the given ID to 1.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function delete(Request $request)
    {
        try {
            $report = SlaReport::findOrFail($request->id);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['success' => false, 'message' => 'Report not found.'], 404);
        }

        $report->deleted = 1;

        if ($report->save()) {
            return response()->json(['success' => true, 'message' => 'Report deleted successfully.'], 200);
        } else {
            return response()->json(['success' => false, 'message' => 'Failed to delete report.'], 500);
        }
    }

    public function download(Request $request)
    {
        $filename = $request->input('file_name');
        $reportName = $request->input('report_name');
        $user = User::find($request->input('user_id'));

        $reportData = [
            'report_name' => $reportName,
            'file_name' => $filename
        ];

        $file_path = storage_path() . '/report/' . $filename . '.pdf';
        if (file_exists($file_path)) {
            ActionLog::saveActionLog('downloadReport', $reportData, 'success', $user->id);
            return Response::download($file_path, $filename, [
                'Content-Length: ' . filesize($file_path)
            ]);
        } else {
            ActionLog::saveActionLog('downloadReport', $reportData, 'failure', $user->id);
            exit('Requested file does not exist on our server!');
        }
    }

    /**
     * Get log data for a specific report (admin only).
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function getLog($id)
    {
        try {
            $report = SlaReport::findOrFail($id);

            return response()->json([
                'success' => true,
                'log' => $report->log,
                'report_name' => $report->name,
                'status' => $report->status
            ], 200);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['success' => false, 'message' => 'Report not found.'], 404);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to retrieve log.'], 500);
        }
    }
}
