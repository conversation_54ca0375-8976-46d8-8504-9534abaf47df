<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class ActionLog extends Model
{
    protected $connection = 'mysql';
    protected $primaryKey = 'id';
    protected $table = 'action_log';
    public $timestamps = false;

    protected $fillable = [
        'action',
        'action_data',
        'action_status',
        'created_by',
        'created_at',
    ];

    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
            'action_data' => 'array',
        ];
    }

    public static function saveActionLog($actionName, $actionData, $status, $userid = null)
    {
        $username = null;
        $user = ($userid == null) ? Auth::user() : User::find($userid);
        if ($user != null) {
            $username = $user->username ?? $user->name;
        }

        $actionLog = ActionLog::create([
            'action' => $actionName,
            'action_data' => json_encode($actionData),
            'action_status' => $status,
            'created_by' => $username,
            'created_at' => now(),
        ]);

        return $actionLog;
    }
}
