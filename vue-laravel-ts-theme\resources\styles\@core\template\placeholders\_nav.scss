// ℹ️ This is common style that needs to be applied to both navs
%nav {
  color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));

  .nav-item-title {
    letter-spacing: normal;
  }
}

/*
    Active nav link styles for horizontal & vertical nav

    For horizontal nav it will be only applied to top level nav items
    For vertical nav it will be only applied to nav links (not nav groups)
*/
%nav-link-active {
  background: linear-gradient(270deg, rgba(var(--v-global-theme-primary), 0.7) 0%, rgb(var(--v-global-theme-primary)) 100%) !important;
  box-shadow: 0 2px 6px rgba(var(--v-global-theme-primary), 0.3);

  i {
    color: rgb(var(--v-theme-on-primary)) !important;
  }

  html[dir="rtl"] &.router-link-exact-active {
    background: linear-gradient(-270deg, rgba(var(--v-global-theme-primary), 0.7) 0%, rgb(var(--v-global-theme-primary)) 100%) !important;
  }
}
