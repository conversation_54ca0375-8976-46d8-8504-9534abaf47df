<?php

namespace App\Http\Resources;

use App\User;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class ParameterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            'id' => $this->id,
            'page' => $this->page,
            'parameter_code' => $this->parameter_code,
            'parameter_name' => $this->parameter_name,
            'parameter_desc' => $this->parameter_desc,
            'parameter_value' => $this->parameter_value,
            'modified_by' => $this->modified_by,
            'modified_by_name' => User::find($this->modified_by)->name,
            'modified_date' => $this->modified_date,
            'modified_date_format' => Carbon::parse($this->modified_date)->format('d/m/Y h:i A')
        ];
    }

    public function with($request)
    {
        return [
            'version' => '1.0.0',
            'author_url' => 'http://www.commercedc.com.my/'
        ];
    }
}
