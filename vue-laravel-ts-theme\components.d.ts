/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddAuthenticatorAppDialog: typeof import('./resources/ts/components/dialogs/AddAuthenticatorAppDialog.vue')['default']
    AddEditAddressDialog: typeof import('./resources/ts/components/dialogs/AddEditAddressDialog.vue')['default']
    AddEditPermissionDialog: typeof import('./resources/ts/components/dialogs/AddEditPermissionDialog.vue')['default']
    AddEditRoleDialog: typeof import('./resources/ts/components/dialogs/AddEditRoleDialog.vue')['default']
    AddPaymentMethodDialog: typeof import('./resources/ts/components/dialogs/AddPaymentMethodDialog.vue')['default']
    AppAutocomplete: typeof import('./resources/ts/@core/components/app-form-elements/AppAutocomplete.vue')['default']
    AppBarSearch: typeof import('./resources/ts/@core/components/AppBarSearch.vue')['default']
    AppCardActions: typeof import('./resources/ts/@core/components/cards/AppCardActions.vue')['default']
    AppCardCode: typeof import('./resources/ts/@core/components/cards/AppCardCode.vue')['default']
    AppCombobox: typeof import('./resources/ts/@core/components/app-form-elements/AppCombobox.vue')['default']
    AppDateTimePicker: typeof import('./resources/ts/@core/components/app-form-elements/AppDateTimePicker.vue')['default']
    AppDrawerHeaderSection: typeof import('./resources/ts/@core/components/AppDrawerHeaderSection.vue')['default']
    AppLoadingIndicator: typeof import('./resources/ts/components/AppLoadingIndicator.vue')['default']
    AppPricing: typeof import('./resources/ts/components/AppPricing.vue')['default']
    AppSearchHeader: typeof import('./resources/ts/components/AppSearchHeader.vue')['default']
    AppSelect: typeof import('./resources/ts/@core/components/app-form-elements/AppSelect.vue')['default']
    AppStepper: typeof import('./resources/ts/@core/components/AppStepper.vue')['default']
    AppTextarea: typeof import('./resources/ts/@core/components/app-form-elements/AppTextarea.vue')['default']
    AppTextField: typeof import('./resources/ts/@core/components/app-form-elements/AppTextField.vue')['default']
    BuyNow: typeof import('./resources/ts/@core/components/BuyNow.vue')['default']
    CardAddEditDialog: typeof import('./resources/ts/components/dialogs/CardAddEditDialog.vue')['default']
    CardStatisticsHorizontal: typeof import('./resources/ts/@core/components/cards/CardStatisticsHorizontal.vue')['default']
    CardStatisticsVertical: typeof import('./resources/ts/@core/components/cards/CardStatisticsVertical.vue')['default']
    CardStatisticsVerticalSimple: typeof import('./resources/ts/@core/components/CardStatisticsVerticalSimple.vue')['default']
    ConfirmDialog: typeof import('./resources/ts/components/dialogs/ConfirmDialog.vue')['default']
    CreateAppDialog: typeof import('./resources/ts/components/dialogs/CreateAppDialog.vue')['default']
    CustomCheckboxes: typeof import('./resources/ts/@core/components/app-form-elements/CustomCheckboxes.vue')['default']
    CustomCheckboxesWithIcon: typeof import('./resources/ts/@core/components/app-form-elements/CustomCheckboxesWithIcon.vue')['default']
    CustomCheckboxesWithImage: typeof import('./resources/ts/@core/components/app-form-elements/CustomCheckboxesWithImage.vue')['default']
    CustomizerSection: typeof import('./resources/ts/@core/components/CustomizerSection.vue')['default']
    CustomRadios: typeof import('./resources/ts/@core/components/app-form-elements/CustomRadios.vue')['default']
    CustomRadiosWithIcon: typeof import('./resources/ts/@core/components/app-form-elements/CustomRadiosWithIcon.vue')['default']
    CustomRadiosWithImage: typeof import('./resources/ts/@core/components/app-form-elements/CustomRadiosWithImage.vue')['default']
    DemoAlertBasic: typeof import('./resources/ts/views/demos/components/alert/DemoAlertBasic.vue')['default']
    DemoAlertBorder: typeof import('./resources/ts/views/demos/components/alert/DemoAlertBorder.vue')['default']
    DemoAlertClosable: typeof import('./resources/ts/views/demos/components/alert/DemoAlertClosable.vue')['default']
    DemoAlertColoredBorder: typeof import('./resources/ts/views/demos/components/alert/DemoAlertColoredBorder.vue')['default']
    DemoAlertColors: typeof import('./resources/ts/views/demos/components/alert/DemoAlertColors.vue')['default']
    DemoAlertDensity: typeof import('./resources/ts/views/demos/components/alert/DemoAlertDensity.vue')['default']
    DemoAlertElevation: typeof import('./resources/ts/views/demos/components/alert/DemoAlertElevation.vue')['default']
    DemoAlertIcons: typeof import('./resources/ts/views/demos/components/alert/DemoAlertIcons.vue')['default']
    DemoAlertOutlined: typeof import('./resources/ts/views/demos/components/alert/DemoAlertOutlined.vue')['default']
    DemoAlertProminent: typeof import('./resources/ts/views/demos/components/alert/DemoAlertProminent.vue')['default']
    DemoAlertTonal: typeof import('./resources/ts/views/demos/components/alert/DemoAlertTonal.vue')['default']
    DemoAlertType: typeof import('./resources/ts/views/demos/components/alert/DemoAlertType.vue')['default']
    DemoAlertVModelSupport: typeof import('./resources/ts/views/demos/components/alert/DemoAlertVModelSupport.vue')['default']
    DemoAutocompleteAsyncItems: typeof import('./resources/ts/views/demos/forms/form-elements/autocomplete/DemoAutocompleteAsyncItems.vue')['default']
    DemoAutocompleteBasic: typeof import('./resources/ts/views/demos/forms/form-elements/autocomplete/DemoAutocompleteBasic.vue')['default']
    DemoAutocompleteChips: typeof import('./resources/ts/views/demos/forms/form-elements/autocomplete/DemoAutocompleteChips.vue')['default']
    DemoAutocompleteClearable: typeof import('./resources/ts/views/demos/forms/form-elements/autocomplete/DemoAutocompleteClearable.vue')['default']
    DemoAutocompleteCustomFilter: typeof import('./resources/ts/views/demos/forms/form-elements/autocomplete/DemoAutocompleteCustomFilter.vue')['default']
    DemoAutocompleteDensity: typeof import('./resources/ts/views/demos/forms/form-elements/autocomplete/DemoAutocompleteDensity.vue')['default']
    DemoAutocompleteMultiple: typeof import('./resources/ts/views/demos/forms/form-elements/autocomplete/DemoAutocompleteMultiple.vue')['default']
    DemoAutocompleteSlots: typeof import('./resources/ts/views/demos/forms/form-elements/autocomplete/DemoAutocompleteSlots.vue')['default']
    DemoAutocompleteStateSelector: typeof import('./resources/ts/views/demos/forms/form-elements/autocomplete/DemoAutocompleteStateSelector.vue')['default']
    DemoAutocompleteValidation: typeof import('./resources/ts/views/demos/forms/form-elements/autocomplete/DemoAutocompleteValidation.vue')['default']
    DemoAutocompleteVariant: typeof import('./resources/ts/views/demos/forms/form-elements/autocomplete/DemoAutocompleteVariant.vue')['default']
    DemoAvatarColors: typeof import('./resources/ts/views/demos/components/avatar/DemoAvatarColors.vue')['default']
    DemoAvatarGroup: typeof import('./resources/ts/views/demos/components/avatar/DemoAvatarGroup.vue')['default']
    DemoAvatarIcons: typeof import('./resources/ts/views/demos/components/avatar/DemoAvatarIcons.vue')['default']
    DemoAvatarImages: typeof import('./resources/ts/views/demos/components/avatar/DemoAvatarImages.vue')['default']
    DemoAvatarRounded: typeof import('./resources/ts/views/demos/components/avatar/DemoAvatarRounded.vue')['default']
    DemoAvatarSizes: typeof import('./resources/ts/views/demos/components/avatar/DemoAvatarSizes.vue')['default']
    DemoAvatarTonal: typeof import('./resources/ts/views/demos/components/avatar/DemoAvatarTonal.vue')['default']
    DemoBadgeAvatarStatus: typeof import('./resources/ts/views/demos/components/badge/DemoBadgeAvatarStatus.vue')['default']
    DemoBadgeColor: typeof import('./resources/ts/views/demos/components/badge/DemoBadgeColor.vue')['default']
    DemoBadgeDynamicNotifications: typeof import('./resources/ts/views/demos/components/badge/DemoBadgeDynamicNotifications.vue')['default']
    DemoBadgeIcon: typeof import('./resources/ts/views/demos/components/badge/DemoBadgeIcon.vue')['default']
    DemoBadgeMaximumValue: typeof import('./resources/ts/views/demos/components/badge/DemoBadgeMaximumValue.vue')['default']
    DemoBadgePosition: typeof import('./resources/ts/views/demos/components/badge/DemoBadgePosition.vue')['default']
    DemoBadgeShowOnHover: typeof import('./resources/ts/views/demos/components/badge/DemoBadgeShowOnHover.vue')['default']
    DemoBadgeStyle: typeof import('./resources/ts/views/demos/components/badge/DemoBadgeStyle.vue')['default']
    DemoBadgeTabs: typeof import('./resources/ts/views/demos/components/badge/DemoBadgeTabs.vue')['default']
    DemoBadgeTonal: typeof import('./resources/ts/views/demos/components/badge/DemoBadgeTonal.vue')['default']
    DemoButtonBlock: typeof import('./resources/ts/views/demos/components/button/DemoButtonBlock.vue')['default']
    DemoButtonColors: typeof import('./resources/ts/views/demos/components/button/DemoButtonColors.vue')['default']
    DemoButtonFlat: typeof import('./resources/ts/views/demos/components/button/DemoButtonFlat.vue')['default']
    DemoButtonGroup: typeof import('./resources/ts/views/demos/components/button/DemoButtonGroup.vue')['default']
    DemoButtonIcon: typeof import('./resources/ts/views/demos/components/button/DemoButtonIcon.vue')['default']
    DemoButtonIconOnly: typeof import('./resources/ts/views/demos/components/button/DemoButtonIconOnly.vue')['default']
    DemoButtonLink: typeof import('./resources/ts/views/demos/components/button/DemoButtonLink.vue')['default']
    DemoButtonLoaders: typeof import('./resources/ts/views/demos/components/button/DemoButtonLoaders.vue')['default']
    DemoButtonOutlined: typeof import('./resources/ts/views/demos/components/button/DemoButtonOutlined.vue')['default']
    DemoButtonPlain: typeof import('./resources/ts/views/demos/components/button/DemoButtonPlain.vue')['default']
    DemoButtonRounded: typeof import('./resources/ts/views/demos/components/button/DemoButtonRounded.vue')['default']
    DemoButtonRouter: typeof import('./resources/ts/views/demos/components/button/DemoButtonRouter.vue')['default']
    DemoButtonSizing: typeof import('./resources/ts/views/demos/components/button/DemoButtonSizing.vue')['default']
    DemoButtonText: typeof import('./resources/ts/views/demos/components/button/DemoButtonText.vue')['default']
    DemoButtonTonal: typeof import('./resources/ts/views/demos/components/button/DemoButtonTonal.vue')['default']
    DemoCheckboxBasic: typeof import('./resources/ts/views/demos/forms/form-elements/checkbox/DemoCheckboxBasic.vue')['default']
    DemoCheckboxCheckboxValue: typeof import('./resources/ts/views/demos/forms/form-elements/checkbox/DemoCheckboxCheckboxValue.vue')['default']
    DemoCheckboxColors: typeof import('./resources/ts/views/demos/forms/form-elements/checkbox/DemoCheckboxColors.vue')['default']
    DemoCheckboxDensity: typeof import('./resources/ts/views/demos/forms/form-elements/checkbox/DemoCheckboxDensity.vue')['default']
    DemoCheckboxIcon: typeof import('./resources/ts/views/demos/forms/form-elements/checkbox/DemoCheckboxIcon.vue')['default']
    DemoCheckboxInlineTextField: typeof import('./resources/ts/views/demos/forms/form-elements/checkbox/DemoCheckboxInlineTextField.vue')['default']
    DemoCheckboxLabelSlot: typeof import('./resources/ts/views/demos/forms/form-elements/checkbox/DemoCheckboxLabelSlot.vue')['default']
    DemoCheckboxModelAsArray: typeof import('./resources/ts/views/demos/forms/form-elements/checkbox/DemoCheckboxModelAsArray.vue')['default']
    DemoCheckboxStates: typeof import('./resources/ts/views/demos/forms/form-elements/checkbox/DemoCheckboxStates.vue')['default']
    DemoChipClosable: typeof import('./resources/ts/views/demos/components/chip/DemoChipClosable.vue')['default']
    DemoChipColor: typeof import('./resources/ts/views/demos/components/chip/DemoChipColor.vue')['default']
    DemoChipElevated: typeof import('./resources/ts/views/demos/components/chip/DemoChipElevated.vue')['default']
    DemoChipExpandable: typeof import('./resources/ts/views/demos/components/chip/DemoChipExpandable.vue')['default']
    DemoChipInSelects: typeof import('./resources/ts/views/demos/components/chip/DemoChipInSelects.vue')['default']
    DemoChipOutlined: typeof import('./resources/ts/views/demos/components/chip/DemoChipOutlined.vue')['default']
    DemoChipRounded: typeof import('./resources/ts/views/demos/components/chip/DemoChipRounded.vue')['default']
    DemoChipSizes: typeof import('./resources/ts/views/demos/components/chip/DemoChipSizes.vue')['default']
    DemoChipWithAvatar: typeof import('./resources/ts/views/demos/components/chip/DemoChipWithAvatar.vue')['default']
    DemoChipWithIcon: typeof import('./resources/ts/views/demos/components/chip/DemoChipWithIcon.vue')['default']
    DemoComboboxBasic: typeof import('./resources/ts/views/demos/forms/form-elements/combobox/DemoComboboxBasic.vue')['default']
    DemoComboboxClearable: typeof import('./resources/ts/views/demos/forms/form-elements/combobox/DemoComboboxClearable.vue')['default']
    DemoComboboxDensity: typeof import('./resources/ts/views/demos/forms/form-elements/combobox/DemoComboboxDensity.vue')['default']
    DemoComboboxMultiple: typeof import('./resources/ts/views/demos/forms/form-elements/combobox/DemoComboboxMultiple.vue')['default']
    DemoComboboxNoDataWithChips: typeof import('./resources/ts/views/demos/forms/form-elements/combobox/DemoComboboxNoDataWithChips.vue')['default']
    DemoComboboxVariant: typeof import('./resources/ts/views/demos/forms/form-elements/combobox/DemoComboboxVariant.vue')['default']
    DemoCustomInputCustomCheckboxes: typeof import('./resources/ts/views/demos/forms/form-elements/custom-input/DemoCustomInputCustomCheckboxes.vue')['default']
    DemoCustomInputCustomCheckboxesWithIcon: typeof import('./resources/ts/views/demos/forms/form-elements/custom-input/DemoCustomInputCustomCheckboxesWithIcon.vue')['default']
    DemoCustomInputCustomCheckboxesWithImage: typeof import('./resources/ts/views/demos/forms/form-elements/custom-input/DemoCustomInputCustomCheckboxesWithImage.vue')['default']
    DemoCustomInputCustomRadios: typeof import('./resources/ts/views/demos/forms/form-elements/custom-input/DemoCustomInputCustomRadios.vue')['default']
    DemoCustomInputCustomRadiosWithIcon: typeof import('./resources/ts/views/demos/forms/form-elements/custom-input/DemoCustomInputCustomRadiosWithIcon.vue')['default']
    DemoCustomInputCustomRadiosWithImage: typeof import('./resources/ts/views/demos/forms/form-elements/custom-input/DemoCustomInputCustomRadiosWithImage.vue')['default']
    DemoDataTableBasic: typeof import('./resources/ts/views/demos/forms/tables/data-table/DemoDataTableBasic.vue')['default']
    DemoDataTableCellSlot: typeof import('./resources/ts/views/demos/forms/tables/data-table/DemoDataTableCellSlot.vue')['default']
    DemoDataTableDense: typeof import('./resources/ts/views/demos/forms/tables/data-table/DemoDataTableDense.vue')['default']
    DemoDataTableExpandableRows: typeof import('./resources/ts/views/demos/forms/tables/data-table/DemoDataTableExpandableRows.vue')['default']
    DemoDataTableExternalPagination: typeof import('./resources/ts/views/demos/forms/tables/data-table/DemoDataTableExternalPagination.vue')['default']
    DemoDataTableFixedHeader: typeof import('./resources/ts/views/demos/forms/tables/data-table/DemoDataTableFixedHeader.vue')['default']
    DemoDataTableGroupingRows: typeof import('./resources/ts/views/demos/forms/tables/data-table/DemoDataTableGroupingRows.vue')['default']
    DemoDataTableKitchenSink: typeof import('./resources/ts/views/demos/forms/tables/data-table/DemoDataTableKitchenSink.vue')['default']
    DemoDataTableRowEditingViaDialog: typeof import('./resources/ts/views/demos/forms/tables/data-table/DemoDataTableRowEditingViaDialog.vue')['default']
    DemoDataTableRowSelection: typeof import('./resources/ts/views/demos/forms/tables/data-table/DemoDataTableRowSelection.vue')['default']
    DemoDateTimePickerBasic: typeof import('./resources/ts/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerBasic.vue')['default']
    DemoDateTimePickerDateAndTime: typeof import('./resources/ts/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerDateAndTime.vue')['default']
    DemoDateTimePickerDisabledRange: typeof import('./resources/ts/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerDisabledRange.vue')['default']
    DemoDateTimePickerHumanFriendly: typeof import('./resources/ts/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerHumanFriendly.vue')['default']
    DemoDateTimePickerInline: typeof import('./resources/ts/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerInline.vue')['default']
    DemoDateTimePickerMultipleDates: typeof import('./resources/ts/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerMultipleDates.vue')['default']
    DemoDateTimePickerRange: typeof import('./resources/ts/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerRange.vue')['default']
    DemoDateTimePickerTimePicker: typeof import('./resources/ts/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerTimePicker.vue')['default']
    DemoDialogBasic: typeof import('./resources/ts/views/demos/components/dialog/DemoDialogBasic.vue')['default']
    DemoDialogForm: typeof import('./resources/ts/views/demos/components/dialog/DemoDialogForm.vue')['default']
    DemoDialogFullscreen: typeof import('./resources/ts/views/demos/components/dialog/DemoDialogFullscreen.vue')['default']
    DemoDialogLoader: typeof import('./resources/ts/views/demos/components/dialog/DemoDialogLoader.vue')['default']
    DemoDialogNesting: typeof import('./resources/ts/views/demos/components/dialog/DemoDialogNesting.vue')['default']
    DemoDialogOverflowed: typeof import('./resources/ts/views/demos/components/dialog/DemoDialogOverflowed.vue')['default']
    DemoDialogPersistent: typeof import('./resources/ts/views/demos/components/dialog/DemoDialogPersistent.vue')['default']
    DemoDialogScrollable: typeof import('./resources/ts/views/demos/components/dialog/DemoDialogScrollable.vue')['default']
    DemoEditorBasicEditor: typeof import('./resources/ts/views/demos/forms/form-elements/editor/DemoEditorBasicEditor.vue')['default']
    DemoEditorCustomEditor: typeof import('./resources/ts/views/demos/forms/form-elements/editor/DemoEditorCustomEditor.vue')['default']
    DemoExpansionPanelAccordion: typeof import('./resources/ts/views/demos/components/expansion-panel/DemoExpansionPanelAccordion.vue')['default']
    DemoExpansionPanelBasic: typeof import('./resources/ts/views/demos/components/expansion-panel/DemoExpansionPanelBasic.vue')['default']
    DemoExpansionPanelCustomIcon: typeof import('./resources/ts/views/demos/components/expansion-panel/DemoExpansionPanelCustomIcon.vue')['default']
    DemoExpansionPanelInset: typeof import('./resources/ts/views/demos/components/expansion-panel/DemoExpansionPanelInset.vue')['default']
    DemoExpansionPanelModel: typeof import('./resources/ts/views/demos/components/expansion-panel/DemoExpansionPanelModel.vue')['default']
    DemoExpansionPanelPopout: typeof import('./resources/ts/views/demos/components/expansion-panel/DemoExpansionPanelPopout.vue')['default']
    DemoExpansionPanelWithBorder: typeof import('./resources/ts/views/demos/components/expansion-panel/DemoExpansionPanelWithBorder.vue')['default']
    DemoFileInputAccept: typeof import('./resources/ts/views/demos/forms/form-elements/file-input/DemoFileInputAccept.vue')['default']
    DemoFileInputBasic: typeof import('./resources/ts/views/demos/forms/form-elements/file-input/DemoFileInputBasic.vue')['default']
    DemoFileInputChips: typeof import('./resources/ts/views/demos/forms/form-elements/file-input/DemoFileInputChips.vue')['default']
    DemoFileInputCounter: typeof import('./resources/ts/views/demos/forms/form-elements/file-input/DemoFileInputCounter.vue')['default']
    DemoFileInputDensity: typeof import('./resources/ts/views/demos/forms/form-elements/file-input/DemoFileInputDensity.vue')['default']
    DemoFileInputLoading: typeof import('./resources/ts/views/demos/forms/form-elements/file-input/DemoFileInputLoading.vue')['default']
    DemoFileInputMultiple: typeof import('./resources/ts/views/demos/forms/form-elements/file-input/DemoFileInputMultiple.vue')['default']
    DemoFileInputPrependIcon: typeof import('./resources/ts/views/demos/forms/form-elements/file-input/DemoFileInputPrependIcon.vue')['default']
    DemoFileInputSelectionSlot: typeof import('./resources/ts/views/demos/forms/form-elements/file-input/DemoFileInputSelectionSlot.vue')['default']
    DemoFileInputShowSize: typeof import('./resources/ts/views/demos/forms/form-elements/file-input/DemoFileInputShowSize.vue')['default']
    DemoFileInputValidation: typeof import('./resources/ts/views/demos/forms/form-elements/file-input/DemoFileInputValidation.vue')['default']
    DemoFileInputVariant: typeof import('./resources/ts/views/demos/forms/form-elements/file-input/DemoFileInputVariant.vue')['default']
    DemoFormLayoutCollapsible: typeof import('./resources/ts/views/demos/forms/form-layout/DemoFormLayoutCollapsible.vue')['default']
    DemoFormLayoutFormHint: typeof import('./resources/ts/views/demos/forms/form-layout/DemoFormLayoutFormHint.vue')['default']
    DemoFormLayoutFormSticky: typeof import('./resources/ts/views/demos/forms/form-layout/DemoFormLayoutFormSticky.vue')['default']
    DemoFormLayoutFormValidation: typeof import('./resources/ts/views/demos/forms/form-layout/DemoFormLayoutFormValidation.vue')['default']
    DemoFormLayoutFormWithTabs: typeof import('./resources/ts/views/demos/forms/form-layout/DemoFormLayoutFormWithTabs.vue')['default']
    DemoFormLayoutHorizontalForm: typeof import('./resources/ts/views/demos/forms/form-layout/DemoFormLayoutHorizontalForm.vue')['default']
    DemoFormLayoutHorizontalFormWithIcons: typeof import('./resources/ts/views/demos/forms/form-layout/DemoFormLayoutHorizontalFormWithIcons.vue')['default']
    DemoFormLayoutMultipleColumn: typeof import('./resources/ts/views/demos/forms/form-layout/DemoFormLayoutMultipleColumn.vue')['default']
    DemoFormLayoutSticky: typeof import('./resources/ts/views/demos/forms/form-layout/DemoFormLayoutSticky.vue')['default']
    DemoFormLayoutVerticalForm: typeof import('./resources/ts/views/demos/forms/form-layout/DemoFormLayoutVerticalForm.vue')['default']
    DemoFormLayoutVerticalFormWithIcons: typeof import('./resources/ts/views/demos/forms/form-layout/DemoFormLayoutVerticalFormWithIcons.vue')['default']
    DemoFormValidationSimpleFormValidation: typeof import('./resources/ts/views/demos/forms/form-validation/DemoFormValidationSimpleFormValidation.vue')['default']
    DemoFormValidationValidatingMultipleRules: typeof import('./resources/ts/views/demos/forms/form-validation/DemoFormValidationValidatingMultipleRules.vue')['default']
    DemoFormValidationValidationTypes: typeof import('./resources/ts/views/demos/forms/form-validation/DemoFormValidationValidationTypes.vue')['default']
    DemoFormWizardIconsBasic: typeof import('./resources/ts/views/demos/forms/form-wizard/form-wizard-icons/DemoFormWizardIconsBasic.vue')['default']
    DemoFormWizardIconsModernBasic: typeof import('./resources/ts/views/demos/forms/form-wizard/form-wizard-icons/DemoFormWizardIconsModernBasic.vue')['default']
    DemoFormWizardIconsModernVertical: typeof import('./resources/ts/views/demos/forms/form-wizard/form-wizard-icons/DemoFormWizardIconsModernVertical.vue')['default']
    DemoFormWizardIconsValidation: typeof import('./resources/ts/views/demos/forms/form-wizard/form-wizard-icons/DemoFormWizardIconsValidation.vue')['default']
    DemoFormWizardIconsVertical: typeof import('./resources/ts/views/demos/forms/form-wizard/form-wizard-icons/DemoFormWizardIconsVertical.vue')['default']
    DemoFormWizardNumberedBasic: typeof import('./resources/ts/views/demos/forms/form-wizard/form-wizard-numbered/DemoFormWizardNumberedBasic.vue')['default']
    DemoFormWizardNumberedModernBasic: typeof import('./resources/ts/views/demos/forms/form-wizard/form-wizard-numbered/DemoFormWizardNumberedModernBasic.vue')['default']
    DemoFormWizardNumberedModernVertical: typeof import('./resources/ts/views/demos/forms/form-wizard/form-wizard-numbered/DemoFormWizardNumberedModernVertical.vue')['default']
    DemoFormWizardNumberedValidation: typeof import('./resources/ts/views/demos/forms/form-wizard/form-wizard-numbered/DemoFormWizardNumberedValidation.vue')['default']
    DemoFormWizardNumberedVertical: typeof import('./resources/ts/views/demos/forms/form-wizard/form-wizard-numbered/DemoFormWizardNumberedVertical.vue')['default']
    DemoListActionAndItemGroup: typeof import('./resources/ts/views/demos/components/list/DemoListActionAndItemGroup.vue')['default']
    DemoListBasic: typeof import('./resources/ts/views/demos/components/list/DemoListBasic.vue')['default']
    DemoListDensity: typeof import('./resources/ts/views/demos/components/list/DemoListDensity.vue')['default']
    DemoListNav: typeof import('./resources/ts/views/demos/components/list/DemoListNav.vue')['default']
    DemoListProgressList: typeof import('./resources/ts/views/demos/components/list/DemoListProgressList.vue')['default']
    DemoListRounded: typeof import('./resources/ts/views/demos/components/list/DemoListRounded.vue')['default']
    DemoListShaped: typeof import('./resources/ts/views/demos/components/list/DemoListShaped.vue')['default']
    DemoListSubGroup: typeof import('./resources/ts/views/demos/components/list/DemoListSubGroup.vue')['default']
    DemoListThreeLine: typeof import('./resources/ts/views/demos/components/list/DemoListThreeLine.vue')['default']
    DemoListTwoLinesAndSubheader: typeof import('./resources/ts/views/demos/components/list/DemoListTwoLinesAndSubheader.vue')['default']
    DemoListUserList: typeof import('./resources/ts/views/demos/components/list/DemoListUserList.vue')['default']
    DemoMenuActivatorAndTooltip: typeof import('./resources/ts/views/demos/components/menu/DemoMenuActivatorAndTooltip.vue')['default']
    DemoMenuBasic: typeof import('./resources/ts/views/demos/components/menu/DemoMenuBasic.vue')['default']
    DemoMenuCustomTransitions: typeof import('./resources/ts/views/demos/components/menu/DemoMenuCustomTransitions.vue')['default']
    DemoMenuLocation: typeof import('./resources/ts/views/demos/components/menu/DemoMenuLocation.vue')['default']
    DemoMenuOpenOnHover: typeof import('./resources/ts/views/demos/components/menu/DemoMenuOpenOnHover.vue')['default']
    DemoMenuPopover: typeof import('./resources/ts/views/demos/components/menu/DemoMenuPopover.vue')['default']
    DemoOtpInputBasic: typeof import('./resources/ts/views/demos/forms/form-elements/otp-input/DemoOtpInputBasic.vue')['default']
    DemoOtpInputFinish: typeof import('./resources/ts/views/demos/forms/form-elements/otp-input/DemoOtpInputFinish.vue')['default']
    DemoOtpInputHidden: typeof import('./resources/ts/views/demos/forms/form-elements/otp-input/DemoOtpInputHidden.vue')['default']
    DemoPaginationBasic: typeof import('./resources/ts/views/demos/components/pagination/DemoPaginationBasic.vue')['default']
    DemoPaginationCircle: typeof import('./resources/ts/views/demos/components/pagination/DemoPaginationCircle.vue')['default']
    DemoPaginationColor: typeof import('./resources/ts/views/demos/components/pagination/DemoPaginationColor.vue')['default']
    DemoPaginationDisabled: typeof import('./resources/ts/views/demos/components/pagination/DemoPaginationDisabled.vue')['default']
    DemoPaginationIcons: typeof import('./resources/ts/views/demos/components/pagination/DemoPaginationIcons.vue')['default']
    DemoPaginationLength: typeof import('./resources/ts/views/demos/components/pagination/DemoPaginationLength.vue')['default']
    DemoPaginationOutline: typeof import('./resources/ts/views/demos/components/pagination/DemoPaginationOutline.vue')['default']
    DemoPaginationOutlineCircle: typeof import('./resources/ts/views/demos/components/pagination/DemoPaginationOutlineCircle.vue')['default']
    DemoPaginationSize: typeof import('./resources/ts/views/demos/components/pagination/DemoPaginationSize.vue')['default']
    DemoPaginationTotalVisible: typeof import('./resources/ts/views/demos/components/pagination/DemoPaginationTotalVisible.vue')['default']
    DemoProgressCircularColor: typeof import('./resources/ts/views/demos/components/progress-circular/DemoProgressCircularColor.vue')['default']
    DemoProgressCircularIndeterminate: typeof import('./resources/ts/views/demos/components/progress-circular/DemoProgressCircularIndeterminate.vue')['default']
    DemoProgressCircularRotate: typeof import('./resources/ts/views/demos/components/progress-circular/DemoProgressCircularRotate.vue')['default']
    DemoProgressCircularSize: typeof import('./resources/ts/views/demos/components/progress-circular/DemoProgressCircularSize.vue')['default']
    DemoProgressLinearBuffering: typeof import('./resources/ts/views/demos/components/progress-linear/DemoProgressLinearBuffering.vue')['default']
    DemoProgressLinearColor: typeof import('./resources/ts/views/demos/components/progress-linear/DemoProgressLinearColor.vue')['default']
    DemoProgressLinearIndeterminate: typeof import('./resources/ts/views/demos/components/progress-linear/DemoProgressLinearIndeterminate.vue')['default']
    DemoProgressLinearReversed: typeof import('./resources/ts/views/demos/components/progress-linear/DemoProgressLinearReversed.vue')['default']
    DemoProgressLinearRounded: typeof import('./resources/ts/views/demos/components/progress-linear/DemoProgressLinearRounded.vue')['default']
    DemoProgressLinearSlots: typeof import('./resources/ts/views/demos/components/progress-linear/DemoProgressLinearSlots.vue')['default']
    DemoProgressLinearStriped: typeof import('./resources/ts/views/demos/components/progress-linear/DemoProgressLinearStriped.vue')['default']
    DemoRadioBasic: typeof import('./resources/ts/views/demos/forms/form-elements/radio/DemoRadioBasic.vue')['default']
    DemoRadioColors: typeof import('./resources/ts/views/demos/forms/form-elements/radio/DemoRadioColors.vue')['default']
    DemoRadioDensity: typeof import('./resources/ts/views/demos/forms/form-elements/radio/DemoRadioDensity.vue')['default']
    DemoRadioIcon: typeof import('./resources/ts/views/demos/forms/form-elements/radio/DemoRadioIcon.vue')['default']
    DemoRadioInline: typeof import('./resources/ts/views/demos/forms/form-elements/radio/DemoRadioInline.vue')['default']
    DemoRadioLabelSlot: typeof import('./resources/ts/views/demos/forms/form-elements/radio/DemoRadioLabelSlot.vue')['default']
    DemoRadioValidation: typeof import('./resources/ts/views/demos/forms/form-elements/radio/DemoRadioValidation.vue')['default']
    DemoRangeSliderBasic: typeof import('./resources/ts/views/demos/forms/form-elements/range-slider/DemoRangeSliderBasic.vue')['default']
    DemoRangeSliderColor: typeof import('./resources/ts/views/demos/forms/form-elements/range-slider/DemoRangeSliderColor.vue')['default']
    DemoRangeSliderDisabled: typeof import('./resources/ts/views/demos/forms/form-elements/range-slider/DemoRangeSliderDisabled.vue')['default']
    DemoRangeSliderStep: typeof import('./resources/ts/views/demos/forms/form-elements/range-slider/DemoRangeSliderStep.vue')['default']
    DemoRangeSliderThumbLabel: typeof import('./resources/ts/views/demos/forms/form-elements/range-slider/DemoRangeSliderThumbLabel.vue')['default']
    DemoRangeSliderVertical: typeof import('./resources/ts/views/demos/forms/form-elements/range-slider/DemoRangeSliderVertical.vue')['default']
    DemoRatingBasic: typeof import('./resources/ts/views/demos/forms/form-elements/rating/DemoRatingBasic.vue')['default']
    DemoRatingClearable: typeof import('./resources/ts/views/demos/forms/form-elements/rating/DemoRatingClearable.vue')['default']
    DemoRatingColors: typeof import('./resources/ts/views/demos/forms/form-elements/rating/DemoRatingColors.vue')['default']
    DemoRatingDensity: typeof import('./resources/ts/views/demos/forms/form-elements/rating/DemoRatingDensity.vue')['default']
    DemoRatingHover: typeof import('./resources/ts/views/demos/forms/form-elements/rating/DemoRatingHover.vue')['default']
    DemoRatingIncremented: typeof import('./resources/ts/views/demos/forms/form-elements/rating/DemoRatingIncremented.vue')['default']
    DemoRatingItemSlot: typeof import('./resources/ts/views/demos/forms/form-elements/rating/DemoRatingItemSlot.vue')['default']
    DemoRatingLength: typeof import('./resources/ts/views/demos/forms/form-elements/rating/DemoRatingLength.vue')['default']
    DemoRatingReadonly: typeof import('./resources/ts/views/demos/forms/form-elements/rating/DemoRatingReadonly.vue')['default']
    DemoRatingSize: typeof import('./resources/ts/views/demos/forms/form-elements/rating/DemoRatingSize.vue')['default']
    DemoSelectBasic: typeof import('./resources/ts/views/demos/forms/form-elements/select/DemoSelectBasic.vue')['default']
    DemoSelectChips: typeof import('./resources/ts/views/demos/forms/form-elements/select/DemoSelectChips.vue')['default']
    DemoSelectCustomTextAndValue: typeof import('./resources/ts/views/demos/forms/form-elements/select/DemoSelectCustomTextAndValue.vue')['default']
    DemoSelectDensity: typeof import('./resources/ts/views/demos/forms/form-elements/select/DemoSelectDensity.vue')['default']
    DemoSelectIcons: typeof import('./resources/ts/views/demos/forms/form-elements/select/DemoSelectIcons.vue')['default']
    DemoSelectMenuProps: typeof import('./resources/ts/views/demos/forms/form-elements/select/DemoSelectMenuProps.vue')['default']
    DemoSelectMultiple: typeof import('./resources/ts/views/demos/forms/form-elements/select/DemoSelectMultiple.vue')['default']
    DemoSelectSelectionSlot: typeof import('./resources/ts/views/demos/forms/form-elements/select/DemoSelectSelectionSlot.vue')['default']
    DemoSelectVariant: typeof import('./resources/ts/views/demos/forms/form-elements/select/DemoSelectVariant.vue')['default']
    DemoSimpleTableBasic: typeof import('./resources/ts/views/demos/forms/tables/simple-table/DemoSimpleTableBasic.vue')['default']
    DemoSimpleTableDensity: typeof import('./resources/ts/views/demos/forms/tables/simple-table/DemoSimpleTableDensity.vue')['default']
    DemoSimpleTableFixedHeader: typeof import('./resources/ts/views/demos/forms/tables/simple-table/DemoSimpleTableFixedHeader.vue')['default']
    DemoSimpleTableHeight: typeof import('./resources/ts/views/demos/forms/tables/simple-table/DemoSimpleTableHeight.vue')['default']
    DemoSimpleTableTheme: typeof import('./resources/ts/views/demos/forms/tables/simple-table/DemoSimpleTableTheme.vue')['default']
    DemoSliderAppendAndPrepend: typeof import('./resources/ts/views/demos/forms/form-elements/slider/DemoSliderAppendAndPrepend.vue')['default']
    DemoSliderAppendTextField: typeof import('./resources/ts/views/demos/forms/form-elements/slider/DemoSliderAppendTextField.vue')['default']
    DemoSliderBasic: typeof import('./resources/ts/views/demos/forms/form-elements/slider/DemoSliderBasic.vue')['default']
    DemoSliderColors: typeof import('./resources/ts/views/demos/forms/form-elements/slider/DemoSliderColors.vue')['default']
    DemoSliderDisabledAndReadonly: typeof import('./resources/ts/views/demos/forms/form-elements/slider/DemoSliderDisabledAndReadonly.vue')['default']
    DemoSliderIcons: typeof import('./resources/ts/views/demos/forms/form-elements/slider/DemoSliderIcons.vue')['default']
    DemoSliderMinAndMax: typeof import('./resources/ts/views/demos/forms/form-elements/slider/DemoSliderMinAndMax.vue')['default']
    DemoSliderSize: typeof import('./resources/ts/views/demos/forms/form-elements/slider/DemoSliderSize.vue')['default']
    DemoSliderStep: typeof import('./resources/ts/views/demos/forms/form-elements/slider/DemoSliderStep.vue')['default']
    DemoSliderThumb: typeof import('./resources/ts/views/demos/forms/form-elements/slider/DemoSliderThumb.vue')['default']
    DemoSliderTicks: typeof import('./resources/ts/views/demos/forms/form-elements/slider/DemoSliderTicks.vue')['default']
    DemoSliderValidation: typeof import('./resources/ts/views/demos/forms/form-elements/slider/DemoSliderValidation.vue')['default']
    DemoSliderVertical: typeof import('./resources/ts/views/demos/forms/form-elements/slider/DemoSliderVertical.vue')['default']
    DemoSnackbarBasic: typeof import('./resources/ts/views/demos/components/snackbar/DemoSnackbarBasic.vue')['default']
    DemoSnackbarMultiLine: typeof import('./resources/ts/views/demos/components/snackbar/DemoSnackbarMultiLine.vue')['default']
    DemoSnackbarPosition: typeof import('./resources/ts/views/demos/components/snackbar/DemoSnackbarPosition.vue')['default']
    DemoSnackbarTimeout: typeof import('./resources/ts/views/demos/components/snackbar/DemoSnackbarTimeout.vue')['default']
    DemoSnackbarTransition: typeof import('./resources/ts/views/demos/components/snackbar/DemoSnackbarTransition.vue')['default']
    DemoSnackbarVariants: typeof import('./resources/ts/views/demos/components/snackbar/DemoSnackbarVariants.vue')['default']
    DemoSnackbarVertical: typeof import('./resources/ts/views/demos/components/snackbar/DemoSnackbarVertical.vue')['default']
    DemoSnackbarWithAction: typeof import('./resources/ts/views/demos/components/snackbar/DemoSnackbarWithAction.vue')['default']
    DemoSwiperAutoplay: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperAutoplay.vue')['default']
    DemoSwiperBasic: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperBasic.vue')['default']
    DemoSwiperCenteredSlidesOption1: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperCenteredSlidesOption1.vue')['default']
    DemoSwiperCenteredSlidesOption2: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperCenteredSlidesOption2.vue')['default']
    DemoSwiperCoverflowEffect: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperCoverflowEffect.vue')['default']
    DemoSwiperCubeEffect: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperCubeEffect.vue')['default']
    DemoSwiperFade: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperFade.vue')['default']
    DemoSwiperGallery: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperGallery.vue')['default']
    DemoSwiperGrid: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperGrid.vue')['default']
    DemoSwiperLazyLoading: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperLazyLoading.vue')['default']
    DemoSwiperMultipleSlidesPerView: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperMultipleSlidesPerView.vue')['default']
    DemoSwiperNavigation: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperNavigation.vue')['default']
    DemoSwiperPagination: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperPagination.vue')['default']
    DemoSwiperProgress: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperProgress.vue')['default']
    DemoSwiperResponsiveBreakpoints: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperResponsiveBreakpoints.vue')['default']
    DemoSwiperVirtualSlides: typeof import('./resources/ts/views/demos/components/swiper/DemoSwiperVirtualSlides.vue')['default']
    DemoSwitchBasic: typeof import('./resources/ts/views/demos/forms/form-elements/switch/DemoSwitchBasic.vue')['default']
    DemoSwitchColors: typeof import('./resources/ts/views/demos/forms/form-elements/switch/DemoSwitchColors.vue')['default']
    DemoSwitchInset: typeof import('./resources/ts/views/demos/forms/form-elements/switch/DemoSwitchInset.vue')['default']
    DemoSwitchLabelSlot: typeof import('./resources/ts/views/demos/forms/form-elements/switch/DemoSwitchLabelSlot.vue')['default']
    DemoSwitchModelAsArray: typeof import('./resources/ts/views/demos/forms/form-elements/switch/DemoSwitchModelAsArray.vue')['default']
    DemoSwitchStates: typeof import('./resources/ts/views/demos/forms/form-elements/switch/DemoSwitchStates.vue')['default']
    DemoSwitchTrueAndFalseValue: typeof import('./resources/ts/views/demos/forms/form-elements/switch/DemoSwitchTrueAndFalseValue.vue')['default']
    DemoTabsAlignment: typeof import('./resources/ts/views/demos/components/tabs/DemoTabsAlignment.vue')['default']
    DemoTabsBasic: typeof import('./resources/ts/views/demos/components/tabs/DemoTabsBasic.vue')['default']
    DemoTabsBasicPill: typeof import('./resources/ts/views/demos/components/tabs/DemoTabsBasicPill.vue')['default']
    DemoTabsCustomIcons: typeof import('./resources/ts/views/demos/components/tabs/DemoTabsCustomIcons.vue')['default']
    DemoTabsDynamic: typeof import('./resources/ts/views/demos/components/tabs/DemoTabsDynamic.vue')['default']
    DemoTabsFixed: typeof import('./resources/ts/views/demos/components/tabs/DemoTabsFixed.vue')['default']
    DemoTabsGrow: typeof import('./resources/ts/views/demos/components/tabs/DemoTabsGrow.vue')['default']
    DemoTabsPagination: typeof import('./resources/ts/views/demos/components/tabs/DemoTabsPagination.vue')['default']
    DemoTabsProgrammaticNavigation: typeof import('./resources/ts/views/demos/components/tabs/DemoTabsProgrammaticNavigation.vue')['default']
    DemoTabsStacked: typeof import('./resources/ts/views/demos/components/tabs/DemoTabsStacked.vue')['default']
    DemoTabsVertical: typeof import('./resources/ts/views/demos/components/tabs/DemoTabsVertical.vue')['default']
    DemoTabsVerticalPill: typeof import('./resources/ts/views/demos/components/tabs/DemoTabsVerticalPill.vue')['default']
    DemoTextareaAutoGrow: typeof import('./resources/ts/views/demos/forms/form-elements/textarea/DemoTextareaAutoGrow.vue')['default']
    DemoTextareaBasic: typeof import('./resources/ts/views/demos/forms/form-elements/textarea/DemoTextareaBasic.vue')['default']
    DemoTextareaBrowserAutocomplete: typeof import('./resources/ts/views/demos/forms/form-elements/textarea/DemoTextareaBrowserAutocomplete.vue')['default']
    DemoTextareaClearable: typeof import('./resources/ts/views/demos/forms/form-elements/textarea/DemoTextareaClearable.vue')['default']
    DemoTextareaCounter: typeof import('./resources/ts/views/demos/forms/form-elements/textarea/DemoTextareaCounter.vue')['default']
    DemoTextareaIcons: typeof import('./resources/ts/views/demos/forms/form-elements/textarea/DemoTextareaIcons.vue')['default']
    DemoTextareaNoResize: typeof import('./resources/ts/views/demos/forms/form-elements/textarea/DemoTextareaNoResize.vue')['default']
    DemoTextareaRows: typeof import('./resources/ts/views/demos/forms/form-elements/textarea/DemoTextareaRows.vue')['default']
    DemoTextareaStates: typeof import('./resources/ts/views/demos/forms/form-elements/textarea/DemoTextareaStates.vue')['default']
    DemoTextareaValidation: typeof import('./resources/ts/views/demos/forms/form-elements/textarea/DemoTextareaValidation.vue')['default']
    DemoTextareaVariant: typeof import('./resources/ts/views/demos/forms/form-elements/textarea/DemoTextareaVariant.vue')['default']
    DemoTextfieldBasic: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldBasic.vue')['default']
    DemoTextfieldClearable: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldClearable.vue')['default']
    DemoTextfieldCounter: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldCounter.vue')['default']
    DemoTextfieldCustomColors: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldCustomColors.vue')['default']
    DemoTextfieldDensity: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldDensity.vue')['default']
    DemoTextfieldIconEvents: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldIconEvents.vue')['default']
    DemoTextfieldIcons: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldIcons.vue')['default']
    DemoTextfieldIconSlots: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldIconSlots.vue')['default']
    DemoTextfieldLabelSlot: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldLabelSlot.vue')['default']
    DemoTextfieldPasswordInput: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldPasswordInput.vue')['default']
    DemoTextfieldPrefixesAndSuffixes: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldPrefixesAndSuffixes.vue')['default']
    DemoTextfieldSingleLine: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldSingleLine.vue')['default']
    DemoTextfieldState: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldState.vue')['default']
    DemoTextfieldValidation: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldValidation.vue')['default']
    DemoTextfieldVariant: typeof import('./resources/ts/views/demos/forms/form-elements/textfield/DemoTextfieldVariant.vue')['default']
    DemoTooltipDelayOnHover: typeof import('./resources/ts/views/demos/components/tooltip/DemoTooltipDelayOnHover.vue')['default']
    DemoTooltipEvents: typeof import('./resources/ts/views/demos/components/tooltip/DemoTooltipEvents.vue')['default']
    DemoTooltipLocation: typeof import('./resources/ts/views/demos/components/tooltip/DemoTooltipLocation.vue')['default']
    DemoTooltipTooltipOnVariousElements: typeof import('./resources/ts/views/demos/components/tooltip/DemoTooltipTooltipOnVariousElements.vue')['default']
    DemoTooltipTransition: typeof import('./resources/ts/views/demos/components/tooltip/DemoTooltipTransition.vue')['default']
    DemoTooltipVModelSupport: typeof import('./resources/ts/views/demos/components/tooltip/DemoTooltipVModelSupport.vue')['default']
    DialogCloseBtn: typeof import('./resources/ts/@core/components/DialogCloseBtn.vue')['default']
    DropZone: typeof import('./resources/ts/@core/components/DropZone.vue')['default']
    EnableOneTimePasswordDialog: typeof import('./resources/ts/components/dialogs/EnableOneTimePasswordDialog.vue')['default']
    ErrorHeader: typeof import('./resources/ts/components/ErrorHeader.vue')['default']
    I18n: typeof import('./resources/ts/@core/components/I18n.vue')['default']
    MoreBtn: typeof import('./resources/ts/@core/components/MoreBtn.vue')['default']
    Notifications: typeof import('./resources/ts/@core/components/Notifications.vue')['default']
    PaymentProvidersDialog: typeof import('./resources/ts/components/dialogs/PaymentProvidersDialog.vue')['default']
    PricingPlanDialog: typeof import('./resources/ts/components/dialogs/PricingPlanDialog.vue')['default']
    ProductDescriptionEditor: typeof import('./resources/ts/@core/components/ProductDescriptionEditor.vue')['default']
    ReferAndEarnDialog: typeof import('./resources/ts/components/dialogs/ReferAndEarnDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScrollToTop: typeof import('./resources/ts/@core/components/ScrollToTop.vue')['default']
    ShareProjectDialog: typeof import('./resources/ts/components/dialogs/ShareProjectDialog.vue')['default']
    Shortcuts: typeof import('./resources/ts/@core/components/Shortcuts.vue')['default']
    TablePagination: typeof import('./resources/ts/@core/components/TablePagination.vue')['default']
    TheCustomizer: typeof import('./resources/ts/@core/components/TheCustomizer.vue')['default']
    ThemeSwitcher: typeof import('./resources/ts/@core/components/ThemeSwitcher.vue')['default']
    TimelineBasic: typeof import('./resources/ts/views/demos/components/timeline/TimelineBasic.vue')['default']
    TimelineOutlined: typeof import('./resources/ts/views/demos/components/timeline/TimelineOutlined.vue')['default']
    TimelineWithIcons: typeof import('./resources/ts/views/demos/components/timeline/TimelineWithIcons.vue')['default']
    TiptapEditor: typeof import('./resources/ts/@core/components/TiptapEditor.vue')['default']
    TwoFactorAuthDialog: typeof import('./resources/ts/components/dialogs/TwoFactorAuthDialog.vue')['default']
    UserInfoEditDialog: typeof import('./resources/ts/components/dialogs/UserInfoEditDialog.vue')['default']
    UserUpgradePlanDialog: typeof import('./resources/ts/components/dialogs/UserUpgradePlanDialog.vue')['default']
    VueApexCharts: typeof import('vue3-apexcharts')['default']
  }
}
