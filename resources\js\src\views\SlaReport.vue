<template>

    <div class="vx-col w-full mb-base">
        <!-- FORM -->
        <vx-card title="SLA Report">
            <div class="vx-row mb-6">
                <div class="vx-col sm:w-1/3 w-full">
                    <span>Report Month/Year</span>
                </div>
                <div class="vx-col sm:w-2/3 w-full">
                    <datepicker :format="'MMMM yyyy'" v-model="cMonthYear" :minimumView="'month'"
                                :maximumView="'year'" :disabledDates="disabledDates"></datepicker>

                </div>
            </div>
            <div class="vx-row">
                <div class="vx-col w-full">
                    <!-- <vs-button class="mr-3 mb-2" to="/">Back</vs-button> -->
                    <vs-button class="mb-2" @click="generateReport">Generate Report</vs-button>
                    <vs-button class="mb-2" v-if="userRole.includes('1')" @click="confirmMTDReport" color="warning">Generate MTD Report</vs-button>
                </div>
            </div>
        </vx-card>

        <!-- LIST TABLE -->
        <vx-card class='mt-8' title="List of Generated SLA Report">
            <vs-table
                @search="handleSearch"
                @change-page="handleChangePage"
                @sort="handleSort"
                v-model="selected"
                pagination
                max-items="10"
                search
                :data="reports">

                <template slot="thead">
                    <vs-th sort-key="#">#</vs-th>
                    <!-- <vs-th sort-key="id">ID</vs-th> -->
                    <vs-th sort-key="filename">Report Name</vs-th>
                    <vs-th sort-key="status">Status</vs-th>
                    <vs-th sort-key="created_date">Created Date</vs-th>
                    <vs-th sort-key="created_by">Created By</vs-th>
                    <vs-th sort-key="action">Action</vs-th>
                </template>

                <template slot-scope="{data}">
                    <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td :data="data[indextr].id">
                            {{ indextr+1 }}
                        </vs-td>
                        <!-- <vs-td :data="data[indextr].id">
                            {{ data[indextr].id }}
                        </vs-td> -->
                        <vs-td :data="data[indextr].file_name">
                            <i v-if="data[indextr].hidden === 1" class="feather icon-eye-off mr-2" title="Admin view only."></i>{{ data[indextr].name }}
                        </vs-td>
                        <vs-td :data="data[indextr].status">
                            <vs-chip :color="getStatusColor(data[indextr].status)" size="small">
                                <i v-if="data[indextr].status !== 'completed' && data[indextr].status !== 'failed'" class="feather icon-loader mr-2 animate-spin"></i>
                                {{ data[indextr].status.toUpperCase() }}
                            </vs-chip>
                        </vs-td>
                        <vs-td :data="data[indextr].created_at">
                            {{ data[indextr].created_at_format }}
                        </vs-td>
                        <vs-td :data="data[indextr].created_by">
                            {{ data[indextr].created_by }}
                        </vs-td>
                        <vs-td :data="data[indextr].id">
                            <div class="flex items-center">
                                <vx-tooltip :text="'Download Report'" position="top">
                                    <vs-button
                                        @click="downloadReport(data[indextr].file_name, data[indextr].name)"
                                        :disabled="data[indextr].status !== 'completed'"
                                        icon-pack="feather"
                                        icon="icon-download"
                                        class="mr-2"
                                    >
                                    </vs-button>
                                </vx-tooltip>
                                <vx-tooltip :text="'Show Log'" position="top" v-if="userRole.includes('1')">
                                    <vs-button
                                        @click="showLog(data[indextr].id)"
                                        color="primary"
                                        icon-pack="feather"
                                        icon="icon-file-text"
                                        class="mr-2"
                                    >
                                    </vs-button>
                                </vx-tooltip>
                                <vx-tooltip :text="'Delete Report'" position="top" v-if="userRole.includes('1')">
                                    <vs-button
                                        @click="deleteReport(data[indextr].id)"
                                        color="danger"
                                        :disabled="data[indextr].status !== 'completed' && data[indextr].status !== 'failed'"
                                        icon-pack="feather"
                                        icon="icon-trash-2"
                                    >
                                    </vs-button>
                                </vx-tooltip>
                            </div>
                        </vs-td>
                    </vs-tr>
                </template>
            </vs-table>
        </vx-card>

        <!-- Log Modal -->
        <vs-popup class="log-modal" title="Report Log" :active.sync="showLogModal">
            <div class="log-content">
                <div v-if="currentLogData">
                    <h4 class="mb-4">{{ currentLogData.report_name }}</h4>
                    <div class="status-label mb-4">
                        <strong>Status:</strong>
                        <vs-chip :color="getStatusColor(currentLogData.status)" size="small" class="ml-2">
                            {{ currentLogData.status.toUpperCase() }}
                        </vs-chip>
                    </div>
                    <div class="log-data">
                        <strong>Log Details:</strong>
                        <pre class="log-text mt-2">{{ formatLogData(currentLogData.log) }}</pre>
                    </div>
                </div>
                <div v-else class="text-center">
                    <p>Loading log data...</p>
                </div>
            </div>
        </vs-popup>
    </div>

</template>

<style scoped>
    .animate-spin {
        animation: spin 2s linear infinite;
    }

    .status-label {
        display: flex;
        align-items: center;
    }

    .log-modal .vs-popup {
        max-width: 800px;
        width: 90%;
    }

    .log-content {
        max-height: 500px;
        overflow-y: auto;
    }

    .log-text {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 15px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        white-space: pre-wrap;
        word-wrap: break-word;
        max-height: 300px;
        overflow-y: auto;
    }
</style>


<script>
    // import jsPDF from 'jspdf'
    import 'jspdf-autotable'
    import Datepicker from 'vuejs-datepicker'
    // import {format, compareAsc} from 'date-fns'
    import axios from 'axios'

    export default {
        data() {
            return {
                cMonthYear: '',
                selected: [],
                log: [],
                reports: [],
                disabledDates: {},
                userRole: [],
                currentUser: null,
                showLogModal: false,
                currentLogData: null
            }
        },

        components: {
            Datepicker
        },

        async mounted() {
            await this.getCurrentUser();

            // set disabled dates to 1 months ago
            const dateFrom = new Date();
            const today = new Date();
            if (today.getDate() >= 3) { // enable current month report generation on 3rd day of the month
                dateFrom.setMonth(dateFrom.getMonth() - 1);
            } else {
                dateFrom.setMonth(dateFrom.getMonth() - 2);
            }
            this.disabledDates.from = dateFrom;
            this.cMonthYear = dateFrom;

            // check for report status every 15 seconds if on sla-report page
            const path = window.location.pathname;
            if (path === '/sla-report') {
                setInterval(() => {
                    const result = this.reports.filter(rep => rep.status === 'queue' || rep.status === 'processing');
                    if (result.length > 0) {
                        this.fetchReportList();
                    }
                }, 15000);
            }
        },

        methods: {

            async getCurrentUser() {
                try {
                    const { data } = await this.$http({
                        url: `user`,
                        method: 'GET'
                    });

                    if (data && data.role) {
                        this.currentUser = data;
                        this.userRole = data.role;
                        this.fetchReportList();
                    }
                } catch (err) {
                    this.notify('danger', 'Error!', 'Something went wrong!');
                    console.log(err);
                }
            },

            async generateReport() {
                if (this.currentUser && this.currentUser.name) {
                    try {
                        const response = await fetch('api/report/addtoqueue', {
                            method: 'post',
                            body: JSON.stringify({month: this.cMonthYear.getMonth() + 1, year: this.cMonthYear.getFullYear(), user_id: this.currentUser.id}),
                            headers: {
                                'content-type': 'application/json'
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            this.notify('success', 'Success!', result.message);
                        } else {
                            this.notify('danger', 'Error!', result.message);
                        }
                    } catch (err) {
                        this.notify('danger', 'Error!', 'Something went wrong! Please contact administrator.');
                        console.log(err);
                    }

                    this.fetchReportList();
                }
            },


            confirmMTDReport() {
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'warning',
                    title: 'Generate MTD Report',
                    text: `Would you like to generate the month-to-date report for ${new Date().toLocaleString('default', { month: 'long' })}? This report is accessible to administrators only.`,
                    accept: this.generateMTDReport.bind(this)
                });
            },


            generateMTDReport: async function () {
                const currentDate = new Date();
                try {
                    if (this.currentUser && this.currentUser.name) {
                        const response = await fetch('api/report/addtoqueue', {
                            method: 'post',
                            body: JSON.stringify({ month: currentDate.getMonth() + 1, year: currentDate.getFullYear(), user_id: this.currentUser.id, is_hidden: true }),
                            headers: {
                                'content-type': 'application/json'
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            this.notify('success', 'Success!', result.message);
                        } else {
                            this.notify('danger', 'Error!', result.message);
                        }

                        this.fetchReportList();
                    }
                } catch (err) {
                    this.notify('danger', 'Error!', 'Something went wrong! Please contact administrator.');
                    console.log(err);
                }
            },

            downloadReport(filename, reportName) {
                const url = '/api/report/download';
                if (this.currentUser && this.currentUser.id) {
                    axios.post(url, { file_name: filename, report_name: reportName, user_id: this.currentUser.id }, { responseType: 'blob' })
                        .then(response => {
                            const blob = new Blob([response.data], { type: 'application/pdf' });
                            const link = document.createElement('a');
                            link.href = URL.createObjectURL(blob);
                            link.download = reportName;
                            link.click();
                            URL.revokeObjectURL(url);
                        })
                        .catch(error => {
                            this.notify('danger', 'Error!', 'Failed to download report. Please try again.');
                            console.error(error);
                        });
                } else {
                    this.notify('danger', 'Error!', 'Failed to fetch user information. Please try again.');
                }
            },

            handleSearch(searching) {
                console.log(`The user searched for: ${searching}`)
            },
            handleChangePage(page) {
                console.log(`The user changed the page to: ${page}`)
            },
            handleSort(key, active) {
                console.log(`the user ordered: ${key} ${active}`)
            },

            fetchReportList() {
                const url = this.userRole.includes('1') ? 'api/report/list-admin' : 'api/report/list';
                fetch(url)
                    .then(res => res.json())
                    .then(res => {
                        this.reports = res.data;
                    })
                    .catch(err => console.log(err));
            },

            notify(color, title, text) {
                this.$vs.notify({
                    color: color,
                    title: title,
                    text: text,
                    iconPack: 'feather',
                    icon: color === 'success' ? 'icon-check-circle' : 'icon-alert-circle'
                });
            },

            getStatusColor(status) {
                switch (status) {
                    case 'queue':
                        return 'warning';
                    case 'processing':
                        return 'primary';
                    case 'completed':
                        return 'success';
                    case 'failed':
                        return 'danger';
                    default:
                        return 'info';
                }
            },

            deleteReport(id) {
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Delete Report',
                    text: 'Are you sure you want to delete this report?',
                    accept: this.deleteReportConfirm.bind(this, id)
                });
            },

            deleteReportConfirm(id) {
                axios.post(`/api/report/delete/${id}`)
                    .then(response => {
                        const res = response.data;
                        if (res.success) {
                            this.notify('success', 'Success!', res.message);
                            this.fetchReportList();
                        } else {
                            this.notify('danger', 'Error!', res.message);
                        }
                    })
                    .catch(err => {
                        this.notify('danger', 'Error!', 'Something went wrong! Please contact administrator.');
                        console.log(err);
                    });
            },

            showLog(reportId) {
                this.currentLogData = null;
                this.showLogModal = true;

                axios.get(`/api/report/log/${reportId}`)
                    .then(response => {
                        const res = response.data;
                        if (res.success) {
                            this.currentLogData = res;
                        } else {
                            this.notify('danger', 'Error!', res.message);
                            this.showLogModal = false;
                        }
                    })
                    .catch(err => {
                        this.notify('danger', 'Error!', 'Failed to load log data. Please try again.');
                        console.log(err);
                        this.showLogModal = false;
                    });
            },

            formatLogData(logData) {
                if (!logData) {
                    return 'No log data available.';
                }

                try {
                    // Try to parse as JSON first (for success logs)
                    const parsed = JSON.parse(logData);
                    return JSON.stringify(parsed, null, 2);
                } catch (e) {
                    // If not JSON, return as is (for error logs)
                    return logData;
                }
            },

        }
    }
</script>

