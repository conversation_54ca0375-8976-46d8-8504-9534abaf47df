(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[9],{

/***/ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/src/views/CrudDemo.vue?vue&type=script&lang=js&":
/*!******************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib??ref--4-0!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/src/views/CrudDemo.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ __webpack_exports__["default"] = ({
  data: function data() {
    return {
      currentx: 1,
      articles: [],
      article: {
        id: '',
        title: '',
        body: ''
      },
      article_id: '',
      pagination: {},
      edit: false
    };
  },
  created: function created() {
    this.fetchArticles();
  },
  methods: {
    fetchArticles: function fetchArticles(page_url) {
      var _this = this;

      var vm = this;
      page_url = page_url || '/api/articles';
      fetch(page_url).then(function (res) {
        return res.json();
      }).then(function (res) {
        _this.articles = res.data;
        vm.makePagination(res.meta, res.links);
      }).catch(function (err) {
        return console.log(err);
      });
    },
    makePagination: function makePagination(meta, links) {
      var pagination = {
        current_page: meta.current_page,
        last_page: meta.last_page,
        next_page_url: links.next,
        prev_page_url: links.prev,
        api_path: meta.path
      };
      this.pagination = pagination;
    },
    pageChange: function pageChange(evt) {
      this.$emit("change", evt);
      console.log(this.currentx);
      this.fetchArticles(this.pagination.api_path + '?page=' + this.currentx);
    },
    deleteArticle: function deleteArticle(id) {
      var _this2 = this;

      if (confirm('Are you Sure?')) {
        fetch("api/article/".concat(id), {
          method: 'delete'
        }).then(function (res) {
          return res.json();
        }).then(function (data) {
          alert('Article Removed');

          _this2.fetchArticles();
        }).catch(function (err) {
          return console.log(err);
        });
      }
    },
    addArticle: function addArticle() {
      var _this3 = this;

      if (this.edit === false) {
        // Edit
        fetch('api/article', {
          method: 'post',
          body: JSON.stringify(this.article),
          headers: {
            'content-type': 'application/json'
          }
        }).then(function (res) {
          return res.json();
        }).then(function (data) {
          _this3.article.title = '';
          _this3.article.body = '';
          alert('Article Added');

          _this3.fetchArticles();
        }).catch(function (err) {
          return console.log(err);
        });
      } else {
        // Update
        fetch('api/article', {
          method: 'put',
          body: JSON.stringify(this.article),
          headers: {
            'content-type': 'application/json'
          }
        }).then(function (res) {
          return res.json();
        }).then(function (data) {
          _this3.article.title = '';
          _this3.article.body = '';
          alert('Article Updated');

          _this3.fetchArticles();
        }).catch(function (err) {
          return console.log(err);
        });
      }
    },
    editArticle: function editArticle(article) {
      this.edit = true;
      this.article.id = article.id;
      this.article.article_id = article.id;
      this.article.title = article.title;
      this.article.body = article.body;
      window.scroll({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });
    },
    reset: function reset() {
      this.edit = false;
      this.article.id = '';
      this.article.article_id = '';
      this.article.title = '';
      this.article.body = '';
    }
  }
});

/***/ }),

/***/ "./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/src/views/CrudDemo.vue?vue&type=template&id=254a38eb&scoped=true&":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/src/views/CrudDemo.vue?vue&type=template&id=254a38eb&scoped=true& ***!
  \**********************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
var render = function() {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  return _c(
    "div",
    [
      _c("h2", [_vm._v("Articles")]),
      _vm._v(" "),
      _c("div", { staticClass: "vx-row" }, [
        _c(
          "div",
          { staticClass: "vx-col md:w-1/1 w-full mb-base" },
          [
            _c("vx-card", { attrs: { title: "Add Article" } }, [
              _c("div", { staticClass: "vx-row mb-6" }, [
                _c("div", { staticClass: "vx-col sm:w-1/6 w-full" }, [
                  _c("span", [_vm._v("Title")])
                ]),
                _vm._v(" "),
                _c(
                  "div",
                  { staticClass: "vx-col sm:w-5/6 w-full" },
                  [
                    _c("vs-input", {
                      staticClass: "w-full",
                      model: {
                        value: _vm.article.title,
                        callback: function($$v) {
                          _vm.$set(_vm.article, "title", $$v)
                        },
                        expression: "article.title"
                      }
                    })
                  ],
                  1
                )
              ]),
              _vm._v(" "),
              _c("div", { staticClass: "vx-row mb-6" }, [
                _c("div", { staticClass: "vx-col sm:w-1/6 w-full" }, [
                  _c("span", [_vm._v("Body")])
                ]),
                _vm._v(" "),
                _c(
                  "div",
                  { staticClass: "vx-col sm:w-5/6 w-full" },
                  [
                    _c("vs-textarea", {
                      staticClass: "w-full",
                      model: {
                        value: _vm.article.body,
                        callback: function($$v) {
                          _vm.$set(_vm.article, "body", $$v)
                        },
                        expression: "article.body"
                      }
                    })
                  ],
                  1
                )
              ]),
              _vm._v(" "),
              _c("div", { staticClass: "vx-row" }, [
                _c(
                  "div",
                  { staticClass: "vx-col sm:w-2/3 w-full ml-auto" },
                  [
                    _c(
                      "vs-button",
                      {
                        staticClass: "mr-3 mb-2",
                        on: { click: _vm.addArticle }
                      },
                      [_vm._v("Save")]
                    ),
                    _vm._v(" "),
                    _c(
                      "vs-button",
                      {
                        staticClass: "mb-2",
                        attrs: { color: "warning", type: "border" },
                        on: { click: _vm.reset }
                      },
                      [_vm._v("Reset\n                        ")]
                    )
                  ],
                  1
                )
              ])
            ])
          ],
          1
        )
      ]),
      _vm._v(" "),
      _c("p", [_vm._v("Current: " + _vm._s(_vm.currentx))]),
      _vm._v(" "),
      _c(
        "div",
        [
          _c("vs-pagination", {
            attrs: {
              total: _vm.pagination.last_page,
              max: _vm.pagination.last_page
            },
            on: { change: _vm.pageChange },
            model: {
              value: _vm.currentx,
              callback: function($$v) {
                _vm.currentx = $$v
              },
              expression: "currentx"
            }
          })
        ],
        1
      ),
      _vm._v(" "),
      _vm._l(_vm.articles, function(article) {
        return _c(
          "vx-card",
          {
            key: article.id,
            staticClass: "mt-2 mb-2",
            attrs: { title: article.title }
          },
          [
            _c("p", [_vm._v(_vm._s(article.body))]),
            _vm._v(" "),
            _c(
              "vs-button",
              {
                attrs: { color: "warning", type: "filled" },
                on: {
                  click: function($event) {
                    return _vm.editArticle(article)
                  }
                }
              },
              [_vm._v("Edit")]
            ),
            _vm._v(" "),
            _c(
              "vs-button",
              {
                attrs: { color: "danger", type: "filled" },
                on: {
                  click: function($event) {
                    return _vm.deleteArticle(article.id)
                  }
                }
              },
              [_vm._v("Delete")]
            )
          ],
          1
        )
      })
    ],
    2
  )
}
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ "./resources/js/src/views/CrudDemo.vue":
/*!*********************************************!*\
  !*** ./resources/js/src/views/CrudDemo.vue ***!
  \*********************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _CrudDemo_vue_vue_type_template_id_254a38eb_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CrudDemo.vue?vue&type=template&id=254a38eb&scoped=true& */ "./resources/js/src/views/CrudDemo.vue?vue&type=template&id=254a38eb&scoped=true&");
/* harmony import */ var _CrudDemo_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CrudDemo.vue?vue&type=script&lang=js& */ "./resources/js/src/views/CrudDemo.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport *//* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");





/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _CrudDemo_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _CrudDemo_vue_vue_type_template_id_254a38eb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _CrudDemo_vue_vue_type_template_id_254a38eb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "254a38eb",
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/js/src/views/CrudDemo.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./resources/js/src/views/CrudDemo.vue?vue&type=script&lang=js&":
/*!**********************************************************************!*\
  !*** ./resources/js/src/views/CrudDemo.vue?vue&type=script&lang=js& ***!
  \**********************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CrudDemo_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib??ref--4-0!../../../../node_modules/vue-loader/lib??vue-loader-options!./CrudDemo.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/src/views/CrudDemo.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CrudDemo_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/js/src/views/CrudDemo.vue?vue&type=template&id=254a38eb&scoped=true&":
/*!****************************************************************************************!*\
  !*** ./resources/js/src/views/CrudDemo.vue?vue&type=template&id=254a38eb&scoped=true& ***!
  \****************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_CrudDemo_vue_vue_type_template_id_254a38eb_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../node_modules/vue-loader/lib??vue-loader-options!./CrudDemo.vue?vue&type=template&id=254a38eb&scoped=true& */ "./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/src/views/CrudDemo.vue?vue&type=template&id=254a38eb&scoped=true&");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_CrudDemo_vue_vue_type_template_id_254a38eb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_CrudDemo_vue_vue_type_template_id_254a38eb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });



/***/ })

}]);