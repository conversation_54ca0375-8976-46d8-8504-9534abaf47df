<?php

namespace App\Http\Controllers;

use App\Http\Resources\ParameterResource;
use App\Parameter;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use DB;
use Illuminate\Support\Facades\Auth;

class ParameterController extends Controller
{
    //

/*    public function getParameterList()
    {
        $paramList = DB::connection('mysql')->table('parameters as param')->get();

        return response()->json([
            'parameter_list' => $paramList,
        ]);
    }*/

    public function getParameterList()
    {
        // Get parameters
        $parameters = Parameter::all();

//        dd($parameters);

        // Return collection of parameters as a resource
        return ParameterResource::collection($parameters);
    }

    public function getParameterPageList($page)
    {
        $parameters = Parameter::where('page', $page)->get();

        return ParameterResource::collection($parameters);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $param = $request->isMethod('put') ? Parameter::findOrFail($request->id) : new Parameter;

        $param->id = $request->input('id');
        $param->parameter_value = $request->input('value');
        $param->modified_by = $request->input('userid');
        $param->modified_date = Carbon::now();

        if ($param->save()) {
            return new ParameterResource($param);
        }
    }
}
