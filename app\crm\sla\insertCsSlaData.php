<?php

/*
 * Function : Insert data from table in db crm into table sla_cs in db poms
 * Date : 4 Oct 2019
 * Author : Nur Asmaa
 */

namespace App\crm\sla;

use Carbon\Carbon;
use DateTime;
use Log;
use DB;
use Ramsey\Uuid\Uuid;
use App\Nagios\MigrateUtils;
use Config;

class insertCsSlaData {  
    
    public static function runCheckingSlaCSData($dateStart = null, $dateEnd = null) {

        Log::debug(self::class . ' Starting ... Checking SLA CS Data ', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);

        self::insertCsSlaData($dateStart, $dateEnd);
    }

    /* Read data from Database crm copy into database cdc_poms */

    private static function insertCsSlaData($dateStart, $dateEnd) {

        Log::info(self::class . ' Start : ' . __FUNCTION__ . '     ->> Date Start: ' . $dateStart . ', Date End: ' . $dateEnd);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);
        $dtStartTime = Carbon::now();
        
        $Data1 = Carbon::now()->subDays(3);
        $getCurr1 = strtotime($Data1);
        
        $Data2 = Carbon::yesterday();
        $getCurr2 = strtotime($Data2);
        
        $processStartDate = date('Y-m-d', $getCurr1);
        $processEndDate = date('Y-m-d', $getCurr2);
        
        
         try{
             
             $result = "SELECT DISTINCT `c`.`case_number`                AS `case_number`,
                        CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00') AS `created_date`,
                        `cc`.`incident_service_type_c`                   AS `type_of_incident`,
                        `c`.`status`                                     AS `case_status`,
                        `c`.`sla_flag`                                   AS `cs_sla_flag`,
                        `cc`.`contact_mode_c`                             AS `contact_mode`,
                        us.`first_name` AS created_by,
                        CONVERT_TZ(`c`.`cntc_mode_sla_startdate`,'+00:00','+08:00') AS `cs_start_datetime`,
                        CONVERT_TZ(`c`.`cntc_mode_sla_duedate`,'+00:00','+08:00')   AS `cs_due_datetime`,
                        CONVERT_TZ(`c`.`cntc_mode_sla_startdate`,'+00:00','+08:00') AS `cs_actual_start_datetime`,
                        CONVERT_TZ(`c`.`cntc_mode_executiondate`,'+00:00','+08:00') AS `cs_completed_datetime`,
                        (CASE WHEN (`c`.`cntc_mode_sla_startdate` IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(`c`.`cntc_mode_sla_startdate`,'+00:00','+08:00'),CONVERT_TZ(`c`.`cntc_mode_sla_duedate`,'+00:00','+08:00')) END)   AS `cs_available_duration`,
                        (CASE WHEN (`c`.`cntc_mode_sla_startdate` IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(`c`.`cntc_mode_sla_startdate`,'+00:00','+08:00'),CONVERT_TZ(`c`.`cntc_mode_executiondate`,'+00:00','+08:00')) END) AS `cs_actual_duration`,
                        `c`.`is_sent`                                     AS `is_sent`  
                        FROM ((`cases` `c`
                                JOIN `cases_cstm` `cc`
                                  ON ((`c`.`id` = `cc`.`id_c`)))
                               LEFT JOIN `cstm_list_app` `cmode`
                                 ON (((`cc`.`contact_mode_c` = `cmode`.`value_code`)
                                      AND (TRIM(`cmode`.`value_code`) <> '')
                                      AND (`cmode`.`type_code` = 'cdc_contact_mode_list')))
                               LEFT JOIN users us
				 ON (us.`id`=c.`created_by`))
                            WHERE((STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN ? AND ?)
                            AND  (cc.category_c NOT IN (10715,10722,10721,10719,10720))
                            AND (`c`.`deleted` = 0)
                            AND c.`name` NOT IN (''))";
     
            $results = DB::connection('mysql_crm')->select($result, array($processStartDate,$processEndDate)); 
                       
            if (is_array($results) || is_object($results)) {
                $counter = 0;
                foreach ($results as $data) {
                    $count = DB::connection('mysql')->table('sla_cs')
                            ->where('case_number', $data->case_number)
                            ->count();

                    $insertedCaseNum = DB::connection('mysql_crm')->table('cases')
                            ->where('case_number', $data->case_number)
                            ->where('is_sent', 1)
                            ->count();    
                    
                    $executionDateNull = DB::connection('mysql_crm')->table('cases')
                            ->where('case_number', $data->case_number)
                            ->whereNull('cntc_mode_executiondate')
                            ->count();  

                    if ($count == 0 && $insertedCaseNum == 0 && $executionDateNull == 0) {
                        
                        $contactMode = $data->contact_mode;
                        
                        if ($data->contact_mode == 'Email' && $data->created_by != 'Email') {
                            $contactMode = 'Email-Inbound';
                        }

                        $insertDataDate = Carbon::now();
                    
                        DB::connection('mysql')
                        ->insert('insert into sla_cs
                            (case_number, created_date, type_of_incident, case_status, cs_sla_flag, contact_mode, cs_start_datetime, cs_due_datetime, cs_actual_start_datetime, cs_completed_datetime, cs_available_duration,cs_actual_duration, cs_insert_data_datetime ) 
                            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [   
                                $data->case_number, 
                                $data->created_date,
                                $data->type_of_incident,
                                $data->case_status,
                                $data->cs_sla_flag,
                                $contactMode,
                                $data->cs_start_datetime, 
                                $data->cs_due_datetime,
                                $data->cs_actual_start_datetime,
                                $data->cs_completed_datetime,
                                $data->cs_available_duration,
                                $data->cs_actual_duration,
                                $insertDataDate
                                ]);
                        
                         DB::connection('mysql_crm')
                                ->table('cases')
                                ->where('case_number', $data->case_number)
                                ->update([
                                    'is_sent' => 1
                        ]);
                         
                        $counter++;
                        if ($counter == 50) {
                            sleep(1);
                            $counter = 0;
                        }
                        
                        $logsdata = self::class . ' Successfully insert data for SLA CS => Date Start : ' . $dtStartTime . ' -> '
                                . 'Query Date End : ' . Carbon::now() . ', Case Number : ' . $data->case_number . ', Line Number : '.$counter.' , Completed --- Taken Time : ' .
                                json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

                        Log::info($logsdata);
                        dump($logsdata);
                    }
                }
            } 

            return $results;
             
         }catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }     
        return null;
    }

}
