{"name": "vuexy-vuejs-laravel-admin-template", "version": "9.4.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "typecheck": "vue-tsc --noEmit", "lint": "eslint . -c .eslintrc.cjs --fix --ext .ts,.js,.cjs,.vue,.tsx,.jsx", "build:icons": "tsx resources/ts/plugins/iconify/build-icons.ts", "msw:init": "msw init public/ --save", "postinstall": "npm run build:icons && npm run msw:init"}, "dependencies": {"@casl/ability": "6.7.3", "@casl/vue": "2.2.2", "@floating-ui/dom": "1.6.8", "@formkit/drag-and-drop": "0.1.6", "@sindresorhus/is": "7.0.1", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@tiptap/vue-3": "^2.12.0", "@vueuse/core": "10.11.1", "@vueuse/math": "10.11.1", "apexcharts": "3.54.1", "chart.js": "4.4.9", "cookie-es": "1.2.2", "destr": "2.0.5", "eslint-plugin-regexp": "2.7.0", "jwt-decode": "4.0.0", "mapbox-gl": "3.5.2", "ofetch": "1.4.1", "pinia": "3.0.2", "prismjs": "1.30.0", "roboto-fontface": "0.10.0", "shepherd.js": "13.0.3", "swiper": "11.2.7", "ufo": "1.6.1", "unplugin-vue-define-options": "1.5.5", "vue": "3.5.14", "vue-chartjs": "5.3.2", "vue-flatpickr-component": "11.0.5", "vue-i18n": "9.13.1", "vue-prism-component": "2.0.0", "vue-router": "4.5.1", "vue3-apexcharts": "1.8.0", "vue3-perfect-scrollbar": "2.0.0", "vuetify": "3.8.5", "webfontloader": "1.6.28"}, "devDependencies": {"@antfu/eslint-config-vue": "0.43.1", "@antfu/utils": "0.7.10", "@fullcalendar/core": "6.1.17", "@fullcalendar/daygrid": "6.1.17", "@fullcalendar/interaction": "6.1.17", "@fullcalendar/list": "6.1.17", "@fullcalendar/timegrid": "6.1.17", "@fullcalendar/vue3": "6.1.17", "@iconify-json/mdi": "1.2.3", "@iconify/tools": "4.1.2", "@iconify/utils": "2.3.0", "@iconify-json/fa": "1.2.1", "@iconify-json/tabler": "1.2.18", "@iconify/vue": "4.1.2", "@intlify/unplugin-vue-i18n": "4.0.0", "@stylistic/stylelint-config": "1.0.1", "@stylistic/stylelint-plugin": "2.1.3", "@tiptap/extension-character-count": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-subscript": "^2.12.0", "@tiptap/extension-superscript": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@types/mapbox-gl": "3.4.1", "@types/node": "22.15.21", "@types/webfontloader": "1.6.38", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "@videojs-player/vue": "1.0.0", "@vitejs/plugin-vue": "5.2.4", "@vitejs/plugin-vue-jsx": "4.2.0", "eslint": "8.57.1", "eslint-config-airbnb-base": "15.0.0", "eslint-import-resolver-typescript": "3.10.1", "eslint-plugin-case-police": "0.6.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-promise": "6.6.0", "eslint-plugin-regex": "1.10.0", "eslint-plugin-sonarjs": "0.24.0", "eslint-plugin-unicorn": "51.0.1", "eslint-plugin-vue": "9.33.0", "msw": "2.6.8", "postcss-html": "1.8.0", "postcss-scss": "4.0.9", "sass": "1.76.0", "shiki": "1.29.2", "stylelint": "16.8.0", "stylelint-config-idiomatic-order": "10.0.0", "stylelint-config-standard-scss": "13.1.0", "stylelint-use-logical-spec": "5.0.1", "tsx": "4.19.4", "type-fest": "4.41.0", "typescript": "5.8.3", "unplugin-auto-import": "0.18.6", "unplugin-vue-components": "0.27.5", "unplugin-vue-router": "0.8.8", "video.js": "8.6.0", "vite": "6.3.5", "vite-plugin-vue-devtools": "7.3.7", "vite-plugin-vue-meta-layouts": "0.5.1", "vite-plugin-vuetify": "2.0.3", "vite-svg-loader": "5.1.0", "vue-shepherd": "3.0.0", "vue-tsc": "2.2.10", "laravel-vite-plugin": "1.2.0"}, "resolutions": {"postcss": "^8", "@tiptap/core": "^2", "@types/video.js": "^7"}, "overrides": {"postcss": "^8", "@tiptap/core": "^2", "@types/video.js": "^7"}, "msw": {"workerDirectory": "public"}}