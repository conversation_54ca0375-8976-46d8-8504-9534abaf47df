/**
 * This injects Tailwind's base styles, which is a combination of
 * Normalize.css and some additional base styles.
 *
 * You can see the styles here:
 * https://github.com/tailwindcss/tailwindcss/blob/master/css/preflight.css
 *
 * If using `postcss-import`, you should import this line from it's own file:
 *
 * @import "./tailwind-preflight.css";
 *
 * See: https://github.com/tailwindcss/tailwindcss/issues/53#issuecomment-341413622
 */

/*NOTE: 'preflight' IS RENAMED TO 'base'*/

/*@tailwind base;*/

/**
 * Here you would add any of your custom component classes; stuff that you'd
 * want loaded *before* the utilities so that the utilities could still
 * override them.
 *
 * Example:
 *
 * .btn { ... }
 * .form-input { ... }
 *
 * Or if using a preprocessor or `postcss-import`:
 *
 * @import "components/buttons";
 * @import "components/forms";
 */

.container {
  width: 100%
}

[dir=ltr] .container {
margin-right: auto;
margin-left: auto;
padding-right: 1rem;
padding-left: 1rem
}

[dir=rtl] .container {
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem
}

@media (min-width: 576px) {
  .container {
    max-width: 576px
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 992px
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1200px
  }
}

/**
 * This injects all of Tailwind's utility classes, generated based on your
 * config file.
 *
 * If using `postcss-import`, you should import this line from it's own file:
 *
 * @import "./tailwind-utilities.css";
 *
 * See: https://github.com/tailwindcss/tailwindcss/issues/53#issuecomment-341413622
 */

.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important
}

[dir] .sr-only {
  padding: 0 !important;
  margin: -1px !important;
  border-width: 0 !important
}

.not-sr-only {
  position: static !important;
  width: auto !important;
  height: auto !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important
}

[dir] .not-sr-only {
  padding: 0 !important;
  margin: 0 !important
}

.focus\:sr-only:focus {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important
}

[dir] .focus\:sr-only:focus {
  padding: 0 !important;
  margin: -1px !important;
  border-width: 0 !important
}

.focus\:not-sr-only:focus {
  position: static !important;
  width: auto !important;
  height: auto !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important
}

[dir] .focus\:not-sr-only:focus {
  padding: 0 !important;
  margin: 0 !important
}

.appearance-none {
  -webkit-appearance: none !important;
     -moz-appearance: none !important;
          appearance: none !important
}

[dir] .bg-fixed {
  background-attachment: fixed !important
}

[dir] .bg-local {
  background-attachment: local !important
}

[dir] .bg-scroll {
  background-attachment: scroll !important
}

[dir] .bg-transparent {
  background-color: transparent !important
}

[dir] .bg-black {
  background-color: #22292f !important
}

[dir] .bg-white {
  background-color: #ffffff !important
}

[dir] .bg-grey {
  background-color: #b8c2cc !important
}

[dir] .bg-grey-light {
  background-color: #dae1e7 !important
}

[dir] .hover\:bg-transparent:hover {
  background-color: transparent !important
}

[dir] .hover\:bg-black:hover {
  background-color: #22292f !important
}

[dir] .hover\:bg-white:hover {
  background-color: #ffffff !important
}

[dir] .hover\:bg-grey:hover {
  background-color: #b8c2cc !important
}

[dir] .hover\:bg-grey-light:hover {
  background-color: #dae1e7 !important
}

[dir] .focus\:bg-transparent:focus {
  background-color: transparent !important
}

[dir] .focus\:bg-black:focus {
  background-color: #22292f !important
}

[dir] .focus\:bg-white:focus {
  background-color: #ffffff !important
}

[dir] .focus\:bg-grey:focus {
  background-color: #b8c2cc !important
}

[dir] .focus\:bg-grey-light:focus {
  background-color: #dae1e7 !important
}

[dir] .bg-auto {
  background-size: auto !important
}

[dir] .bg-cover {
  background-size: cover !important
}

[dir] .bg-contain {
  background-size: contain !important
}

.border-collapse {
  border-collapse: collapse !important
}

.border-separate {
  border-collapse: separate !important
}

[dir] .border-transparent {
  border-color: transparent !important
}

[dir] .border-black {
  border-color: #22292f !important
}

[dir] .border-white {
  border-color: #ffffff !important
}

[dir] .border-grey {
  border-color: #b8c2cc !important
}

[dir] .border-grey-light {
  border-color: #dae1e7 !important
}

[dir] .hover\:border-transparent:hover {
  border-color: transparent !important
}

[dir] .hover\:border-black:hover {
  border-color: #22292f !important
}

[dir] .hover\:border-white:hover {
  border-color: #ffffff !important
}

[dir] .hover\:border-grey:hover {
  border-color: #b8c2cc !important
}

[dir] .hover\:border-grey-light:hover {
  border-color: #dae1e7 !important
}

[dir] .rounded-none {
  border-radius: 0 !important
}

[dir] .rounded-sm {
  border-radius: .125rem !important
}

[dir] .rounded {
  border-radius: .25rem !important
}

[dir] .rounded-lg {
  border-radius: .5rem !important
}

[dir] .rounded-full {
  border-radius: 9999px !important
}

[dir=ltr] .rounded-t-none {
border-top-left-radius: 0 !important;
border-top-right-radius: 0 !important
}

[dir=rtl] .rounded-t-none {
  border-top-right-radius: 0 !important;
  border-top-left-radius: 0 !important
}

[dir=ltr] .rounded-r-none {
border-top-right-radius: 0 !important;
border-bottom-right-radius: 0 !important
}

[dir=rtl] .rounded-r-none {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important
}

[dir=ltr] .rounded-b-none {
border-bottom-right-radius: 0 !important;
border-bottom-left-radius: 0 !important
}

[dir=rtl] .rounded-b-none {
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important
}

[dir=ltr] .rounded-l-none {
border-top-left-radius: 0 !important;
border-bottom-left-radius: 0 !important
}

[dir=rtl] .rounded-l-none {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important
}

[dir=ltr] .rounded-t-sm {
border-top-left-radius: .125rem !important;
border-top-right-radius: .125rem !important
}

[dir=rtl] .rounded-t-sm {
  border-top-right-radius: .125rem !important;
  border-top-left-radius: .125rem !important
}

[dir=ltr] .rounded-r-sm {
border-top-right-radius: .125rem !important;
border-bottom-right-radius: .125rem !important
}

[dir=rtl] .rounded-r-sm {
  border-top-left-radius: .125rem !important;
  border-bottom-left-radius: .125rem !important
}

[dir=ltr] .rounded-b-sm {
border-bottom-right-radius: .125rem !important;
border-bottom-left-radius: .125rem !important
}

[dir=rtl] .rounded-b-sm {
  border-bottom-left-radius: .125rem !important;
  border-bottom-right-radius: .125rem !important
}

[dir=ltr] .rounded-l-sm {
border-top-left-radius: .125rem !important;
border-bottom-left-radius: .125rem !important
}

[dir=rtl] .rounded-l-sm {
  border-top-right-radius: .125rem !important;
  border-bottom-right-radius: .125rem !important
}

[dir=ltr] .rounded-t {
border-top-left-radius: .25rem !important;
border-top-right-radius: .25rem !important
}

[dir=rtl] .rounded-t {
  border-top-right-radius: .25rem !important;
  border-top-left-radius: .25rem !important
}

[dir=ltr] .rounded-r {
border-top-right-radius: .25rem !important;
border-bottom-right-radius: .25rem !important
}

[dir=rtl] .rounded-r {
  border-top-left-radius: .25rem !important;
  border-bottom-left-radius: .25rem !important
}

[dir=ltr] .rounded-b {
border-bottom-right-radius: .25rem !important;
border-bottom-left-radius: .25rem !important
}

[dir=rtl] .rounded-b {
  border-bottom-left-radius: .25rem !important;
  border-bottom-right-radius: .25rem !important
}

[dir=ltr] .rounded-l {
border-top-left-radius: .25rem !important;
border-bottom-left-radius: .25rem !important
}

[dir=rtl] .rounded-l {
  border-top-right-radius: .25rem !important;
  border-bottom-right-radius: .25rem !important
}

[dir=ltr] .rounded-t-lg {
border-top-left-radius: .5rem !important;
border-top-right-radius: .5rem !important
}

[dir=rtl] .rounded-t-lg {
  border-top-right-radius: .5rem !important;
  border-top-left-radius: .5rem !important
}

[dir=ltr] .rounded-r-lg {
border-top-right-radius: .5rem !important;
border-bottom-right-radius: .5rem !important
}

[dir=rtl] .rounded-r-lg {
  border-top-left-radius: .5rem !important;
  border-bottom-left-radius: .5rem !important
}

[dir=ltr] .rounded-b-lg {
border-bottom-right-radius: .5rem !important;
border-bottom-left-radius: .5rem !important
}

[dir=rtl] .rounded-b-lg {
  border-bottom-left-radius: .5rem !important;
  border-bottom-right-radius: .5rem !important
}

[dir=ltr] .rounded-l-lg {
border-top-left-radius: .5rem !important;
border-bottom-left-radius: .5rem !important
}

[dir=rtl] .rounded-l-lg {
  border-top-right-radius: .5rem !important;
  border-bottom-right-radius: .5rem !important
}

[dir=ltr] .rounded-t-full {
border-top-left-radius: 9999px !important;
border-top-right-radius: 9999px !important
}

[dir=rtl] .rounded-t-full {
  border-top-right-radius: 9999px !important;
  border-top-left-radius: 9999px !important
}

[dir=ltr] .rounded-r-full {
border-top-right-radius: 9999px !important;
border-bottom-right-radius: 9999px !important
}

[dir=rtl] .rounded-r-full {
  border-top-left-radius: 9999px !important;
  border-bottom-left-radius: 9999px !important
}

[dir=ltr] .rounded-b-full {
border-bottom-right-radius: 9999px !important;
border-bottom-left-radius: 9999px !important
}

[dir=rtl] .rounded-b-full {
  border-bottom-left-radius: 9999px !important;
  border-bottom-right-radius: 9999px !important
}

[dir=ltr] .rounded-l-full {
border-top-left-radius: 9999px !important;
border-bottom-left-radius: 9999px !important
}

[dir=rtl] .rounded-l-full {
  border-top-right-radius: 9999px !important;
  border-bottom-right-radius: 9999px !important
}

[dir=ltr] .rounded-tl-none {
border-top-left-radius: 0 !important
}

[dir=rtl] .rounded-tl-none {
  border-top-right-radius: 0 !important
}

[dir=ltr] .rounded-tr-none {
border-top-right-radius: 0 !important
}

[dir=rtl] .rounded-tr-none {
  border-top-left-radius: 0 !important
}

[dir=ltr] .rounded-br-none {
border-bottom-right-radius: 0 !important
}

[dir=rtl] .rounded-br-none {
  border-bottom-left-radius: 0 !important
}

[dir=ltr] .rounded-bl-none {
border-bottom-left-radius: 0 !important
}

[dir=rtl] .rounded-bl-none {
  border-bottom-right-radius: 0 !important
}

[dir=ltr] .rounded-tl-sm {
border-top-left-radius: .125rem !important
}

[dir=rtl] .rounded-tl-sm {
  border-top-right-radius: .125rem !important
}

[dir=ltr] .rounded-tr-sm {
border-top-right-radius: .125rem !important
}

[dir=rtl] .rounded-tr-sm {
  border-top-left-radius: .125rem !important
}

[dir=ltr] .rounded-br-sm {
border-bottom-right-radius: .125rem !important
}

[dir=rtl] .rounded-br-sm {
  border-bottom-left-radius: .125rem !important
}

[dir=ltr] .rounded-bl-sm {
border-bottom-left-radius: .125rem !important
}

[dir=rtl] .rounded-bl-sm {
  border-bottom-right-radius: .125rem !important
}

[dir=ltr] .rounded-tl {
border-top-left-radius: .25rem !important
}

[dir=rtl] .rounded-tl {
  border-top-right-radius: .25rem !important
}

[dir=ltr] .rounded-tr {
border-top-right-radius: .25rem !important
}

[dir=rtl] .rounded-tr {
  border-top-left-radius: .25rem !important
}

[dir=ltr] .rounded-br {
border-bottom-right-radius: .25rem !important
}

[dir=rtl] .rounded-br {
  border-bottom-left-radius: .25rem !important
}

[dir=ltr] .rounded-bl {
border-bottom-left-radius: .25rem !important
}

[dir=rtl] .rounded-bl {
  border-bottom-right-radius: .25rem !important
}

[dir=ltr] .rounded-tl-lg {
border-top-left-radius: .5rem !important
}

[dir=rtl] .rounded-tl-lg {
  border-top-right-radius: .5rem !important
}

[dir=ltr] .rounded-tr-lg {
border-top-right-radius: .5rem !important
}

[dir=rtl] .rounded-tr-lg {
  border-top-left-radius: .5rem !important
}

[dir=ltr] .rounded-br-lg {
border-bottom-right-radius: .5rem !important
}

[dir=rtl] .rounded-br-lg {
  border-bottom-left-radius: .5rem !important
}

[dir=ltr] .rounded-bl-lg {
border-bottom-left-radius: .5rem !important
}

[dir=rtl] .rounded-bl-lg {
  border-bottom-right-radius: .5rem !important
}

[dir=ltr] .rounded-tl-full {
border-top-left-radius: 9999px !important
}

[dir=rtl] .rounded-tl-full {
  border-top-right-radius: 9999px !important
}

[dir=ltr] .rounded-tr-full {
border-top-right-radius: 9999px !important
}

[dir=rtl] .rounded-tr-full {
  border-top-left-radius: 9999px !important
}

[dir=ltr] .rounded-br-full {
border-bottom-right-radius: 9999px !important
}

[dir=rtl] .rounded-br-full {
  border-bottom-left-radius: 9999px !important
}

[dir=ltr] .rounded-bl-full {
border-bottom-left-radius: 9999px !important
}

[dir=rtl] .rounded-bl-full {
  border-bottom-right-radius: 9999px !important
}

[dir] .border-solid {
  border-style: solid !important
}

[dir] .border-dashed {
  border-style: dashed !important
}

[dir] .border-dotted {
  border-style: dotted !important
}

[dir] .border-double {
  border-style: double !important
}

[dir] .border-none {
  border-style: none !important
}

[dir] .border-0 {
  border-width: 0 !important
}

[dir] .border-2 {
  border-width: 2px !important
}

[dir] .border-4 {
  border-width: 4px !important
}

[dir] .border-8 {
  border-width: 8px !important
}

[dir] .border {
  border-width: 1px !important
}

[dir] .border-t-0 {
  border-top-width: 0 !important
}

[dir=ltr] .border-r-0 {
border-right-width: 0 !important
}

[dir=rtl] .border-r-0 {
  border-left-width: 0 !important
}

[dir] .border-b-0 {
  border-bottom-width: 0 !important
}

[dir=ltr] .border-l-0 {
border-left-width: 0 !important
}

[dir=rtl] .border-l-0 {
  border-right-width: 0 !important
}

[dir] .border-t-2 {
  border-top-width: 2px !important
}

[dir=ltr] .border-r-2 {
border-right-width: 2px !important
}

[dir=rtl] .border-r-2 {
  border-left-width: 2px !important
}

[dir] .border-b-2 {
  border-bottom-width: 2px !important
}

[dir=ltr] .border-l-2 {
border-left-width: 2px !important
}

[dir=rtl] .border-l-2 {
  border-right-width: 2px !important
}

[dir] .border-t-4 {
  border-top-width: 4px !important
}

[dir=ltr] .border-r-4 {
border-right-width: 4px !important
}

[dir=rtl] .border-r-4 {
  border-left-width: 4px !important
}

[dir] .border-b-4 {
  border-bottom-width: 4px !important
}

[dir=ltr] .border-l-4 {
border-left-width: 4px !important
}

[dir=rtl] .border-l-4 {
  border-right-width: 4px !important
}

[dir] .border-t-8 {
  border-top-width: 8px !important
}

[dir=ltr] .border-r-8 {
border-right-width: 8px !important
}

[dir=rtl] .border-r-8 {
  border-left-width: 8px !important
}

[dir] .border-b-8 {
  border-bottom-width: 8px !important
}

[dir=ltr] .border-l-8 {
border-left-width: 8px !important
}

[dir=rtl] .border-l-8 {
  border-right-width: 8px !important
}

[dir] .border-t {
  border-top-width: 1px !important
}

[dir=ltr] .border-r {
border-right-width: 1px !important
}

[dir=rtl] .border-r {
  border-left-width: 1px !important
}

[dir] .border-b {
  border-bottom-width: 1px !important
}

[dir=ltr] .border-l {
border-left-width: 1px !important
}

[dir=rtl] .border-l {
  border-right-width: 1px !important
}

[dir] .cursor-auto {
  cursor: auto !important
}

[dir] .cursor-default {
  cursor: default !important
}

[dir] .cursor-pointer {
  cursor: pointer !important
}

[dir] .cursor-wait {
  cursor: wait !important
}

[dir] .cursor-text {
  cursor: text !important
}

[dir] .cursor-move {
  cursor: move !important
}

[dir] .cursor-not-allowed {
  cursor: not-allowed !important
}

.block {
  display: block !important
}

.inline-block {
  display: inline-block !important
}

.inline {
  display: inline !important
}

.flex {
  display: -webkit-box !important;
  display: flex !important
}

.inline-flex {
  display: -webkit-inline-box !important;
  display: inline-flex !important
}

.table {
  display: table !important
}

.table-row {
  display: table-row !important
}

.table-cell {
  display: table-cell !important
}

.hidden {
  display: none !important
}

.flex-row {
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: normal !important;
          flex-direction: row !important
}

.flex-row-reverse {
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: reverse !important;
          flex-direction: row-reverse !important
}

.flex-col {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
          flex-direction: column !important
}

.flex-col-reverse {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: reverse !important;
          flex-direction: column-reverse !important
}

.flex-wrap {
  flex-wrap: wrap !important
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important
}

.flex-no-wrap {
  flex-wrap: nowrap !important
}

.items-start {
  -webkit-box-align: start !important;
          align-items: flex-start !important
}

.items-end {
  -webkit-box-align: end !important;
          align-items: flex-end !important
}

.items-center {
  -webkit-box-align: center !important;
          align-items: center !important
}

.items-baseline {
  -webkit-box-align: baseline !important;
          align-items: baseline !important
}

.items-stretch {
  -webkit-box-align: stretch !important;
          align-items: stretch !important
}

.self-auto {
  align-self: auto !important
}

.self-start {
  align-self: flex-start !important
}

.self-end {
  align-self: flex-end !important
}

.self-center {
  align-self: center !important
}

.self-stretch {
  align-self: stretch !important
}

.justify-start {
  -webkit-box-pack: start !important;
          justify-content: flex-start !important
}

.justify-end {
  -webkit-box-pack: end !important;
          justify-content: flex-end !important
}

.justify-center {
  -webkit-box-pack: center !important;
          justify-content: center !important
}

.justify-between {
  -webkit-box-pack: justify !important;
          justify-content: space-between !important
}

.justify-around {
  justify-content: space-around !important
}

.content-center {
  align-content: center !important
}

.content-start {
  align-content: flex-start !important
}

.content-end {
  align-content: flex-end !important
}

.content-between {
  align-content: space-between !important
}

.content-around {
  align-content: space-around !important
}

.flex-1 {
  -webkit-box-flex: 1 !important;
          flex: 1 1 0% !important
}

.flex-auto {
  -webkit-box-flex: 1 !important;
          flex: 1 1 auto !important
}

.flex-initial {
  -webkit-box-flex: 0 !important;
          flex: 0 1 auto !important
}

.flex-none {
  -webkit-box-flex: 0 !important;
          flex: none !important
}

.flex-grow-0 {
  -webkit-box-flex: 0 !important;
          flex-grow: 0 !important
}

.flex-grow {
  -webkit-box-flex: 1 !important;
          flex-grow: 1 !important
}

.flex-shrink-0 {
  flex-shrink: 0 !important
}

.flex-shrink {
  flex-shrink: 1 !important
}

.order-1 {
  -webkit-box-ordinal-group: 2 !important;
          order: 1 !important
}

.order-2 {
  -webkit-box-ordinal-group: 3 !important;
          order: 2 !important
}

.order-3 {
  -webkit-box-ordinal-group: 4 !important;
          order: 3 !important
}

.order-4 {
  -webkit-box-ordinal-group: 5 !important;
          order: 4 !important
}

.order-5 {
  -webkit-box-ordinal-group: 6 !important;
          order: 5 !important
}

.order-6 {
  -webkit-box-ordinal-group: 7 !important;
          order: 6 !important
}

.order-first {
  -webkit-box-ordinal-group: 0 !important;
          order: -1 !important
}

.order-last {
  -webkit-box-ordinal-group: 1000 !important;
          order: 999 !important
}

.order-normal {
  -webkit-box-ordinal-group: 1 !important;
          order: 0 !important
}

.hover\:order-1:hover {
  -webkit-box-ordinal-group: 2 !important;
          order: 1 !important
}

.hover\:order-2:hover {
  -webkit-box-ordinal-group: 3 !important;
          order: 2 !important
}

.hover\:order-3:hover {
  -webkit-box-ordinal-group: 4 !important;
          order: 3 !important
}

.hover\:order-4:hover {
  -webkit-box-ordinal-group: 5 !important;
          order: 4 !important
}

.hover\:order-5:hover {
  -webkit-box-ordinal-group: 6 !important;
          order: 5 !important
}

.hover\:order-6:hover {
  -webkit-box-ordinal-group: 7 !important;
          order: 6 !important
}

.hover\:order-first:hover {
  -webkit-box-ordinal-group: 0 !important;
          order: -1 !important
}

.hover\:order-last:hover {
  -webkit-box-ordinal-group: 1000 !important;
          order: 999 !important
}

.hover\:order-normal:hover {
  -webkit-box-ordinal-group: 1 !important;
          order: 0 !important
}

.focus\:order-1:focus {
  -webkit-box-ordinal-group: 2 !important;
          order: 1 !important
}

.focus\:order-2:focus {
  -webkit-box-ordinal-group: 3 !important;
          order: 2 !important
}

.focus\:order-3:focus {
  -webkit-box-ordinal-group: 4 !important;
          order: 3 !important
}

.focus\:order-4:focus {
  -webkit-box-ordinal-group: 5 !important;
          order: 4 !important
}

.focus\:order-5:focus {
  -webkit-box-ordinal-group: 6 !important;
          order: 5 !important
}

.focus\:order-6:focus {
  -webkit-box-ordinal-group: 7 !important;
          order: 6 !important
}

.focus\:order-first:focus {
  -webkit-box-ordinal-group: 0 !important;
          order: -1 !important
}

.focus\:order-last:focus {
  -webkit-box-ordinal-group: 1000 !important;
          order: 999 !important
}

.focus\:order-normal:focus {
  -webkit-box-ordinal-group: 1 !important;
          order: 0 !important
}

[dir=ltr] .float-right {
float: right !important
}

[dir=rtl] .float-right {
  float: left !important
}

[dir=ltr] .float-left {
float: left !important
}

[dir=rtl] .float-left {
  float: right !important
}

[dir] .float-none {
  float: none !important
}

.clearfix:after {
  content: "" !important;
  display: table !important
}

[dir] .clearfix:after {
  clear: both !important
}

.font-light {
  font-weight: 300 !important
}

.font-normal {
  font-weight: 400 !important
}

.font-medium {
  font-weight: 500 !important
}

.font-semibold {
  font-weight: 600 !important
}

.font-bold {
  font-weight: 700 !important
}

.font-extrabold {
  font-weight: 800 !important
}

.font-black {
  font-weight: 900 !important
}

.hover\:font-light:hover {
  font-weight: 300 !important
}

.hover\:font-normal:hover {
  font-weight: 400 !important
}

.hover\:font-medium:hover {
  font-weight: 500 !important
}

.hover\:font-semibold:hover {
  font-weight: 600 !important
}

.hover\:font-bold:hover {
  font-weight: 700 !important
}

.hover\:font-extrabold:hover {
  font-weight: 800 !important
}

.hover\:font-black:hover {
  font-weight: 900 !important
}

.h-1 {
  height: 0.25rem !important
}

.h-2 {
  height: 0.5rem !important
}

.h-3 {
  height: 0.75rem !important
}

.h-4 {
  height: 1rem !important
}

.h-5 {
  height: 1.25rem !important
}

.h-6 {
  height: 1.5rem !important
}

.h-8 {
  height: 2rem !important
}

.h-10 {
  height: 2.5rem !important
}

.h-12 {
  height: 3rem !important
}

.h-16 {
  height: 4rem !important
}

.h-24 {
  height: 6rem !important
}

.h-32 {
  height: 8rem !important
}

.h-48 {
  height: 12rem !important
}

.h-64 {
  height: 16rem !important
}

.h-auto {
  height: auto !important
}

.h-px {
  height: 1px !important
}

.h-full {
  height: 100% !important
}

.h-screen {
  height: 100vh !important
}

.leading-none {
  line-height: 1 !important
}

.leading-tight {
  line-height: 1.25 !important
}

.leading-normal {
  line-height: 1.5 !important
}

.leading-loose {
  line-height: 2 !important
}

.list-inside {
  list-style-position: inside !important
}

.list-outside {
  list-style-position: outside !important
}

[dir] .m-0 {
  margin: 0 !important
}

[dir] .m-1 {
  margin: 0.25rem !important
}

[dir] .m-2 {
  margin: 0.5rem !important
}

[dir] .m-3 {
  margin: 0.75rem !important
}

[dir] .m-4 {
  margin: 1rem !important
}

[dir] .m-5 {
  margin: 1.25rem !important
}

[dir] .m-6 {
  margin: 1.5rem !important
}

[dir] .m-8 {
  margin: 2rem !important
}

[dir] .m-10 {
  margin: 2.5rem !important
}

[dir] .m-12 {
  margin: 3rem !important
}

[dir] .m-16 {
  margin: 4rem !important
}

[dir] .m-20 {
  margin: 5rem !important
}

[dir] .m-24 {
  margin: 6rem !important
}

[dir] .m-32 {
  margin: 8rem !important
}

[dir] .m-auto {
  margin: auto !important
}

[dir] .m-px {
  margin: 1px !important
}

[dir] .m-base {
  margin: 2.2rem !important
}

[dir] .-m-px {
  margin: -1px !important
}

[dir] .-m-1 {
  margin: -0.25rem !important
}

[dir] .-m-2 {
  margin: -0.5rem !important
}

[dir] .-m-3 {
  margin: -0.75rem !important
}

[dir] .-m-4 {
  margin: -1rem !important
}

[dir] .my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important
}

[dir=ltr] .mx-0 {
margin-left: 0 !important;
margin-right: 0 !important
}

[dir=rtl] .mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important
}

[dir] .my-1 {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important
}

[dir=ltr] .mx-1 {
margin-left: 0.25rem !important;
margin-right: 0.25rem !important
}

[dir=rtl] .mx-1 {
  margin-right: 0.25rem !important;
  margin-left: 0.25rem !important
}

[dir] .my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important
}

[dir=ltr] .mx-2 {
margin-left: 0.5rem !important;
margin-right: 0.5rem !important
}

[dir=rtl] .mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important
}

[dir] .my-3 {
  margin-top: 0.75rem !important;
  margin-bottom: 0.75rem !important
}

[dir=ltr] .mx-3 {
margin-left: 0.75rem !important;
margin-right: 0.75rem !important
}

[dir=rtl] .mx-3 {
  margin-right: 0.75rem !important;
  margin-left: 0.75rem !important
}

[dir] .my-4 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important
}

[dir=ltr] .mx-4 {
margin-left: 1rem !important;
margin-right: 1rem !important
}

[dir=rtl] .mx-4 {
  margin-right: 1rem !important;
  margin-left: 1rem !important
}

[dir] .my-5 {
  margin-top: 1.25rem !important;
  margin-bottom: 1.25rem !important
}

[dir=ltr] .mx-5 {
margin-left: 1.25rem !important;
margin-right: 1.25rem !important
}

[dir=rtl] .mx-5 {
  margin-right: 1.25rem !important;
  margin-left: 1.25rem !important
}

[dir] .my-6 {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important
}

[dir=ltr] .mx-6 {
margin-left: 1.5rem !important;
margin-right: 1.5rem !important
}

[dir=rtl] .mx-6 {
  margin-right: 1.5rem !important;
  margin-left: 1.5rem !important
}

[dir] .my-8 {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important
}

[dir=ltr] .mx-8 {
margin-left: 2rem !important;
margin-right: 2rem !important
}

[dir=rtl] .mx-8 {
  margin-right: 2rem !important;
  margin-left: 2rem !important
}

[dir] .my-10 {
  margin-top: 2.5rem !important;
  margin-bottom: 2.5rem !important
}

[dir=ltr] .mx-10 {
margin-left: 2.5rem !important;
margin-right: 2.5rem !important
}

[dir=rtl] .mx-10 {
  margin-right: 2.5rem !important;
  margin-left: 2.5rem !important
}

[dir] .my-12 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important
}

[dir=ltr] .mx-12 {
margin-left: 3rem !important;
margin-right: 3rem !important
}

[dir=rtl] .mx-12 {
  margin-right: 3rem !important;
  margin-left: 3rem !important
}

[dir] .my-16 {
  margin-top: 4rem !important;
  margin-bottom: 4rem !important
}

[dir=ltr] .mx-16 {
margin-left: 4rem !important;
margin-right: 4rem !important
}

[dir=rtl] .mx-16 {
  margin-right: 4rem !important;
  margin-left: 4rem !important
}

[dir] .my-20 {
  margin-top: 5rem !important;
  margin-bottom: 5rem !important
}

[dir=ltr] .mx-20 {
margin-left: 5rem !important;
margin-right: 5rem !important
}

[dir=rtl] .mx-20 {
  margin-right: 5rem !important;
  margin-left: 5rem !important
}

[dir] .my-24 {
  margin-top: 6rem !important;
  margin-bottom: 6rem !important
}

[dir=ltr] .mx-24 {
margin-left: 6rem !important;
margin-right: 6rem !important
}

[dir=rtl] .mx-24 {
  margin-right: 6rem !important;
  margin-left: 6rem !important
}

[dir] .my-32 {
  margin-top: 8rem !important;
  margin-bottom: 8rem !important
}

[dir=ltr] .mx-32 {
margin-left: 8rem !important;
margin-right: 8rem !important
}

[dir=rtl] .mx-32 {
  margin-right: 8rem !important;
  margin-left: 8rem !important
}

[dir] .my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important
}

[dir=ltr] .mx-auto {
margin-left: auto !important;
margin-right: auto !important
}

[dir=rtl] .mx-auto {
  margin-right: auto !important;
  margin-left: auto !important
}

[dir] .my-px {
  margin-top: 1px !important;
  margin-bottom: 1px !important
}

[dir=ltr] .mx-px {
margin-left: 1px !important;
margin-right: 1px !important
}

[dir=rtl] .mx-px {
  margin-right: 1px !important;
  margin-left: 1px !important
}

[dir] .my-base {
  margin-top: 2.2rem !important;
  margin-bottom: 2.2rem !important
}

[dir=ltr] .mx-base {
margin-left: 2.2rem !important;
margin-right: 2.2rem !important
}

[dir=rtl] .mx-base {
  margin-right: 2.2rem !important;
  margin-left: 2.2rem !important
}

[dir] .-my-px {
  margin-top: -1px !important;
  margin-bottom: -1px !important
}

[dir=ltr] .-mx-px {
margin-left: -1px !important;
margin-right: -1px !important
}

[dir=rtl] .-mx-px {
  margin-right: -1px !important;
  margin-left: -1px !important
}

[dir] .-my-1 {
  margin-top: -0.25rem !important;
  margin-bottom: -0.25rem !important
}

[dir=ltr] .-mx-1 {
margin-left: -0.25rem !important;
margin-right: -0.25rem !important
}

[dir=rtl] .-mx-1 {
  margin-right: -0.25rem !important;
  margin-left: -0.25rem !important
}

[dir] .-my-2 {
  margin-top: -0.5rem !important;
  margin-bottom: -0.5rem !important
}

[dir=ltr] .-mx-2 {
margin-left: -0.5rem !important;
margin-right: -0.5rem !important
}

[dir=rtl] .-mx-2 {
  margin-right: -0.5rem !important;
  margin-left: -0.5rem !important
}

[dir] .-my-3 {
  margin-top: -0.75rem !important;
  margin-bottom: -0.75rem !important
}

[dir=ltr] .-mx-3 {
margin-left: -0.75rem !important;
margin-right: -0.75rem !important
}

[dir=rtl] .-mx-3 {
  margin-right: -0.75rem !important;
  margin-left: -0.75rem !important
}

[dir] .-my-4 {
  margin-top: -1rem !important;
  margin-bottom: -1rem !important
}

[dir=ltr] .-mx-4 {
margin-left: -1rem !important;
margin-right: -1rem !important
}

[dir=rtl] .-mx-4 {
  margin-right: -1rem !important;
  margin-left: -1rem !important
}

[dir] .mt-0 {
  margin-top: 0 !important
}

[dir=ltr] .mr-0 {
margin-right: 0 !important
}

[dir=rtl] .mr-0 {
  margin-left: 0 !important
}

[dir] .mb-0 {
  margin-bottom: 0 !important
}

[dir=ltr] .ml-0 {
margin-left: 0 !important
}

[dir=rtl] .ml-0 {
  margin-right: 0 !important
}

[dir] .mt-1 {
  margin-top: 0.25rem !important
}

[dir=ltr] .mr-1 {
margin-right: 0.25rem !important
}

[dir=rtl] .mr-1 {
  margin-left: 0.25rem !important
}

[dir] .mb-1 {
  margin-bottom: 0.25rem !important
}

[dir=ltr] .ml-1 {
margin-left: 0.25rem !important
}

[dir=rtl] .ml-1 {
  margin-right: 0.25rem !important
}

[dir] .mt-2 {
  margin-top: 0.5rem !important
}

[dir=ltr] .mr-2 {
margin-right: 0.5rem !important
}

[dir=rtl] .mr-2 {
  margin-left: 0.5rem !important
}

[dir] .mb-2 {
  margin-bottom: 0.5rem !important
}

[dir=ltr] .ml-2 {
margin-left: 0.5rem !important
}

[dir=rtl] .ml-2 {
  margin-right: 0.5rem !important
}

[dir] .mt-3 {
  margin-top: 0.75rem !important
}

[dir=ltr] .mr-3 {
margin-right: 0.75rem !important
}

[dir=rtl] .mr-3 {
  margin-left: 0.75rem !important
}

[dir] .mb-3 {
  margin-bottom: 0.75rem !important
}

[dir=ltr] .ml-3 {
margin-left: 0.75rem !important
}

[dir=rtl] .ml-3 {
  margin-right: 0.75rem !important
}

[dir] .mt-4 {
  margin-top: 1rem !important
}

[dir=ltr] .mr-4 {
margin-right: 1rem !important
}

[dir=rtl] .mr-4 {
  margin-left: 1rem !important
}

[dir] .mb-4 {
  margin-bottom: 1rem !important
}

[dir=ltr] .ml-4 {
margin-left: 1rem !important
}

[dir=rtl] .ml-4 {
  margin-right: 1rem !important
}

[dir] .mt-5 {
  margin-top: 1.25rem !important
}

[dir=ltr] .mr-5 {
margin-right: 1.25rem !important
}

[dir=rtl] .mr-5 {
  margin-left: 1.25rem !important
}

[dir] .mb-5 {
  margin-bottom: 1.25rem !important
}

[dir=ltr] .ml-5 {
margin-left: 1.25rem !important
}

[dir=rtl] .ml-5 {
  margin-right: 1.25rem !important
}

[dir] .mt-6 {
  margin-top: 1.5rem !important
}

[dir=ltr] .mr-6 {
margin-right: 1.5rem !important
}

[dir=rtl] .mr-6 {
  margin-left: 1.5rem !important
}

[dir] .mb-6 {
  margin-bottom: 1.5rem !important
}

[dir=ltr] .ml-6 {
margin-left: 1.5rem !important
}

[dir=rtl] .ml-6 {
  margin-right: 1.5rem !important
}

[dir] .mt-8 {
  margin-top: 2rem !important
}

[dir=ltr] .mr-8 {
margin-right: 2rem !important
}

[dir=rtl] .mr-8 {
  margin-left: 2rem !important
}

[dir] .mb-8 {
  margin-bottom: 2rem !important
}

[dir=ltr] .ml-8 {
margin-left: 2rem !important
}

[dir=rtl] .ml-8 {
  margin-right: 2rem !important
}

[dir] .mt-10 {
  margin-top: 2.5rem !important
}

[dir=ltr] .mr-10 {
margin-right: 2.5rem !important
}

[dir=rtl] .mr-10 {
  margin-left: 2.5rem !important
}

[dir] .mb-10 {
  margin-bottom: 2.5rem !important
}

[dir=ltr] .ml-10 {
margin-left: 2.5rem !important
}

[dir=rtl] .ml-10 {
  margin-right: 2.5rem !important
}

[dir] .mt-12 {
  margin-top: 3rem !important
}

[dir=ltr] .mr-12 {
margin-right: 3rem !important
}

[dir=rtl] .mr-12 {
  margin-left: 3rem !important
}

[dir] .mb-12 {
  margin-bottom: 3rem !important
}

[dir=ltr] .ml-12 {
margin-left: 3rem !important
}

[dir=rtl] .ml-12 {
  margin-right: 3rem !important
}

[dir] .mt-16 {
  margin-top: 4rem !important
}

[dir=ltr] .mr-16 {
margin-right: 4rem !important
}

[dir=rtl] .mr-16 {
  margin-left: 4rem !important
}

[dir] .mb-16 {
  margin-bottom: 4rem !important
}

[dir=ltr] .ml-16 {
margin-left: 4rem !important
}

[dir=rtl] .ml-16 {
  margin-right: 4rem !important
}

[dir] .mt-20 {
  margin-top: 5rem !important
}

[dir=ltr] .mr-20 {
margin-right: 5rem !important
}

[dir=rtl] .mr-20 {
  margin-left: 5rem !important
}

[dir] .mb-20 {
  margin-bottom: 5rem !important
}

[dir=ltr] .ml-20 {
margin-left: 5rem !important
}

[dir=rtl] .ml-20 {
  margin-right: 5rem !important
}

[dir] .mt-24 {
  margin-top: 6rem !important
}

[dir=ltr] .mr-24 {
margin-right: 6rem !important
}

[dir=rtl] .mr-24 {
  margin-left: 6rem !important
}

[dir] .mb-24 {
  margin-bottom: 6rem !important
}

[dir=ltr] .ml-24 {
margin-left: 6rem !important
}

[dir=rtl] .ml-24 {
  margin-right: 6rem !important
}

[dir] .mt-32 {
  margin-top: 8rem !important
}

[dir=ltr] .mr-32 {
margin-right: 8rem !important
}

[dir=rtl] .mr-32 {
  margin-left: 8rem !important
}

[dir] .mb-32 {
  margin-bottom: 8rem !important
}

[dir=ltr] .ml-32 {
margin-left: 8rem !important
}

[dir=rtl] .ml-32 {
  margin-right: 8rem !important
}

[dir] .mt-auto {
  margin-top: auto !important
}

[dir=ltr] .mr-auto {
margin-right: auto !important
}

[dir=rtl] .mr-auto {
  margin-left: auto !important
}

[dir] .mb-auto {
  margin-bottom: auto !important
}

[dir=ltr] .ml-auto {
margin-left: auto !important
}

[dir=rtl] .ml-auto {
  margin-right: auto !important
}

[dir] .mt-px {
  margin-top: 1px !important
}

[dir=ltr] .mr-px {
margin-right: 1px !important
}

[dir=rtl] .mr-px {
  margin-left: 1px !important
}

[dir] .mb-px {
  margin-bottom: 1px !important
}

[dir=ltr] .ml-px {
margin-left: 1px !important
}

[dir=rtl] .ml-px {
  margin-right: 1px !important
}

[dir] .mt-base {
  margin-top: 2.2rem !important
}

[dir=ltr] .mr-base {
margin-right: 2.2rem !important
}

[dir=rtl] .mr-base {
  margin-left: 2.2rem !important
}

[dir] .mb-base {
  margin-bottom: 2.2rem !important
}

[dir=ltr] .ml-base {
margin-left: 2.2rem !important
}

[dir=rtl] .ml-base {
  margin-right: 2.2rem !important
}

[dir] .-mt-px {
  margin-top: -1px !important
}

[dir=ltr] .-mr-px {
margin-right: -1px !important
}

[dir=rtl] .-mr-px {
  margin-left: -1px !important
}

[dir] .-mb-px {
  margin-bottom: -1px !important
}

[dir=ltr] .-ml-px {
margin-left: -1px !important
}

[dir=rtl] .-ml-px {
  margin-right: -1px !important
}

[dir] .-mt-1 {
  margin-top: -0.25rem !important
}

[dir=ltr] .-mr-1 {
margin-right: -0.25rem !important
}

[dir=rtl] .-mr-1 {
  margin-left: -0.25rem !important
}

[dir] .-mb-1 {
  margin-bottom: -0.25rem !important
}

[dir=ltr] .-ml-1 {
margin-left: -0.25rem !important
}

[dir=rtl] .-ml-1 {
  margin-right: -0.25rem !important
}

[dir] .-mt-2 {
  margin-top: -0.5rem !important
}

[dir=ltr] .-mr-2 {
margin-right: -0.5rem !important
}

[dir=rtl] .-mr-2 {
  margin-left: -0.5rem !important
}

[dir] .-mb-2 {
  margin-bottom: -0.5rem !important
}

[dir=ltr] .-ml-2 {
margin-left: -0.5rem !important
}

[dir=rtl] .-ml-2 {
  margin-right: -0.5rem !important
}

[dir] .-mt-3 {
  margin-top: -0.75rem !important
}

[dir=ltr] .-mr-3 {
margin-right: -0.75rem !important
}

[dir=rtl] .-mr-3 {
  margin-left: -0.75rem !important
}

[dir] .-mb-3 {
  margin-bottom: -0.75rem !important
}

[dir=ltr] .-ml-3 {
margin-left: -0.75rem !important
}

[dir=rtl] .-ml-3 {
  margin-right: -0.75rem !important
}

[dir] .-mt-4 {
  margin-top: -1rem !important
}

[dir=ltr] .-mr-4 {
margin-right: -1rem !important
}

[dir=rtl] .-mr-4 {
  margin-left: -1rem !important
}

[dir] .-mb-4 {
  margin-bottom: -1rem !important
}

[dir=ltr] .-ml-4 {
margin-left: -1rem !important
}

[dir=rtl] .-ml-4 {
  margin-right: -1rem !important
}

.max-h-full {
  max-height: 100% !important
}

.max-h-screen {
  max-height: 100vh !important
}

.max-w-xs {
  max-width: 20rem !important
}

.max-w-sm {
  max-width: 30rem !important
}

.max-w-md {
  max-width: 40rem !important
}

.max-w-lg {
  max-width: 50rem !important
}

.max-w-xl {
  max-width: 60rem !important
}

.max-w-2xl {
  max-width: 70rem !important
}

.max-w-3xl {
  max-width: 80rem !important
}

.max-w-4xl {
  max-width: 90rem !important
}

.max-w-5xl {
  max-width: 100rem !important
}

.max-w-full {
  max-width: 100% !important
}

.min-h-0 {
  min-height: 0 !important
}

.min-h-full {
  min-height: 100% !important
}

.min-h-screen {
  min-height: 100vh !important
}

.min-w-0 {
  min-width: 0 !important
}

.min-w-full {
  min-width: 100% !important
}

.object-contain {
  -o-object-fit: contain !important;
     object-fit: contain !important
}

.object-cover {
  -o-object-fit: cover !important;
     object-fit: cover !important
}

.object-fill {
  -o-object-fit: fill !important;
     object-fit: fill !important
}

.object-none {
  -o-object-fit: none !important;
     object-fit: none !important
}

.object-scale-down {
  -o-object-fit: scale-down !important;
     object-fit: scale-down !important
}

.object-bottom {
  -o-object-position: bottom !important;
     object-position: bottom !important
}

.object-center {
  -o-object-position: center !important;
     object-position: center !important
}

.object-left {
  -o-object-position: left !important;
     object-position: left !important
}

.object-left-bottom {
  -o-object-position: left bottom !important;
     object-position: left bottom !important
}

.object-left-top {
  -o-object-position: left top !important;
     object-position: left top !important
}

.object-right {
  -o-object-position: right !important;
     object-position: right !important
}

.object-right-bottom {
  -o-object-position: right bottom !important;
     object-position: right bottom !important
}

.object-right-top {
  -o-object-position: right top !important;
     object-position: right top !important
}

.object-top {
  -o-object-position: top !important;
     object-position: top !important
}

.opacity-0 {
  opacity: 0 !important
}

.opacity-25 {
  opacity: 0.25 !important
}

.opacity-50 {
  opacity: 0.5 !important
}

.opacity-75 {
  opacity: 0.75 !important
}

.opacity-100 {
  opacity: 1 !important
}

.outline-none {
  outline: 0 !important
}

.focus\:outline-none:focus {
  outline: 0 !important
}

.overflow-auto {
  overflow: auto !important
}

.overflow-hidden {
  overflow: hidden !important
}

.overflow-visible {
  overflow: visible !important
}

.overflow-scroll {
  overflow: scroll !important
}

.overflow-x-auto {
  overflow-x: auto !important
}

.overflow-y-auto {
  overflow-y: auto !important
}

.overflow-x-hidden {
  overflow-x: hidden !important
}

.overflow-y-hidden {
  overflow-y: hidden !important
}

.overflow-x-visible {
  overflow-x: visible !important
}

.overflow-y-visible {
  overflow-y: visible !important
}

.overflow-x-scroll {
  overflow-x: scroll !important
}

.overflow-y-scroll {
  overflow-y: scroll !important
}

.scrolling-touch {
  -webkit-overflow-scrolling: touch !important
}

.scrolling-auto {
  -webkit-overflow-scrolling: auto !important
}

[dir] .p-0 {
  padding: 0 !important
}

[dir] .p-1 {
  padding: 0.25rem !important
}

[dir] .p-2 {
  padding: 0.5rem !important
}

[dir] .p-3 {
  padding: 0.75rem !important
}

[dir] .p-4 {
  padding: 1rem !important
}

[dir] .p-5 {
  padding: 1.25rem !important
}

[dir] .p-6 {
  padding: 1.5rem !important
}

[dir] .p-8 {
  padding: 2rem !important
}

[dir] .p-10 {
  padding: 2.5rem !important
}

[dir] .p-12 {
  padding: 3rem !important
}

[dir] .p-16 {
  padding: 4rem !important
}

[dir] .p-20 {
  padding: 5rem !important
}

[dir] .p-24 {
  padding: 6rem !important
}

[dir] .p-32 {
  padding: 8rem !important
}

[dir] .p-px {
  padding: 1px !important
}

[dir] .p-base {
  padding: 2.2rem !important
}

[dir] .py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important
}

[dir=ltr] .px-0 {
padding-left: 0 !important;
padding-right: 0 !important
}

[dir=rtl] .px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important
}

[dir] .py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important
}

[dir=ltr] .px-1 {
padding-left: 0.25rem !important;
padding-right: 0.25rem !important
}

[dir=rtl] .px-1 {
  padding-right: 0.25rem !important;
  padding-left: 0.25rem !important
}

[dir] .py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important
}

[dir=ltr] .px-2 {
padding-left: 0.5rem !important;
padding-right: 0.5rem !important
}

[dir=rtl] .px-2 {
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important
}

[dir] .py-3 {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important
}

[dir=ltr] .px-3 {
padding-left: 0.75rem !important;
padding-right: 0.75rem !important
}

[dir=rtl] .px-3 {
  padding-right: 0.75rem !important;
  padding-left: 0.75rem !important
}

[dir] .py-4 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important
}

[dir=ltr] .px-4 {
padding-left: 1rem !important;
padding-right: 1rem !important
}

[dir=rtl] .px-4 {
  padding-right: 1rem !important;
  padding-left: 1rem !important
}

[dir] .py-5 {
  padding-top: 1.25rem !important;
  padding-bottom: 1.25rem !important
}

[dir=ltr] .px-5 {
padding-left: 1.25rem !important;
padding-right: 1.25rem !important
}

[dir=rtl] .px-5 {
  padding-right: 1.25rem !important;
  padding-left: 1.25rem !important
}

[dir] .py-6 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important
}

[dir=ltr] .px-6 {
padding-left: 1.5rem !important;
padding-right: 1.5rem !important
}

[dir=rtl] .px-6 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important
}

[dir] .py-8 {
  padding-top: 2rem !important;
  padding-bottom: 2rem !important
}

[dir=ltr] .px-8 {
padding-left: 2rem !important;
padding-right: 2rem !important
}

[dir=rtl] .px-8 {
  padding-right: 2rem !important;
  padding-left: 2rem !important
}

[dir] .py-10 {
  padding-top: 2.5rem !important;
  padding-bottom: 2.5rem !important
}

[dir=ltr] .px-10 {
padding-left: 2.5rem !important;
padding-right: 2.5rem !important
}

[dir=rtl] .px-10 {
  padding-right: 2.5rem !important;
  padding-left: 2.5rem !important
}

[dir] .py-12 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important
}

[dir=ltr] .px-12 {
padding-left: 3rem !important;
padding-right: 3rem !important
}

[dir=rtl] .px-12 {
  padding-right: 3rem !important;
  padding-left: 3rem !important
}

[dir] .py-16 {
  padding-top: 4rem !important;
  padding-bottom: 4rem !important
}

[dir=ltr] .px-16 {
padding-left: 4rem !important;
padding-right: 4rem !important
}

[dir=rtl] .px-16 {
  padding-right: 4rem !important;
  padding-left: 4rem !important
}

[dir] .py-20 {
  padding-top: 5rem !important;
  padding-bottom: 5rem !important
}

[dir=ltr] .px-20 {
padding-left: 5rem !important;
padding-right: 5rem !important
}

[dir=rtl] .px-20 {
  padding-right: 5rem !important;
  padding-left: 5rem !important
}

[dir] .py-24 {
  padding-top: 6rem !important;
  padding-bottom: 6rem !important
}

[dir=ltr] .px-24 {
padding-left: 6rem !important;
padding-right: 6rem !important
}

[dir=rtl] .px-24 {
  padding-right: 6rem !important;
  padding-left: 6rem !important
}

[dir] .py-32 {
  padding-top: 8rem !important;
  padding-bottom: 8rem !important
}

[dir=ltr] .px-32 {
padding-left: 8rem !important;
padding-right: 8rem !important
}

[dir=rtl] .px-32 {
  padding-right: 8rem !important;
  padding-left: 8rem !important
}

[dir] .py-px {
  padding-top: 1px !important;
  padding-bottom: 1px !important
}

[dir=ltr] .px-px {
padding-left: 1px !important;
padding-right: 1px !important
}

[dir=rtl] .px-px {
  padding-right: 1px !important;
  padding-left: 1px !important
}

[dir] .py-base {
  padding-top: 2.2rem !important;
  padding-bottom: 2.2rem !important
}

[dir=ltr] .px-base {
padding-left: 2.2rem !important;
padding-right: 2.2rem !important
}

[dir=rtl] .px-base {
  padding-right: 2.2rem !important;
  padding-left: 2.2rem !important
}

[dir] .pt-0 {
  padding-top: 0 !important
}

[dir=ltr] .pr-0 {
padding-right: 0 !important
}

[dir=rtl] .pr-0 {
  padding-left: 0 !important
}

[dir] .pb-0 {
  padding-bottom: 0 !important
}

[dir=ltr] .pl-0 {
padding-left: 0 !important
}

[dir=rtl] .pl-0 {
  padding-right: 0 !important
}

[dir] .pt-1 {
  padding-top: 0.25rem !important
}

[dir=ltr] .pr-1 {
padding-right: 0.25rem !important
}

[dir=rtl] .pr-1 {
  padding-left: 0.25rem !important
}

[dir] .pb-1 {
  padding-bottom: 0.25rem !important
}

[dir=ltr] .pl-1 {
padding-left: 0.25rem !important
}

[dir=rtl] .pl-1 {
  padding-right: 0.25rem !important
}

[dir] .pt-2 {
  padding-top: 0.5rem !important
}

[dir=ltr] .pr-2 {
padding-right: 0.5rem !important
}

[dir=rtl] .pr-2 {
  padding-left: 0.5rem !important
}

[dir] .pb-2 {
  padding-bottom: 0.5rem !important
}

[dir=ltr] .pl-2 {
padding-left: 0.5rem !important
}

[dir=rtl] .pl-2 {
  padding-right: 0.5rem !important
}

[dir] .pt-3 {
  padding-top: 0.75rem !important
}

[dir=ltr] .pr-3 {
padding-right: 0.75rem !important
}

[dir=rtl] .pr-3 {
  padding-left: 0.75rem !important
}

[dir] .pb-3 {
  padding-bottom: 0.75rem !important
}

[dir=ltr] .pl-3 {
padding-left: 0.75rem !important
}

[dir=rtl] .pl-3 {
  padding-right: 0.75rem !important
}

[dir] .pt-4 {
  padding-top: 1rem !important
}

[dir=ltr] .pr-4 {
padding-right: 1rem !important
}

[dir=rtl] .pr-4 {
  padding-left: 1rem !important
}

[dir] .pb-4 {
  padding-bottom: 1rem !important
}

[dir=ltr] .pl-4 {
padding-left: 1rem !important
}

[dir=rtl] .pl-4 {
  padding-right: 1rem !important
}

[dir] .pt-5 {
  padding-top: 1.25rem !important
}

[dir=ltr] .pr-5 {
padding-right: 1.25rem !important
}

[dir=rtl] .pr-5 {
  padding-left: 1.25rem !important
}

[dir] .pb-5 {
  padding-bottom: 1.25rem !important
}

[dir=ltr] .pl-5 {
padding-left: 1.25rem !important
}

[dir=rtl] .pl-5 {
  padding-right: 1.25rem !important
}

[dir] .pt-6 {
  padding-top: 1.5rem !important
}

[dir=ltr] .pr-6 {
padding-right: 1.5rem !important
}

[dir=rtl] .pr-6 {
  padding-left: 1.5rem !important
}

[dir] .pb-6 {
  padding-bottom: 1.5rem !important
}

[dir=ltr] .pl-6 {
padding-left: 1.5rem !important
}

[dir=rtl] .pl-6 {
  padding-right: 1.5rem !important
}

[dir] .pt-8 {
  padding-top: 2rem !important
}

[dir=ltr] .pr-8 {
padding-right: 2rem !important
}

[dir=rtl] .pr-8 {
  padding-left: 2rem !important
}

[dir] .pb-8 {
  padding-bottom: 2rem !important
}

[dir=ltr] .pl-8 {
padding-left: 2rem !important
}

[dir=rtl] .pl-8 {
  padding-right: 2rem !important
}

[dir] .pt-10 {
  padding-top: 2.5rem !important
}

[dir=ltr] .pr-10 {
padding-right: 2.5rem !important
}

[dir=rtl] .pr-10 {
  padding-left: 2.5rem !important
}

[dir] .pb-10 {
  padding-bottom: 2.5rem !important
}

[dir=ltr] .pl-10 {
padding-left: 2.5rem !important
}

[dir=rtl] .pl-10 {
  padding-right: 2.5rem !important
}

[dir] .pt-12 {
  padding-top: 3rem !important
}

[dir=ltr] .pr-12 {
padding-right: 3rem !important
}

[dir=rtl] .pr-12 {
  padding-left: 3rem !important
}

[dir] .pb-12 {
  padding-bottom: 3rem !important
}

[dir=ltr] .pl-12 {
padding-left: 3rem !important
}

[dir=rtl] .pl-12 {
  padding-right: 3rem !important
}

[dir] .pt-16 {
  padding-top: 4rem !important
}

[dir=ltr] .pr-16 {
padding-right: 4rem !important
}

[dir=rtl] .pr-16 {
  padding-left: 4rem !important
}

[dir] .pb-16 {
  padding-bottom: 4rem !important
}

[dir=ltr] .pl-16 {
padding-left: 4rem !important
}

[dir=rtl] .pl-16 {
  padding-right: 4rem !important
}

[dir] .pt-20 {
  padding-top: 5rem !important
}

[dir=ltr] .pr-20 {
padding-right: 5rem !important
}

[dir=rtl] .pr-20 {
  padding-left: 5rem !important
}

[dir] .pb-20 {
  padding-bottom: 5rem !important
}

[dir=ltr] .pl-20 {
padding-left: 5rem !important
}

[dir=rtl] .pl-20 {
  padding-right: 5rem !important
}

[dir] .pt-24 {
  padding-top: 6rem !important
}

[dir=ltr] .pr-24 {
padding-right: 6rem !important
}

[dir=rtl] .pr-24 {
  padding-left: 6rem !important
}

[dir] .pb-24 {
  padding-bottom: 6rem !important
}

[dir=ltr] .pl-24 {
padding-left: 6rem !important
}

[dir=rtl] .pl-24 {
  padding-right: 6rem !important
}

[dir] .pt-32 {
  padding-top: 8rem !important
}

[dir=ltr] .pr-32 {
padding-right: 8rem !important
}

[dir=rtl] .pr-32 {
  padding-left: 8rem !important
}

[dir] .pb-32 {
  padding-bottom: 8rem !important
}

[dir=ltr] .pl-32 {
padding-left: 8rem !important
}

[dir=rtl] .pl-32 {
  padding-right: 8rem !important
}

[dir] .pt-px {
  padding-top: 1px !important
}

[dir=ltr] .pr-px {
padding-right: 1px !important
}

[dir=rtl] .pr-px {
  padding-left: 1px !important
}

[dir] .pb-px {
  padding-bottom: 1px !important
}

[dir=ltr] .pl-px {
padding-left: 1px !important
}

[dir=rtl] .pl-px {
  padding-right: 1px !important
}

[dir] .pt-base {
  padding-top: 2.2rem !important
}

[dir=ltr] .pr-base {
padding-right: 2.2rem !important
}

[dir=rtl] .pr-base {
  padding-left: 2.2rem !important
}

[dir] .pb-base {
  padding-bottom: 2.2rem !important
}

[dir=ltr] .pl-base {
padding-left: 2.2rem !important
}

[dir=rtl] .pl-base {
  padding-right: 2.2rem !important
}

.placeholder-transparent::-webkit-input-placeholder {
  color: transparent !important
}

.placeholder-transparent::-moz-placeholder {
  color: transparent !important
}

.placeholder-transparent:-ms-input-placeholder {
  color: transparent !important
}

.placeholder-transparent::-ms-input-placeholder {
  color: transparent !important
}

.placeholder-transparent::placeholder {
  color: transparent !important
}

.placeholder-black::-webkit-input-placeholder {
  color: #22292f !important
}

.placeholder-black::-moz-placeholder {
  color: #22292f !important
}

.placeholder-black:-ms-input-placeholder {
  color: #22292f !important
}

.placeholder-black::-ms-input-placeholder {
  color: #22292f !important
}

.placeholder-black::placeholder {
  color: #22292f !important
}

.placeholder-white::-webkit-input-placeholder {
  color: #ffffff !important
}

.placeholder-white::-moz-placeholder {
  color: #ffffff !important
}

.placeholder-white:-ms-input-placeholder {
  color: #ffffff !important
}

.placeholder-white::-ms-input-placeholder {
  color: #ffffff !important
}

.placeholder-white::placeholder {
  color: #ffffff !important
}

.placeholder-grey::-webkit-input-placeholder {
  color: #b8c2cc !important
}

.placeholder-grey::-moz-placeholder {
  color: #b8c2cc !important
}

.placeholder-grey:-ms-input-placeholder {
  color: #b8c2cc !important
}

.placeholder-grey::-ms-input-placeholder {
  color: #b8c2cc !important
}

.placeholder-grey::placeholder {
  color: #b8c2cc !important
}

.placeholder-grey-light::-webkit-input-placeholder {
  color: #dae1e7 !important
}

.placeholder-grey-light::-moz-placeholder {
  color: #dae1e7 !important
}

.placeholder-grey-light:-ms-input-placeholder {
  color: #dae1e7 !important
}

.placeholder-grey-light::-ms-input-placeholder {
  color: #dae1e7 !important
}

.placeholder-grey-light::placeholder {
  color: #dae1e7 !important
}

.focus\:placeholder-transparent:focus::-webkit-input-placeholder {
  color: transparent !important
}

.focus\:placeholder-transparent:focus::-moz-placeholder {
  color: transparent !important
}

.focus\:placeholder-transparent:focus:-ms-input-placeholder {
  color: transparent !important
}

.focus\:placeholder-transparent:focus::-ms-input-placeholder {
  color: transparent !important
}

.focus\:placeholder-transparent:focus::placeholder {
  color: transparent !important
}

.focus\:placeholder-black:focus::-webkit-input-placeholder {
  color: #22292f !important
}

.focus\:placeholder-black:focus::-moz-placeholder {
  color: #22292f !important
}

.focus\:placeholder-black:focus:-ms-input-placeholder {
  color: #22292f !important
}

.focus\:placeholder-black:focus::-ms-input-placeholder {
  color: #22292f !important
}

.focus\:placeholder-black:focus::placeholder {
  color: #22292f !important
}

.focus\:placeholder-white:focus::-webkit-input-placeholder {
  color: #ffffff !important
}

.focus\:placeholder-white:focus::-moz-placeholder {
  color: #ffffff !important
}

.focus\:placeholder-white:focus:-ms-input-placeholder {
  color: #ffffff !important
}

.focus\:placeholder-white:focus::-ms-input-placeholder {
  color: #ffffff !important
}

.focus\:placeholder-white:focus::placeholder {
  color: #ffffff !important
}

.focus\:placeholder-grey:focus::-webkit-input-placeholder {
  color: #b8c2cc !important
}

.focus\:placeholder-grey:focus::-moz-placeholder {
  color: #b8c2cc !important
}

.focus\:placeholder-grey:focus:-ms-input-placeholder {
  color: #b8c2cc !important
}

.focus\:placeholder-grey:focus::-ms-input-placeholder {
  color: #b8c2cc !important
}

.focus\:placeholder-grey:focus::placeholder {
  color: #b8c2cc !important
}

.focus\:placeholder-grey-light:focus::-webkit-input-placeholder {
  color: #dae1e7 !important
}

.focus\:placeholder-grey-light:focus::-moz-placeholder {
  color: #dae1e7 !important
}

.focus\:placeholder-grey-light:focus:-ms-input-placeholder {
  color: #dae1e7 !important
}

.focus\:placeholder-grey-light:focus::-ms-input-placeholder {
  color: #dae1e7 !important
}

.focus\:placeholder-grey-light:focus::placeholder {
  color: #dae1e7 !important
}

.pointer-events-none {
  pointer-events: none !important
}

.pointer-events-auto {
  pointer-events: auto !important
}

.static {
  position: static !important
}

.fixed {
  position: fixed !important
}

.absolute {
  position: absolute !important
}

.relative {
  position: relative !important
}

.sticky {
  position: -webkit-sticky !important;
  position: sticky !important
}

.inset-0 {
  top: 0 !important;
  bottom: 0 !important
}

[dir=ltr] .inset-0 {
right: 0 !important;
left: 0 !important
}

[dir=rtl] .inset-0 {
  left: 0 !important;
  right: 0 !important
}

.inset-auto {
  top: auto !important;
  bottom: auto !important
}

[dir=ltr] .inset-auto {
right: auto !important;
left: auto !important
}

[dir=rtl] .inset-auto {
  left: auto !important;
  right: auto !important
}

.inset-y-0 {
  top: 0 !important;
  bottom: 0 !important
}

[dir=ltr] .inset-x-0 {
right: 0 !important;
left: 0 !important
}

[dir=rtl] .inset-x-0 {
  left: 0 !important;
  right: 0 !important
}

.inset-y-auto {
  top: auto !important;
  bottom: auto !important
}

[dir=ltr] .inset-x-auto {
right: auto !important;
left: auto !important
}

[dir=rtl] .inset-x-auto {
  left: auto !important;
  right: auto !important
}

.top-0 {
  top: 0 !important
}

[dir=ltr] .right-0 {
right: 0 !important
}

[dir=rtl] .right-0 {
  left: 0 !important
}

.bottom-0 {
  bottom: 0 !important
}

[dir=ltr] .left-0 {
left: 0 !important
}

[dir=rtl] .left-0 {
  right: 0 !important
}

.top-auto {
  top: auto !important
}

[dir=ltr] .right-auto {
right: auto !important
}

[dir=rtl] .right-auto {
  left: auto !important
}

.bottom-auto {
  bottom: auto !important
}

[dir=ltr] .left-auto {
left: auto !important
}

[dir=rtl] .left-auto {
  right: auto !important
}

.resize-none {
  resize: none !important
}

.resize-y {
  resize: vertical !important
}

.resize-x {
  resize: horizontal !important
}

.resize {
  resize: both !important
}

[dir] .shadow {
  box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
}

[dir] .shadow-md {
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
}

[dir] .shadow-lg {
  box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
}

[dir] .shadow-inner {
  box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
}

[dir] .shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
}

[dir] .shadow-2xl {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
}

[dir] .shadow-outline {
  box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
}

[dir] .shadow-none {
  box-shadow: none !important
}

[dir] .shadow-drop {
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
}

[dir] .hover\:shadow:hover {
  box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
}

[dir] .hover\:shadow-md:hover {
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
}

[dir] .hover\:shadow-lg:hover {
  box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
}

[dir] .hover\:shadow-inner:hover {
  box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
}

[dir] .hover\:shadow-xl:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
}

[dir] .hover\:shadow-2xl:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
}

[dir] .hover\:shadow-outline:hover {
  box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
}

[dir] .hover\:shadow-none:hover {
  box-shadow: none !important
}

[dir] .hover\:shadow-drop:hover {
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
}

[dir] .focus\:shadow:focus {
  box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
}

[dir] .focus\:shadow-md:focus {
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
}

[dir] .focus\:shadow-lg:focus {
  box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
}

[dir] .focus\:shadow-inner:focus {
  box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
}

[dir] .focus\:shadow-xl:focus {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
}

[dir] .focus\:shadow-2xl:focus {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
}

[dir] .focus\:shadow-outline:focus {
  box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
}

[dir] .focus\:shadow-none:focus {
  box-shadow: none !important
}

[dir] .focus\:shadow-drop:focus {
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
}

.fill-current {
  fill: currentColor !important
}

.stroke-current {
  stroke: currentColor !important
}

.table-auto {
  table-layout: auto !important
}

.table-fixed {
  table-layout: fixed !important
}

[dir=ltr] .text-left {
text-align: left !important
}

[dir=rtl] .text-left {
  text-align: right !important
}

[dir] .text-center {
  text-align: center !important
}

[dir=ltr] .text-right {
text-align: right !important
}

[dir=rtl] .text-right {
  text-align: left !important
}

[dir] .text-justify {
  text-align: justify !important
}

.text-inherit {
  color: inherit !important
}

.text-transparent {
  color: transparent !important
}

.text-black {
  color: #22292f !important
}

.text-white {
  color: #ffffff !important
}

.text-grey {
  color: #b8c2cc !important
}

.text-grey-light {
  color: #dae1e7 !important
}

.hover\:text-inherit:hover {
  color: inherit !important
}

.hover\:text-transparent:hover {
  color: transparent !important
}

.hover\:text-black:hover {
  color: #22292f !important
}

.hover\:text-white:hover {
  color: #ffffff !important
}

.hover\:text-grey:hover {
  color: #b8c2cc !important
}

.hover\:text-grey-light:hover {
  color: #dae1e7 !important
}

.focus\:text-inherit:focus {
  color: inherit !important
}

.focus\:text-transparent:focus {
  color: transparent !important
}

.focus\:text-black:focus {
  color: #22292f !important
}

.focus\:text-white:focus {
  color: #ffffff !important
}

.focus\:text-grey:focus {
  color: #b8c2cc !important
}

.focus\:text-grey-light:focus {
  color: #dae1e7 !important
}

.text-xs {
  font-size: .75rem !important
}

.text-sm {
  font-size: .875rem !important
}

.text-base {
  font-size: 1rem !important
}

.text-lg {
  font-size: 1.125rem !important
}

.text-xl {
  font-size: 1.25rem !important
}

.text-2xl {
  font-size: 1.5rem !important
}

.text-3xl {
  font-size: 1.875rem !important
}

.text-4xl {
  font-size: 2.25rem !important
}

.text-5xl {
  font-size: 3rem !important
}

.text-6xl {
  font-size: 4rem !important
}

.italic {
  font-style: italic !important
}

.not-italic {
  font-style: normal !important
}

.hover\:italic:hover {
  font-style: italic !important
}

.hover\:not-italic:hover {
  font-style: normal !important
}

.focus\:italic:focus {
  font-style: italic !important
}

.focus\:not-italic:focus {
  font-style: normal !important
}

.uppercase {
  text-transform: uppercase !important
}

.lowercase {
  text-transform: lowercase !important
}

.capitalize {
  text-transform: capitalize !important
}

.normal-case {
  text-transform: none !important
}

.hover\:uppercase:hover {
  text-transform: uppercase !important
}

.hover\:lowercase:hover {
  text-transform: lowercase !important
}

.hover\:capitalize:hover {
  text-transform: capitalize !important
}

.hover\:normal-case:hover {
  text-transform: none !important
}

.focus\:uppercase:focus {
  text-transform: uppercase !important
}

.focus\:lowercase:focus {
  text-transform: lowercase !important
}

.focus\:capitalize:focus {
  text-transform: capitalize !important
}

.focus\:normal-case:focus {
  text-transform: none !important
}

.underline {
  text-decoration: underline !important
}

.line-through {
  text-decoration: line-through !important
}

.no-underline {
  text-decoration: none !important
}

.hover\:underline:hover {
  text-decoration: underline !important
}

.hover\:line-through:hover {
  text-decoration: line-through !important
}

.hover\:no-underline:hover {
  text-decoration: none !important
}

.focus\:underline:focus {
  text-decoration: underline !important
}

.focus\:line-through:focus {
  text-decoration: line-through !important
}

.focus\:no-underline:focus {
  text-decoration: none !important
}

.antialiased {
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important
}

.subpixel-antialiased {
  -webkit-font-smoothing: auto !important;
  -moz-osx-font-smoothing: auto !important
}

.hover\:antialiased:hover {
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important
}

.hover\:subpixel-antialiased:hover {
  -webkit-font-smoothing: auto !important;
  -moz-osx-font-smoothing: auto !important
}

.focus\:antialiased:focus {
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important
}

.focus\:subpixel-antialiased:focus {
  -webkit-font-smoothing: auto !important;
  -moz-osx-font-smoothing: auto !important
}

.select-none {
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
      -ms-user-select: none !important;
          user-select: none !important
}

.select-text {
  -webkit-user-select: text !important;
     -moz-user-select: text !important;
      -ms-user-select: text !important;
          user-select: text !important
}

.select-all {
  -webkit-user-select: all !important;
     -moz-user-select: all !important;
      -ms-user-select: all !important;
          user-select: all !important
}

.select-auto {
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
      -ms-user-select: auto !important;
          user-select: auto !important
}

.align-baseline {
  vertical-align: baseline !important
}

.align-top {
  vertical-align: top !important
}

.align-middle {
  vertical-align: middle !important
}

.align-bottom {
  vertical-align: bottom !important
}

.align-text-top {
  vertical-align: text-top !important
}

.align-text-bottom {
  vertical-align: text-bottom !important
}

.visible {
  visibility: visible !important
}

.invisible {
  visibility: hidden !important
}

.whitespace-normal {
  white-space: normal !important
}

.whitespace-no-wrap {
  white-space: nowrap !important
}

.whitespace-pre {
  white-space: pre !important
}

.whitespace-pre-line {
  white-space: pre-line !important
}

.whitespace-pre-wrap {
  white-space: pre-wrap !important
}

.break-normal {
  overflow-wrap: normal !important;
  word-break: normal !important
}

.break-words {
  overflow-wrap: break-word !important
}

.break-all {
  word-break: break-all !important
}

.truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important
}

.w-1 {
  width: 0.25rem !important
}

.w-2 {
  width: 0.5rem !important
}

.w-3 {
  width: 0.75rem !important
}

.w-4 {
  width: 1rem !important
}

.w-5 {
  width: 1.25rem !important
}

.w-6 {
  width: 1.5rem !important
}

.w-8 {
  width: 2rem !important
}

.w-10 {
  width: 2.5rem !important
}

.w-12 {
  width: 3rem !important
}

.w-16 {
  width: 4rem !important
}

.w-24 {
  width: 6rem !important
}

.w-32 {
  width: 8rem !important
}

.w-48 {
  width: 12rem !important
}

.w-64 {
  width: 16rem !important
}

.w-auto {
  width: auto !important
}

.w-px {
  width: 1px !important
}

.w-1\/2 {
  width: 50% !important
}

.w-1\/3 {
  width: 33.33333% !important
}

.w-2\/3 {
  width: 66.66667% !important
}

.w-1\/4 {
  width: 25% !important
}

.w-3\/4 {
  width: 75% !important
}

.w-1\/5 {
  width: 20% !important
}

.w-2\/5 {
  width: 40% !important
}

.w-3\/5 {
  width: 60% !important
}

.w-4\/5 {
  width: 80% !important
}

.w-1\/6 {
  width: 16.66667% !important
}

.w-5\/6 {
  width: 83.33333% !important
}

.w-1\/12 {
  width: 8.33333% !important
}

.w-2\/12 {
  width: 16.66667% !important
}

.w-3\/12 {
  width: 25% !important
}

.w-4\/12 {
  width: 33.33333% !important
}

.w-5\/12 {
  width: 41.66667% !important
}

.w-6\/12 {
  width: 50% !important
}

.w-7\/12 {
  width: 58.33333% !important
}

.w-8\/12 {
  width: 66.66667% !important
}

.w-9\/12 {
  width: 75% !important
}

.w-10\/12 {
  width: 83.33333% !important
}

.w-11\/12 {
  width: 91.66667% !important
}

.w-full {
  width: 100% !important
}

.w-screen {
  width: 100vw !important
}

.z-0 {
  z-index: 0 !important
}

.z-10 {
  z-index: 10 !important
}

.z-20 {
  z-index: 20 !important
}

.z-30 {
  z-index: 30 !important
}

.z-40 {
  z-index: 40 !important
}

.z-50 {
  z-index: 50 !important
}

.z-auto {
  z-index: auto !important
}

/**
 * Here you would add any custom utilities you need that don't come out of the
 * box with Tailwind.
 *
 * Example :
 *
 * .bg-pattern-graph-paper { ... }
 * .skew-45 { ... }
 *
 * Or if using a preprocessor or `postcss-import`:
 *
 * @import "utilities/background-patterns";
 * @import "utilities/skew-transforms";
 */

@media (min-width: 576px) {
  .sm\:sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important
  }
  [dir] .sm\:sr-only {
    padding: 0 !important;
    margin: -1px !important;
    border-width: 0 !important
  }

  .sm\:not-sr-only {
    position: static !important;
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important
  }

  [dir] .sm\:not-sr-only {
    padding: 0 !important;
    margin: 0 !important
  }

  .sm\:focus\:sr-only:focus {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important
  }

  [dir] .sm\:focus\:sr-only:focus {
    padding: 0 !important;
    margin: -1px !important;
    border-width: 0 !important
  }

  .sm\:focus\:not-sr-only:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important
  }

  [dir] .sm\:focus\:not-sr-only:focus {
    padding: 0 !important;
    margin: 0 !important
  }

  .sm\:appearance-none {
    -webkit-appearance: none !important;
       -moz-appearance: none !important;
            appearance: none !important
  }

  [dir] .sm\:bg-fixed {
    background-attachment: fixed !important
  }

  [dir] .sm\:bg-local {
    background-attachment: local !important
  }

  [dir] .sm\:bg-scroll {
    background-attachment: scroll !important
  }

  [dir] .sm\:bg-transparent {
    background-color: transparent !important
  }

  [dir] .sm\:bg-black {
    background-color: #22292f !important
  }

  [dir] .sm\:bg-white {
    background-color: #ffffff !important
  }

  [dir] .sm\:bg-grey {
    background-color: #b8c2cc !important
  }

  [dir] .sm\:bg-grey-light {
    background-color: #dae1e7 !important
  }

  [dir] .sm\:hover\:bg-transparent:hover {
    background-color: transparent !important
  }

  [dir] .sm\:hover\:bg-black:hover {
    background-color: #22292f !important
  }

  [dir] .sm\:hover\:bg-white:hover {
    background-color: #ffffff !important
  }

  [dir] .sm\:hover\:bg-grey:hover {
    background-color: #b8c2cc !important
  }

  [dir] .sm\:hover\:bg-grey-light:hover {
    background-color: #dae1e7 !important
  }

  [dir] .sm\:focus\:bg-transparent:focus {
    background-color: transparent !important
  }

  [dir] .sm\:focus\:bg-black:focus {
    background-color: #22292f !important
  }

  [dir] .sm\:focus\:bg-white:focus {
    background-color: #ffffff !important
  }

  [dir] .sm\:focus\:bg-grey:focus {
    background-color: #b8c2cc !important
  }

  [dir] .sm\:focus\:bg-grey-light:focus {
    background-color: #dae1e7 !important
  }

  [dir] .sm\:bg-auto {
    background-size: auto !important
  }

  [dir] .sm\:bg-cover {
    background-size: cover !important
  }

  [dir] .sm\:bg-contain {
    background-size: contain !important
  }

  [dir] .sm\:border-transparent {
    border-color: transparent !important
  }

  [dir] .sm\:border-black {
    border-color: #22292f !important
  }

  [dir] .sm\:border-white {
    border-color: #ffffff !important
  }

  [dir] .sm\:border-grey {
    border-color: #b8c2cc !important
  }

  [dir] .sm\:border-grey-light {
    border-color: #dae1e7 !important
  }

  [dir] .sm\:hover\:border-transparent:hover {
    border-color: transparent !important
  }

  [dir] .sm\:hover\:border-black:hover {
    border-color: #22292f !important
  }

  [dir] .sm\:hover\:border-white:hover {
    border-color: #ffffff !important
  }

  [dir] .sm\:hover\:border-grey:hover {
    border-color: #b8c2cc !important
  }

  [dir] .sm\:hover\:border-grey-light:hover {
    border-color: #dae1e7 !important
  }

  [dir] .sm\:border-solid {
    border-style: solid !important
  }

  [dir] .sm\:border-dashed {
    border-style: dashed !important
  }

  [dir] .sm\:border-dotted {
    border-style: dotted !important
  }

  [dir] .sm\:border-double {
    border-style: double !important
  }

  [dir] .sm\:border-none {
    border-style: none !important
  }

  [dir] .sm\:border-0 {
    border-width: 0 !important
  }

  [dir] .sm\:border-2 {
    border-width: 2px !important
  }

  [dir] .sm\:border-4 {
    border-width: 4px !important
  }

  [dir] .sm\:border-8 {
    border-width: 8px !important
  }

  [dir] .sm\:border {
    border-width: 1px !important
  }

  [dir] .sm\:border-t-0 {
    border-top-width: 0 !important
  }

  [dir=ltr] .sm\:border-r-0 {
border-right-width: 0 !important
  }

  [dir=rtl] .sm\:border-r-0 {
    border-left-width: 0 !important
  }

  [dir] .sm\:border-b-0 {
    border-bottom-width: 0 !important
  }

  [dir=ltr] .sm\:border-l-0 {
border-left-width: 0 !important
  }

  [dir=rtl] .sm\:border-l-0 {
    border-right-width: 0 !important
  }

  [dir] .sm\:border-t-2 {
    border-top-width: 2px !important
  }

  [dir=ltr] .sm\:border-r-2 {
border-right-width: 2px !important
  }

  [dir=rtl] .sm\:border-r-2 {
    border-left-width: 2px !important
  }

  [dir] .sm\:border-b-2 {
    border-bottom-width: 2px !important
  }

  [dir=ltr] .sm\:border-l-2 {
border-left-width: 2px !important
  }

  [dir=rtl] .sm\:border-l-2 {
    border-right-width: 2px !important
  }

  [dir] .sm\:border-t-4 {
    border-top-width: 4px !important
  }

  [dir=ltr] .sm\:border-r-4 {
border-right-width: 4px !important
  }

  [dir=rtl] .sm\:border-r-4 {
    border-left-width: 4px !important
  }

  [dir] .sm\:border-b-4 {
    border-bottom-width: 4px !important
  }

  [dir=ltr] .sm\:border-l-4 {
border-left-width: 4px !important
  }

  [dir=rtl] .sm\:border-l-4 {
    border-right-width: 4px !important
  }

  [dir] .sm\:border-t-8 {
    border-top-width: 8px !important
  }

  [dir=ltr] .sm\:border-r-8 {
border-right-width: 8px !important
  }

  [dir=rtl] .sm\:border-r-8 {
    border-left-width: 8px !important
  }

  [dir] .sm\:border-b-8 {
    border-bottom-width: 8px !important
  }

  [dir=ltr] .sm\:border-l-8 {
border-left-width: 8px !important
  }

  [dir=rtl] .sm\:border-l-8 {
    border-right-width: 8px !important
  }

  [dir] .sm\:border-t {
    border-top-width: 1px !important
  }

  [dir=ltr] .sm\:border-r {
border-right-width: 1px !important
  }

  [dir=rtl] .sm\:border-r {
    border-left-width: 1px !important
  }

  [dir] .sm\:border-b {
    border-bottom-width: 1px !important
  }

  [dir=ltr] .sm\:border-l {
border-left-width: 1px !important
  }

  [dir=rtl] .sm\:border-l {
    border-right-width: 1px !important
  }

  .sm\:block {
    display: block !important
  }

  .sm\:inline-block {
    display: inline-block !important
  }

  .sm\:inline {
    display: inline !important
  }

  .sm\:flex {
    display: -webkit-box !important;
    display: flex !important
  }

  .sm\:inline-flex {
    display: -webkit-inline-box !important;
    display: inline-flex !important
  }

  .sm\:table {
    display: table !important
  }

  .sm\:table-row {
    display: table-row !important
  }

  .sm\:table-cell {
    display: table-cell !important
  }

  .sm\:hidden {
    display: none !important
  }

  .sm\:flex-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
            flex-direction: row !important
  }

  .sm\:flex-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
            flex-direction: row-reverse !important
  }

  .sm\:flex-col {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
            flex-direction: column !important
  }

  .sm\:flex-col-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
            flex-direction: column-reverse !important
  }

  .sm\:flex-wrap {
    flex-wrap: wrap !important
  }

  .sm\:flex-wrap-reverse {
    flex-wrap: wrap-reverse !important
  }

  .sm\:flex-no-wrap {
    flex-wrap: nowrap !important
  }

  .sm\:items-start {
    -webkit-box-align: start !important;
            align-items: flex-start !important
  }

  .sm\:items-end {
    -webkit-box-align: end !important;
            align-items: flex-end !important
  }

  .sm\:items-center {
    -webkit-box-align: center !important;
            align-items: center !important
  }

  .sm\:items-baseline {
    -webkit-box-align: baseline !important;
            align-items: baseline !important
  }

  .sm\:items-stretch {
    -webkit-box-align: stretch !important;
            align-items: stretch !important
  }

  .sm\:self-auto {
    align-self: auto !important
  }

  .sm\:self-start {
    align-self: flex-start !important
  }

  .sm\:self-end {
    align-self: flex-end !important
  }

  .sm\:self-center {
    align-self: center !important
  }

  .sm\:self-stretch {
    align-self: stretch !important
  }

  .sm\:justify-start {
    -webkit-box-pack: start !important;
            justify-content: flex-start !important
  }

  .sm\:justify-end {
    -webkit-box-pack: end !important;
            justify-content: flex-end !important
  }

  .sm\:justify-center {
    -webkit-box-pack: center !important;
            justify-content: center !important
  }

  .sm\:justify-between {
    -webkit-box-pack: justify !important;
            justify-content: space-between !important
  }

  .sm\:justify-around {
    justify-content: space-around !important
  }

  .sm\:content-center {
    align-content: center !important
  }

  .sm\:content-start {
    align-content: flex-start !important
  }

  .sm\:content-end {
    align-content: flex-end !important
  }

  .sm\:content-between {
    align-content: space-between !important
  }

  .sm\:content-around {
    align-content: space-around !important
  }

  .sm\:flex-1 {
    -webkit-box-flex: 1 !important;
            flex: 1 1 0% !important
  }

  .sm\:flex-auto {
    -webkit-box-flex: 1 !important;
            flex: 1 1 auto !important
  }

  .sm\:flex-initial {
    -webkit-box-flex: 0 !important;
            flex: 0 1 auto !important
  }

  .sm\:flex-none {
    -webkit-box-flex: 0 !important;
            flex: none !important
  }

  .sm\:flex-grow-0 {
    -webkit-box-flex: 0 !important;
            flex-grow: 0 !important
  }

  .sm\:flex-grow {
    -webkit-box-flex: 1 !important;
            flex-grow: 1 !important
  }

  .sm\:flex-shrink-0 {
    flex-shrink: 0 !important
  }

  .sm\:flex-shrink {
    flex-shrink: 1 !important
  }

  .sm\:order-1 {
    -webkit-box-ordinal-group: 2 !important;
            order: 1 !important
  }

  .sm\:order-2 {
    -webkit-box-ordinal-group: 3 !important;
            order: 2 !important
  }

  .sm\:order-3 {
    -webkit-box-ordinal-group: 4 !important;
            order: 3 !important
  }

  .sm\:order-4 {
    -webkit-box-ordinal-group: 5 !important;
            order: 4 !important
  }

  .sm\:order-5 {
    -webkit-box-ordinal-group: 6 !important;
            order: 5 !important
  }

  .sm\:order-6 {
    -webkit-box-ordinal-group: 7 !important;
            order: 6 !important
  }

  .sm\:order-first {
    -webkit-box-ordinal-group: 0 !important;
            order: -1 !important
  }

  .sm\:order-last {
    -webkit-box-ordinal-group: 1000 !important;
            order: 999 !important
  }

  .sm\:order-normal {
    -webkit-box-ordinal-group: 1 !important;
            order: 0 !important
  }

  .sm\:hover\:order-1:hover {
    -webkit-box-ordinal-group: 2 !important;
            order: 1 !important
  }

  .sm\:hover\:order-2:hover {
    -webkit-box-ordinal-group: 3 !important;
            order: 2 !important
  }

  .sm\:hover\:order-3:hover {
    -webkit-box-ordinal-group: 4 !important;
            order: 3 !important
  }

  .sm\:hover\:order-4:hover {
    -webkit-box-ordinal-group: 5 !important;
            order: 4 !important
  }

  .sm\:hover\:order-5:hover {
    -webkit-box-ordinal-group: 6 !important;
            order: 5 !important
  }

  .sm\:hover\:order-6:hover {
    -webkit-box-ordinal-group: 7 !important;
            order: 6 !important
  }

  .sm\:hover\:order-first:hover {
    -webkit-box-ordinal-group: 0 !important;
            order: -1 !important
  }

  .sm\:hover\:order-last:hover {
    -webkit-box-ordinal-group: 1000 !important;
            order: 999 !important
  }

  .sm\:hover\:order-normal:hover {
    -webkit-box-ordinal-group: 1 !important;
            order: 0 !important
  }

  .sm\:focus\:order-1:focus {
    -webkit-box-ordinal-group: 2 !important;
            order: 1 !important
  }

  .sm\:focus\:order-2:focus {
    -webkit-box-ordinal-group: 3 !important;
            order: 2 !important
  }

  .sm\:focus\:order-3:focus {
    -webkit-box-ordinal-group: 4 !important;
            order: 3 !important
  }

  .sm\:focus\:order-4:focus {
    -webkit-box-ordinal-group: 5 !important;
            order: 4 !important
  }

  .sm\:focus\:order-5:focus {
    -webkit-box-ordinal-group: 6 !important;
            order: 5 !important
  }

  .sm\:focus\:order-6:focus {
    -webkit-box-ordinal-group: 7 !important;
            order: 6 !important
  }

  .sm\:focus\:order-first:focus {
    -webkit-box-ordinal-group: 0 !important;
            order: -1 !important
  }

  .sm\:focus\:order-last:focus {
    -webkit-box-ordinal-group: 1000 !important;
            order: 999 !important
  }

  .sm\:focus\:order-normal:focus {
    -webkit-box-ordinal-group: 1 !important;
            order: 0 !important
  }

  [dir=ltr] .sm\:float-right {
float: right !important
  }

  [dir=rtl] .sm\:float-right {
    float: left !important
  }

  [dir=ltr] .sm\:float-left {
float: left !important
  }

  [dir=rtl] .sm\:float-left {
    float: right !important
  }

  [dir] .sm\:float-none {
    float: none !important
  }

  .sm\:clearfix:after {
    content: "" !important;
    display: table !important
  }

  [dir] .sm\:clearfix:after {
    clear: both !important
  }

  .sm\:font-light {
    font-weight: 300 !important
  }

  .sm\:font-normal {
    font-weight: 400 !important
  }

  .sm\:font-medium {
    font-weight: 500 !important
  }

  .sm\:font-semibold {
    font-weight: 600 !important
  }

  .sm\:font-bold {
    font-weight: 700 !important
  }

  .sm\:font-extrabold {
    font-weight: 800 !important
  }

  .sm\:font-black {
    font-weight: 900 !important
  }

  .sm\:hover\:font-light:hover {
    font-weight: 300 !important
  }

  .sm\:hover\:font-normal:hover {
    font-weight: 400 !important
  }

  .sm\:hover\:font-medium:hover {
    font-weight: 500 !important
  }

  .sm\:hover\:font-semibold:hover {
    font-weight: 600 !important
  }

  .sm\:hover\:font-bold:hover {
    font-weight: 700 !important
  }

  .sm\:hover\:font-extrabold:hover {
    font-weight: 800 !important
  }

  .sm\:hover\:font-black:hover {
    font-weight: 900 !important
  }

  .sm\:h-1 {
    height: 0.25rem !important
  }

  .sm\:h-2 {
    height: 0.5rem !important
  }

  .sm\:h-3 {
    height: 0.75rem !important
  }

  .sm\:h-4 {
    height: 1rem !important
  }

  .sm\:h-5 {
    height: 1.25rem !important
  }

  .sm\:h-6 {
    height: 1.5rem !important
  }

  .sm\:h-8 {
    height: 2rem !important
  }

  .sm\:h-10 {
    height: 2.5rem !important
  }

  .sm\:h-12 {
    height: 3rem !important
  }

  .sm\:h-16 {
    height: 4rem !important
  }

  .sm\:h-24 {
    height: 6rem !important
  }

  .sm\:h-32 {
    height: 8rem !important
  }

  .sm\:h-48 {
    height: 12rem !important
  }

  .sm\:h-64 {
    height: 16rem !important
  }

  .sm\:h-auto {
    height: auto !important
  }

  .sm\:h-px {
    height: 1px !important
  }

  .sm\:h-full {
    height: 100% !important
  }

  .sm\:h-screen {
    height: 100vh !important
  }

  .sm\:leading-none {
    line-height: 1 !important
  }

  .sm\:leading-tight {
    line-height: 1.25 !important
  }

  .sm\:leading-normal {
    line-height: 1.5 !important
  }

  .sm\:leading-loose {
    line-height: 2 !important
  }

  .sm\:list-inside {
    list-style-position: inside !important
  }

  .sm\:list-outside {
    list-style-position: outside !important
  }

  [dir] .sm\:m-0 {
    margin: 0 !important
  }

  [dir] .sm\:m-1 {
    margin: 0.25rem !important
  }

  [dir] .sm\:m-2 {
    margin: 0.5rem !important
  }

  [dir] .sm\:m-3 {
    margin: 0.75rem !important
  }

  [dir] .sm\:m-4 {
    margin: 1rem !important
  }

  [dir] .sm\:m-5 {
    margin: 1.25rem !important
  }

  [dir] .sm\:m-6 {
    margin: 1.5rem !important
  }

  [dir] .sm\:m-8 {
    margin: 2rem !important
  }

  [dir] .sm\:m-10 {
    margin: 2.5rem !important
  }

  [dir] .sm\:m-12 {
    margin: 3rem !important
  }

  [dir] .sm\:m-16 {
    margin: 4rem !important
  }

  [dir] .sm\:m-20 {
    margin: 5rem !important
  }

  [dir] .sm\:m-24 {
    margin: 6rem !important
  }

  [dir] .sm\:m-32 {
    margin: 8rem !important
  }

  [dir] .sm\:m-auto {
    margin: auto !important
  }

  [dir] .sm\:m-px {
    margin: 1px !important
  }

  [dir] .sm\:m-base {
    margin: 2.2rem !important
  }

  [dir] .sm\:-m-px {
    margin: -1px !important
  }

  [dir] .sm\:-m-1 {
    margin: -0.25rem !important
  }

  [dir] .sm\:-m-2 {
    margin: -0.5rem !important
  }

  [dir] .sm\:-m-3 {
    margin: -0.75rem !important
  }

  [dir] .sm\:-m-4 {
    margin: -1rem !important
  }

  [dir] .sm\:my-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important
  }

  [dir=ltr] .sm\:mx-0 {
margin-left: 0 !important;
margin-right: 0 !important
  }

  [dir=rtl] .sm\:mx-0 {
    margin-right: 0 !important;
    margin-left: 0 !important
  }

  [dir] .sm\:my-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important
  }

  [dir=ltr] .sm\:mx-1 {
margin-left: 0.25rem !important;
margin-right: 0.25rem !important
  }

  [dir=rtl] .sm\:mx-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important
  }

  [dir] .sm\:my-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important
  }

  [dir=ltr] .sm\:mx-2 {
margin-left: 0.5rem !important;
margin-right: 0.5rem !important
  }

  [dir=rtl] .sm\:mx-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important
  }

  [dir] .sm\:my-3 {
    margin-top: 0.75rem !important;
    margin-bottom: 0.75rem !important
  }

  [dir=ltr] .sm\:mx-3 {
margin-left: 0.75rem !important;
margin-right: 0.75rem !important
  }

  [dir=rtl] .sm\:mx-3 {
    margin-right: 0.75rem !important;
    margin-left: 0.75rem !important
  }

  [dir] .sm\:my-4 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important
  }

  [dir=ltr] .sm\:mx-4 {
margin-left: 1rem !important;
margin-right: 1rem !important
  }

  [dir=rtl] .sm\:mx-4 {
    margin-right: 1rem !important;
    margin-left: 1rem !important
  }

  [dir] .sm\:my-5 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important
  }

  [dir=ltr] .sm\:mx-5 {
margin-left: 1.25rem !important;
margin-right: 1.25rem !important
  }

  [dir=rtl] .sm\:mx-5 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important
  }

  [dir] .sm\:my-6 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important
  }

  [dir=ltr] .sm\:mx-6 {
margin-left: 1.5rem !important;
margin-right: 1.5rem !important
  }

  [dir=rtl] .sm\:mx-6 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important
  }

  [dir] .sm\:my-8 {
    margin-top: 2rem !important;
    margin-bottom: 2rem !important
  }

  [dir=ltr] .sm\:mx-8 {
margin-left: 2rem !important;
margin-right: 2rem !important
  }

  [dir=rtl] .sm\:mx-8 {
    margin-right: 2rem !important;
    margin-left: 2rem !important
  }

  [dir] .sm\:my-10 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important
  }

  [dir=ltr] .sm\:mx-10 {
margin-left: 2.5rem !important;
margin-right: 2.5rem !important
  }

  [dir=rtl] .sm\:mx-10 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important
  }

  [dir] .sm\:my-12 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important
  }

  [dir=ltr] .sm\:mx-12 {
margin-left: 3rem !important;
margin-right: 3rem !important
  }

  [dir=rtl] .sm\:mx-12 {
    margin-right: 3rem !important;
    margin-left: 3rem !important
  }

  [dir] .sm\:my-16 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important
  }

  [dir=ltr] .sm\:mx-16 {
margin-left: 4rem !important;
margin-right: 4rem !important
  }

  [dir=rtl] .sm\:mx-16 {
    margin-right: 4rem !important;
    margin-left: 4rem !important
  }

  [dir] .sm\:my-20 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important
  }

  [dir=ltr] .sm\:mx-20 {
margin-left: 5rem !important;
margin-right: 5rem !important
  }

  [dir=rtl] .sm\:mx-20 {
    margin-right: 5rem !important;
    margin-left: 5rem !important
  }

  [dir] .sm\:my-24 {
    margin-top: 6rem !important;
    margin-bottom: 6rem !important
  }

  [dir=ltr] .sm\:mx-24 {
margin-left: 6rem !important;
margin-right: 6rem !important
  }

  [dir=rtl] .sm\:mx-24 {
    margin-right: 6rem !important;
    margin-left: 6rem !important
  }

  [dir] .sm\:my-32 {
    margin-top: 8rem !important;
    margin-bottom: 8rem !important
  }

  [dir=ltr] .sm\:mx-32 {
margin-left: 8rem !important;
margin-right: 8rem !important
  }

  [dir=rtl] .sm\:mx-32 {
    margin-right: 8rem !important;
    margin-left: 8rem !important
  }

  [dir] .sm\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important
  }

  [dir=ltr] .sm\:mx-auto {
margin-left: auto !important;
margin-right: auto !important
  }

  [dir=rtl] .sm\:mx-auto {
    margin-right: auto !important;
    margin-left: auto !important
  }

  [dir] .sm\:my-px {
    margin-top: 1px !important;
    margin-bottom: 1px !important
  }

  [dir=ltr] .sm\:mx-px {
margin-left: 1px !important;
margin-right: 1px !important
  }

  [dir=rtl] .sm\:mx-px {
    margin-right: 1px !important;
    margin-left: 1px !important
  }

  [dir] .sm\:my-base {
    margin-top: 2.2rem !important;
    margin-bottom: 2.2rem !important
  }

  [dir=ltr] .sm\:mx-base {
margin-left: 2.2rem !important;
margin-right: 2.2rem !important
  }

  [dir=rtl] .sm\:mx-base {
    margin-right: 2.2rem !important;
    margin-left: 2.2rem !important
  }

  [dir] .sm\:-my-px {
    margin-top: -1px !important;
    margin-bottom: -1px !important
  }

  [dir=ltr] .sm\:-mx-px {
margin-left: -1px !important;
margin-right: -1px !important
  }

  [dir=rtl] .sm\:-mx-px {
    margin-right: -1px !important;
    margin-left: -1px !important
  }

  [dir] .sm\:-my-1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important
  }

  [dir=ltr] .sm\:-mx-1 {
margin-left: -0.25rem !important;
margin-right: -0.25rem !important
  }

  [dir=rtl] .sm\:-mx-1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important
  }

  [dir] .sm\:-my-2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important
  }

  [dir=ltr] .sm\:-mx-2 {
margin-left: -0.5rem !important;
margin-right: -0.5rem !important
  }

  [dir=rtl] .sm\:-mx-2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important
  }

  [dir] .sm\:-my-3 {
    margin-top: -0.75rem !important;
    margin-bottom: -0.75rem !important
  }

  [dir=ltr] .sm\:-mx-3 {
margin-left: -0.75rem !important;
margin-right: -0.75rem !important
  }

  [dir=rtl] .sm\:-mx-3 {
    margin-right: -0.75rem !important;
    margin-left: -0.75rem !important
  }

  [dir] .sm\:-my-4 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important
  }

  [dir=ltr] .sm\:-mx-4 {
margin-left: -1rem !important;
margin-right: -1rem !important
  }

  [dir=rtl] .sm\:-mx-4 {
    margin-right: -1rem !important;
    margin-left: -1rem !important
  }

  [dir] .sm\:mt-0 {
    margin-top: 0 !important
  }

  [dir=ltr] .sm\:mr-0 {
margin-right: 0 !important
  }

  [dir=rtl] .sm\:mr-0 {
    margin-left: 0 !important
  }

  [dir] .sm\:mb-0 {
    margin-bottom: 0 !important
  }

  [dir=ltr] .sm\:ml-0 {
margin-left: 0 !important
  }

  [dir=rtl] .sm\:ml-0 {
    margin-right: 0 !important
  }

  [dir] .sm\:mt-1 {
    margin-top: 0.25rem !important
  }

  [dir=ltr] .sm\:mr-1 {
margin-right: 0.25rem !important
  }

  [dir=rtl] .sm\:mr-1 {
    margin-left: 0.25rem !important
  }

  [dir] .sm\:mb-1 {
    margin-bottom: 0.25rem !important
  }

  [dir=ltr] .sm\:ml-1 {
margin-left: 0.25rem !important
  }

  [dir=rtl] .sm\:ml-1 {
    margin-right: 0.25rem !important
  }

  [dir] .sm\:mt-2 {
    margin-top: 0.5rem !important
  }

  [dir=ltr] .sm\:mr-2 {
margin-right: 0.5rem !important
  }

  [dir=rtl] .sm\:mr-2 {
    margin-left: 0.5rem !important
  }

  [dir] .sm\:mb-2 {
    margin-bottom: 0.5rem !important
  }

  [dir=ltr] .sm\:ml-2 {
margin-left: 0.5rem !important
  }

  [dir=rtl] .sm\:ml-2 {
    margin-right: 0.5rem !important
  }

  [dir] .sm\:mt-3 {
    margin-top: 0.75rem !important
  }

  [dir=ltr] .sm\:mr-3 {
margin-right: 0.75rem !important
  }

  [dir=rtl] .sm\:mr-3 {
    margin-left: 0.75rem !important
  }

  [dir] .sm\:mb-3 {
    margin-bottom: 0.75rem !important
  }

  [dir=ltr] .sm\:ml-3 {
margin-left: 0.75rem !important
  }

  [dir=rtl] .sm\:ml-3 {
    margin-right: 0.75rem !important
  }

  [dir] .sm\:mt-4 {
    margin-top: 1rem !important
  }

  [dir=ltr] .sm\:mr-4 {
margin-right: 1rem !important
  }

  [dir=rtl] .sm\:mr-4 {
    margin-left: 1rem !important
  }

  [dir] .sm\:mb-4 {
    margin-bottom: 1rem !important
  }

  [dir=ltr] .sm\:ml-4 {
margin-left: 1rem !important
  }

  [dir=rtl] .sm\:ml-4 {
    margin-right: 1rem !important
  }

  [dir] .sm\:mt-5 {
    margin-top: 1.25rem !important
  }

  [dir=ltr] .sm\:mr-5 {
margin-right: 1.25rem !important
  }

  [dir=rtl] .sm\:mr-5 {
    margin-left: 1.25rem !important
  }

  [dir] .sm\:mb-5 {
    margin-bottom: 1.25rem !important
  }

  [dir=ltr] .sm\:ml-5 {
margin-left: 1.25rem !important
  }

  [dir=rtl] .sm\:ml-5 {
    margin-right: 1.25rem !important
  }

  [dir] .sm\:mt-6 {
    margin-top: 1.5rem !important
  }

  [dir=ltr] .sm\:mr-6 {
margin-right: 1.5rem !important
  }

  [dir=rtl] .sm\:mr-6 {
    margin-left: 1.5rem !important
  }

  [dir] .sm\:mb-6 {
    margin-bottom: 1.5rem !important
  }

  [dir=ltr] .sm\:ml-6 {
margin-left: 1.5rem !important
  }

  [dir=rtl] .sm\:ml-6 {
    margin-right: 1.5rem !important
  }

  [dir] .sm\:mt-8 {
    margin-top: 2rem !important
  }

  [dir=ltr] .sm\:mr-8 {
margin-right: 2rem !important
  }

  [dir=rtl] .sm\:mr-8 {
    margin-left: 2rem !important
  }

  [dir] .sm\:mb-8 {
    margin-bottom: 2rem !important
  }

  [dir=ltr] .sm\:ml-8 {
margin-left: 2rem !important
  }

  [dir=rtl] .sm\:ml-8 {
    margin-right: 2rem !important
  }

  [dir] .sm\:mt-10 {
    margin-top: 2.5rem !important
  }

  [dir=ltr] .sm\:mr-10 {
margin-right: 2.5rem !important
  }

  [dir=rtl] .sm\:mr-10 {
    margin-left: 2.5rem !important
  }

  [dir] .sm\:mb-10 {
    margin-bottom: 2.5rem !important
  }

  [dir=ltr] .sm\:ml-10 {
margin-left: 2.5rem !important
  }

  [dir=rtl] .sm\:ml-10 {
    margin-right: 2.5rem !important
  }

  [dir] .sm\:mt-12 {
    margin-top: 3rem !important
  }

  [dir=ltr] .sm\:mr-12 {
margin-right: 3rem !important
  }

  [dir=rtl] .sm\:mr-12 {
    margin-left: 3rem !important
  }

  [dir] .sm\:mb-12 {
    margin-bottom: 3rem !important
  }

  [dir=ltr] .sm\:ml-12 {
margin-left: 3rem !important
  }

  [dir=rtl] .sm\:ml-12 {
    margin-right: 3rem !important
  }

  [dir] .sm\:mt-16 {
    margin-top: 4rem !important
  }

  [dir=ltr] .sm\:mr-16 {
margin-right: 4rem !important
  }

  [dir=rtl] .sm\:mr-16 {
    margin-left: 4rem !important
  }

  [dir] .sm\:mb-16 {
    margin-bottom: 4rem !important
  }

  [dir=ltr] .sm\:ml-16 {
margin-left: 4rem !important
  }

  [dir=rtl] .sm\:ml-16 {
    margin-right: 4rem !important
  }

  [dir] .sm\:mt-20 {
    margin-top: 5rem !important
  }

  [dir=ltr] .sm\:mr-20 {
margin-right: 5rem !important
  }

  [dir=rtl] .sm\:mr-20 {
    margin-left: 5rem !important
  }

  [dir] .sm\:mb-20 {
    margin-bottom: 5rem !important
  }

  [dir=ltr] .sm\:ml-20 {
margin-left: 5rem !important
  }

  [dir=rtl] .sm\:ml-20 {
    margin-right: 5rem !important
  }

  [dir] .sm\:mt-24 {
    margin-top: 6rem !important
  }

  [dir=ltr] .sm\:mr-24 {
margin-right: 6rem !important
  }

  [dir=rtl] .sm\:mr-24 {
    margin-left: 6rem !important
  }

  [dir] .sm\:mb-24 {
    margin-bottom: 6rem !important
  }

  [dir=ltr] .sm\:ml-24 {
margin-left: 6rem !important
  }

  [dir=rtl] .sm\:ml-24 {
    margin-right: 6rem !important
  }

  [dir] .sm\:mt-32 {
    margin-top: 8rem !important
  }

  [dir=ltr] .sm\:mr-32 {
margin-right: 8rem !important
  }

  [dir=rtl] .sm\:mr-32 {
    margin-left: 8rem !important
  }

  [dir] .sm\:mb-32 {
    margin-bottom: 8rem !important
  }

  [dir=ltr] .sm\:ml-32 {
margin-left: 8rem !important
  }

  [dir=rtl] .sm\:ml-32 {
    margin-right: 8rem !important
  }

  [dir] .sm\:mt-auto {
    margin-top: auto !important
  }

  [dir=ltr] .sm\:mr-auto {
margin-right: auto !important
  }

  [dir=rtl] .sm\:mr-auto {
    margin-left: auto !important
  }

  [dir] .sm\:mb-auto {
    margin-bottom: auto !important
  }

  [dir=ltr] .sm\:ml-auto {
margin-left: auto !important
  }

  [dir=rtl] .sm\:ml-auto {
    margin-right: auto !important
  }

  [dir] .sm\:mt-px {
    margin-top: 1px !important
  }

  [dir=ltr] .sm\:mr-px {
margin-right: 1px !important
  }

  [dir=rtl] .sm\:mr-px {
    margin-left: 1px !important
  }

  [dir] .sm\:mb-px {
    margin-bottom: 1px !important
  }

  [dir=ltr] .sm\:ml-px {
margin-left: 1px !important
  }

  [dir=rtl] .sm\:ml-px {
    margin-right: 1px !important
  }

  [dir] .sm\:mt-base {
    margin-top: 2.2rem !important
  }

  [dir=ltr] .sm\:mr-base {
margin-right: 2.2rem !important
  }

  [dir=rtl] .sm\:mr-base {
    margin-left: 2.2rem !important
  }

  [dir] .sm\:mb-base {
    margin-bottom: 2.2rem !important
  }

  [dir=ltr] .sm\:ml-base {
margin-left: 2.2rem !important
  }

  [dir=rtl] .sm\:ml-base {
    margin-right: 2.2rem !important
  }

  [dir] .sm\:-mt-px {
    margin-top: -1px !important
  }

  [dir=ltr] .sm\:-mr-px {
margin-right: -1px !important
  }

  [dir=rtl] .sm\:-mr-px {
    margin-left: -1px !important
  }

  [dir] .sm\:-mb-px {
    margin-bottom: -1px !important
  }

  [dir=ltr] .sm\:-ml-px {
margin-left: -1px !important
  }

  [dir=rtl] .sm\:-ml-px {
    margin-right: -1px !important
  }

  [dir] .sm\:-mt-1 {
    margin-top: -0.25rem !important
  }

  [dir=ltr] .sm\:-mr-1 {
margin-right: -0.25rem !important
  }

  [dir=rtl] .sm\:-mr-1 {
    margin-left: -0.25rem !important
  }

  [dir] .sm\:-mb-1 {
    margin-bottom: -0.25rem !important
  }

  [dir=ltr] .sm\:-ml-1 {
margin-left: -0.25rem !important
  }

  [dir=rtl] .sm\:-ml-1 {
    margin-right: -0.25rem !important
  }

  [dir] .sm\:-mt-2 {
    margin-top: -0.5rem !important
  }

  [dir=ltr] .sm\:-mr-2 {
margin-right: -0.5rem !important
  }

  [dir=rtl] .sm\:-mr-2 {
    margin-left: -0.5rem !important
  }

  [dir] .sm\:-mb-2 {
    margin-bottom: -0.5rem !important
  }

  [dir=ltr] .sm\:-ml-2 {
margin-left: -0.5rem !important
  }

  [dir=rtl] .sm\:-ml-2 {
    margin-right: -0.5rem !important
  }

  [dir] .sm\:-mt-3 {
    margin-top: -0.75rem !important
  }

  [dir=ltr] .sm\:-mr-3 {
margin-right: -0.75rem !important
  }

  [dir=rtl] .sm\:-mr-3 {
    margin-left: -0.75rem !important
  }

  [dir] .sm\:-mb-3 {
    margin-bottom: -0.75rem !important
  }

  [dir=ltr] .sm\:-ml-3 {
margin-left: -0.75rem !important
  }

  [dir=rtl] .sm\:-ml-3 {
    margin-right: -0.75rem !important
  }

  [dir] .sm\:-mt-4 {
    margin-top: -1rem !important
  }

  [dir=ltr] .sm\:-mr-4 {
margin-right: -1rem !important
  }

  [dir=rtl] .sm\:-mr-4 {
    margin-left: -1rem !important
  }

  [dir] .sm\:-mb-4 {
    margin-bottom: -1rem !important
  }

  [dir=ltr] .sm\:-ml-4 {
margin-left: -1rem !important
  }

  [dir=rtl] .sm\:-ml-4 {
    margin-right: -1rem !important
  }

  .sm\:max-h-full {
    max-height: 100% !important
  }

  .sm\:max-h-screen {
    max-height: 100vh !important
  }

  .sm\:max-w-xs {
    max-width: 20rem !important
  }

  .sm\:max-w-sm {
    max-width: 30rem !important
  }

  .sm\:max-w-md {
    max-width: 40rem !important
  }

  .sm\:max-w-lg {
    max-width: 50rem !important
  }

  .sm\:max-w-xl {
    max-width: 60rem !important
  }

  .sm\:max-w-2xl {
    max-width: 70rem !important
  }

  .sm\:max-w-3xl {
    max-width: 80rem !important
  }

  .sm\:max-w-4xl {
    max-width: 90rem !important
  }

  .sm\:max-w-5xl {
    max-width: 100rem !important
  }

  .sm\:max-w-full {
    max-width: 100% !important
  }

  .sm\:min-h-0 {
    min-height: 0 !important
  }

  .sm\:min-h-full {
    min-height: 100% !important
  }

  .sm\:min-h-screen {
    min-height: 100vh !important
  }

  .sm\:min-w-0 {
    min-width: 0 !important
  }

  .sm\:min-w-full {
    min-width: 100% !important
  }

  .sm\:object-contain {
    -o-object-fit: contain !important;
       object-fit: contain !important
  }

  .sm\:object-cover {
    -o-object-fit: cover !important;
       object-fit: cover !important
  }

  .sm\:object-fill {
    -o-object-fit: fill !important;
       object-fit: fill !important
  }

  .sm\:object-none {
    -o-object-fit: none !important;
       object-fit: none !important
  }

  .sm\:object-scale-down {
    -o-object-fit: scale-down !important;
       object-fit: scale-down !important
  }

  .sm\:object-bottom {
    -o-object-position: bottom !important;
       object-position: bottom !important
  }

  .sm\:object-center {
    -o-object-position: center !important;
       object-position: center !important
  }

  .sm\:object-left {
    -o-object-position: left !important;
       object-position: left !important
  }

  .sm\:object-left-bottom {
    -o-object-position: left bottom !important;
       object-position: left bottom !important
  }

  .sm\:object-left-top {
    -o-object-position: left top !important;
       object-position: left top !important
  }

  .sm\:object-right {
    -o-object-position: right !important;
       object-position: right !important
  }

  .sm\:object-right-bottom {
    -o-object-position: right bottom !important;
       object-position: right bottom !important
  }

  .sm\:object-right-top {
    -o-object-position: right top !important;
       object-position: right top !important
  }

  .sm\:object-top {
    -o-object-position: top !important;
       object-position: top !important
  }

  .sm\:opacity-0 {
    opacity: 0 !important
  }

  .sm\:opacity-25 {
    opacity: 0.25 !important
  }

  .sm\:opacity-50 {
    opacity: 0.5 !important
  }

  .sm\:opacity-75 {
    opacity: 0.75 !important
  }

  .sm\:opacity-100 {
    opacity: 1 !important
  }

  .sm\:overflow-auto {
    overflow: auto !important
  }

  .sm\:overflow-hidden {
    overflow: hidden !important
  }

  .sm\:overflow-visible {
    overflow: visible !important
  }

  .sm\:overflow-scroll {
    overflow: scroll !important
  }

  .sm\:overflow-x-auto {
    overflow-x: auto !important
  }

  .sm\:overflow-y-auto {
    overflow-y: auto !important
  }

  .sm\:overflow-x-hidden {
    overflow-x: hidden !important
  }

  .sm\:overflow-y-hidden {
    overflow-y: hidden !important
  }

  .sm\:overflow-x-visible {
    overflow-x: visible !important
  }

  .sm\:overflow-y-visible {
    overflow-y: visible !important
  }

  .sm\:overflow-x-scroll {
    overflow-x: scroll !important
  }

  .sm\:overflow-y-scroll {
    overflow-y: scroll !important
  }

  .sm\:scrolling-touch {
    -webkit-overflow-scrolling: touch !important
  }

  .sm\:scrolling-auto {
    -webkit-overflow-scrolling: auto !important
  }

  [dir] .sm\:p-0 {
    padding: 0 !important
  }

  [dir] .sm\:p-1 {
    padding: 0.25rem !important
  }

  [dir] .sm\:p-2 {
    padding: 0.5rem !important
  }

  [dir] .sm\:p-3 {
    padding: 0.75rem !important
  }

  [dir] .sm\:p-4 {
    padding: 1rem !important
  }

  [dir] .sm\:p-5 {
    padding: 1.25rem !important
  }

  [dir] .sm\:p-6 {
    padding: 1.5rem !important
  }

  [dir] .sm\:p-8 {
    padding: 2rem !important
  }

  [dir] .sm\:p-10 {
    padding: 2.5rem !important
  }

  [dir] .sm\:p-12 {
    padding: 3rem !important
  }

  [dir] .sm\:p-16 {
    padding: 4rem !important
  }

  [dir] .sm\:p-20 {
    padding: 5rem !important
  }

  [dir] .sm\:p-24 {
    padding: 6rem !important
  }

  [dir] .sm\:p-32 {
    padding: 8rem !important
  }

  [dir] .sm\:p-px {
    padding: 1px !important
  }

  [dir] .sm\:p-base {
    padding: 2.2rem !important
  }

  [dir] .sm\:py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important
  }

  [dir=ltr] .sm\:px-0 {
padding-left: 0 !important;
padding-right: 0 !important
  }

  [dir=rtl] .sm\:px-0 {
    padding-right: 0 !important;
    padding-left: 0 !important
  }

  [dir] .sm\:py-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important
  }

  [dir=ltr] .sm\:px-1 {
padding-left: 0.25rem !important;
padding-right: 0.25rem !important
  }

  [dir=rtl] .sm\:px-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important
  }

  [dir] .sm\:py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important
  }

  [dir=ltr] .sm\:px-2 {
padding-left: 0.5rem !important;
padding-right: 0.5rem !important
  }

  [dir=rtl] .sm\:px-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important
  }

  [dir] .sm\:py-3 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important
  }

  [dir=ltr] .sm\:px-3 {
padding-left: 0.75rem !important;
padding-right: 0.75rem !important
  }

  [dir=rtl] .sm\:px-3 {
    padding-right: 0.75rem !important;
    padding-left: 0.75rem !important
  }

  [dir] .sm\:py-4 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important
  }

  [dir=ltr] .sm\:px-4 {
padding-left: 1rem !important;
padding-right: 1rem !important
  }

  [dir=rtl] .sm\:px-4 {
    padding-right: 1rem !important;
    padding-left: 1rem !important
  }

  [dir] .sm\:py-5 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important
  }

  [dir=ltr] .sm\:px-5 {
padding-left: 1.25rem !important;
padding-right: 1.25rem !important
  }

  [dir=rtl] .sm\:px-5 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important
  }

  [dir] .sm\:py-6 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important
  }

  [dir=ltr] .sm\:px-6 {
padding-left: 1.5rem !important;
padding-right: 1.5rem !important
  }

  [dir=rtl] .sm\:px-6 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important
  }

  [dir] .sm\:py-8 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important
  }

  [dir=ltr] .sm\:px-8 {
padding-left: 2rem !important;
padding-right: 2rem !important
  }

  [dir=rtl] .sm\:px-8 {
    padding-right: 2rem !important;
    padding-left: 2rem !important
  }

  [dir] .sm\:py-10 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important
  }

  [dir=ltr] .sm\:px-10 {
padding-left: 2.5rem !important;
padding-right: 2.5rem !important
  }

  [dir=rtl] .sm\:px-10 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important
  }

  [dir] .sm\:py-12 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important
  }

  [dir=ltr] .sm\:px-12 {
padding-left: 3rem !important;
padding-right: 3rem !important
  }

  [dir=rtl] .sm\:px-12 {
    padding-right: 3rem !important;
    padding-left: 3rem !important
  }

  [dir] .sm\:py-16 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important
  }

  [dir=ltr] .sm\:px-16 {
padding-left: 4rem !important;
padding-right: 4rem !important
  }

  [dir=rtl] .sm\:px-16 {
    padding-right: 4rem !important;
    padding-left: 4rem !important
  }

  [dir] .sm\:py-20 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important
  }

  [dir=ltr] .sm\:px-20 {
padding-left: 5rem !important;
padding-right: 5rem !important
  }

  [dir=rtl] .sm\:px-20 {
    padding-right: 5rem !important;
    padding-left: 5rem !important
  }

  [dir] .sm\:py-24 {
    padding-top: 6rem !important;
    padding-bottom: 6rem !important
  }

  [dir=ltr] .sm\:px-24 {
padding-left: 6rem !important;
padding-right: 6rem !important
  }

  [dir=rtl] .sm\:px-24 {
    padding-right: 6rem !important;
    padding-left: 6rem !important
  }

  [dir] .sm\:py-32 {
    padding-top: 8rem !important;
    padding-bottom: 8rem !important
  }

  [dir=ltr] .sm\:px-32 {
padding-left: 8rem !important;
padding-right: 8rem !important
  }

  [dir=rtl] .sm\:px-32 {
    padding-right: 8rem !important;
    padding-left: 8rem !important
  }

  [dir] .sm\:py-px {
    padding-top: 1px !important;
    padding-bottom: 1px !important
  }

  [dir=ltr] .sm\:px-px {
padding-left: 1px !important;
padding-right: 1px !important
  }

  [dir=rtl] .sm\:px-px {
    padding-right: 1px !important;
    padding-left: 1px !important
  }

  [dir] .sm\:py-base {
    padding-top: 2.2rem !important;
    padding-bottom: 2.2rem !important
  }

  [dir=ltr] .sm\:px-base {
padding-left: 2.2rem !important;
padding-right: 2.2rem !important
  }

  [dir=rtl] .sm\:px-base {
    padding-right: 2.2rem !important;
    padding-left: 2.2rem !important
  }

  [dir] .sm\:pt-0 {
    padding-top: 0 !important
  }

  [dir=ltr] .sm\:pr-0 {
padding-right: 0 !important
  }

  [dir=rtl] .sm\:pr-0 {
    padding-left: 0 !important
  }

  [dir] .sm\:pb-0 {
    padding-bottom: 0 !important
  }

  [dir=ltr] .sm\:pl-0 {
padding-left: 0 !important
  }

  [dir=rtl] .sm\:pl-0 {
    padding-right: 0 !important
  }

  [dir] .sm\:pt-1 {
    padding-top: 0.25rem !important
  }

  [dir=ltr] .sm\:pr-1 {
padding-right: 0.25rem !important
  }

  [dir=rtl] .sm\:pr-1 {
    padding-left: 0.25rem !important
  }

  [dir] .sm\:pb-1 {
    padding-bottom: 0.25rem !important
  }

  [dir=ltr] .sm\:pl-1 {
padding-left: 0.25rem !important
  }

  [dir=rtl] .sm\:pl-1 {
    padding-right: 0.25rem !important
  }

  [dir] .sm\:pt-2 {
    padding-top: 0.5rem !important
  }

  [dir=ltr] .sm\:pr-2 {
padding-right: 0.5rem !important
  }

  [dir=rtl] .sm\:pr-2 {
    padding-left: 0.5rem !important
  }

  [dir] .sm\:pb-2 {
    padding-bottom: 0.5rem !important
  }

  [dir=ltr] .sm\:pl-2 {
padding-left: 0.5rem !important
  }

  [dir=rtl] .sm\:pl-2 {
    padding-right: 0.5rem !important
  }

  [dir] .sm\:pt-3 {
    padding-top: 0.75rem !important
  }

  [dir=ltr] .sm\:pr-3 {
padding-right: 0.75rem !important
  }

  [dir=rtl] .sm\:pr-3 {
    padding-left: 0.75rem !important
  }

  [dir] .sm\:pb-3 {
    padding-bottom: 0.75rem !important
  }

  [dir=ltr] .sm\:pl-3 {
padding-left: 0.75rem !important
  }

  [dir=rtl] .sm\:pl-3 {
    padding-right: 0.75rem !important
  }

  [dir] .sm\:pt-4 {
    padding-top: 1rem !important
  }

  [dir=ltr] .sm\:pr-4 {
padding-right: 1rem !important
  }

  [dir=rtl] .sm\:pr-4 {
    padding-left: 1rem !important
  }

  [dir] .sm\:pb-4 {
    padding-bottom: 1rem !important
  }

  [dir=ltr] .sm\:pl-4 {
padding-left: 1rem !important
  }

  [dir=rtl] .sm\:pl-4 {
    padding-right: 1rem !important
  }

  [dir] .sm\:pt-5 {
    padding-top: 1.25rem !important
  }

  [dir=ltr] .sm\:pr-5 {
padding-right: 1.25rem !important
  }

  [dir=rtl] .sm\:pr-5 {
    padding-left: 1.25rem !important
  }

  [dir] .sm\:pb-5 {
    padding-bottom: 1.25rem !important
  }

  [dir=ltr] .sm\:pl-5 {
padding-left: 1.25rem !important
  }

  [dir=rtl] .sm\:pl-5 {
    padding-right: 1.25rem !important
  }

  [dir] .sm\:pt-6 {
    padding-top: 1.5rem !important
  }

  [dir=ltr] .sm\:pr-6 {
padding-right: 1.5rem !important
  }

  [dir=rtl] .sm\:pr-6 {
    padding-left: 1.5rem !important
  }

  [dir] .sm\:pb-6 {
    padding-bottom: 1.5rem !important
  }

  [dir=ltr] .sm\:pl-6 {
padding-left: 1.5rem !important
  }

  [dir=rtl] .sm\:pl-6 {
    padding-right: 1.5rem !important
  }

  [dir] .sm\:pt-8 {
    padding-top: 2rem !important
  }

  [dir=ltr] .sm\:pr-8 {
padding-right: 2rem !important
  }

  [dir=rtl] .sm\:pr-8 {
    padding-left: 2rem !important
  }

  [dir] .sm\:pb-8 {
    padding-bottom: 2rem !important
  }

  [dir=ltr] .sm\:pl-8 {
padding-left: 2rem !important
  }

  [dir=rtl] .sm\:pl-8 {
    padding-right: 2rem !important
  }

  [dir] .sm\:pt-10 {
    padding-top: 2.5rem !important
  }

  [dir=ltr] .sm\:pr-10 {
padding-right: 2.5rem !important
  }

  [dir=rtl] .sm\:pr-10 {
    padding-left: 2.5rem !important
  }

  [dir] .sm\:pb-10 {
    padding-bottom: 2.5rem !important
  }

  [dir=ltr] .sm\:pl-10 {
padding-left: 2.5rem !important
  }

  [dir=rtl] .sm\:pl-10 {
    padding-right: 2.5rem !important
  }

  [dir] .sm\:pt-12 {
    padding-top: 3rem !important
  }

  [dir=ltr] .sm\:pr-12 {
padding-right: 3rem !important
  }

  [dir=rtl] .sm\:pr-12 {
    padding-left: 3rem !important
  }

  [dir] .sm\:pb-12 {
    padding-bottom: 3rem !important
  }

  [dir=ltr] .sm\:pl-12 {
padding-left: 3rem !important
  }

  [dir=rtl] .sm\:pl-12 {
    padding-right: 3rem !important
  }

  [dir] .sm\:pt-16 {
    padding-top: 4rem !important
  }

  [dir=ltr] .sm\:pr-16 {
padding-right: 4rem !important
  }

  [dir=rtl] .sm\:pr-16 {
    padding-left: 4rem !important
  }

  [dir] .sm\:pb-16 {
    padding-bottom: 4rem !important
  }

  [dir=ltr] .sm\:pl-16 {
padding-left: 4rem !important
  }

  [dir=rtl] .sm\:pl-16 {
    padding-right: 4rem !important
  }

  [dir] .sm\:pt-20 {
    padding-top: 5rem !important
  }

  [dir=ltr] .sm\:pr-20 {
padding-right: 5rem !important
  }

  [dir=rtl] .sm\:pr-20 {
    padding-left: 5rem !important
  }

  [dir] .sm\:pb-20 {
    padding-bottom: 5rem !important
  }

  [dir=ltr] .sm\:pl-20 {
padding-left: 5rem !important
  }

  [dir=rtl] .sm\:pl-20 {
    padding-right: 5rem !important
  }

  [dir] .sm\:pt-24 {
    padding-top: 6rem !important
  }

  [dir=ltr] .sm\:pr-24 {
padding-right: 6rem !important
  }

  [dir=rtl] .sm\:pr-24 {
    padding-left: 6rem !important
  }

  [dir] .sm\:pb-24 {
    padding-bottom: 6rem !important
  }

  [dir=ltr] .sm\:pl-24 {
padding-left: 6rem !important
  }

  [dir=rtl] .sm\:pl-24 {
    padding-right: 6rem !important
  }

  [dir] .sm\:pt-32 {
    padding-top: 8rem !important
  }

  [dir=ltr] .sm\:pr-32 {
padding-right: 8rem !important
  }

  [dir=rtl] .sm\:pr-32 {
    padding-left: 8rem !important
  }

  [dir] .sm\:pb-32 {
    padding-bottom: 8rem !important
  }

  [dir=ltr] .sm\:pl-32 {
padding-left: 8rem !important
  }

  [dir=rtl] .sm\:pl-32 {
    padding-right: 8rem !important
  }

  [dir] .sm\:pt-px {
    padding-top: 1px !important
  }

  [dir=ltr] .sm\:pr-px {
padding-right: 1px !important
  }

  [dir=rtl] .sm\:pr-px {
    padding-left: 1px !important
  }

  [dir] .sm\:pb-px {
    padding-bottom: 1px !important
  }

  [dir=ltr] .sm\:pl-px {
padding-left: 1px !important
  }

  [dir=rtl] .sm\:pl-px {
    padding-right: 1px !important
  }

  [dir] .sm\:pt-base {
    padding-top: 2.2rem !important
  }

  [dir=ltr] .sm\:pr-base {
padding-right: 2.2rem !important
  }

  [dir=rtl] .sm\:pr-base {
    padding-left: 2.2rem !important
  }

  [dir] .sm\:pb-base {
    padding-bottom: 2.2rem !important
  }

  [dir=ltr] .sm\:pl-base {
padding-left: 2.2rem !important
  }

  [dir=rtl] .sm\:pl-base {
    padding-right: 2.2rem !important
  }

  .sm\:placeholder-transparent::-webkit-input-placeholder {
    color: transparent !important
  }

  .sm\:placeholder-transparent::-moz-placeholder {
    color: transparent !important
  }

  .sm\:placeholder-transparent:-ms-input-placeholder {
    color: transparent !important
  }

  .sm\:placeholder-transparent::-ms-input-placeholder {
    color: transparent !important
  }

  .sm\:placeholder-transparent::placeholder {
    color: transparent !important
  }

  .sm\:placeholder-black::-webkit-input-placeholder {
    color: #22292f !important
  }

  .sm\:placeholder-black::-moz-placeholder {
    color: #22292f !important
  }

  .sm\:placeholder-black:-ms-input-placeholder {
    color: #22292f !important
  }

  .sm\:placeholder-black::-ms-input-placeholder {
    color: #22292f !important
  }

  .sm\:placeholder-black::placeholder {
    color: #22292f !important
  }

  .sm\:placeholder-white::-webkit-input-placeholder {
    color: #ffffff !important
  }

  .sm\:placeholder-white::-moz-placeholder {
    color: #ffffff !important
  }

  .sm\:placeholder-white:-ms-input-placeholder {
    color: #ffffff !important
  }

  .sm\:placeholder-white::-ms-input-placeholder {
    color: #ffffff !important
  }

  .sm\:placeholder-white::placeholder {
    color: #ffffff !important
  }

  .sm\:placeholder-grey::-webkit-input-placeholder {
    color: #b8c2cc !important
  }

  .sm\:placeholder-grey::-moz-placeholder {
    color: #b8c2cc !important
  }

  .sm\:placeholder-grey:-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .sm\:placeholder-grey::-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .sm\:placeholder-grey::placeholder {
    color: #b8c2cc !important
  }

  .sm\:placeholder-grey-light::-webkit-input-placeholder {
    color: #dae1e7 !important
  }

  .sm\:placeholder-grey-light::-moz-placeholder {
    color: #dae1e7 !important
  }

  .sm\:placeholder-grey-light:-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .sm\:placeholder-grey-light::-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .sm\:placeholder-grey-light::placeholder {
    color: #dae1e7 !important
  }

  .sm\:focus\:placeholder-transparent:focus::-webkit-input-placeholder {
    color: transparent !important
  }

  .sm\:focus\:placeholder-transparent:focus::-moz-placeholder {
    color: transparent !important
  }

  .sm\:focus\:placeholder-transparent:focus:-ms-input-placeholder {
    color: transparent !important
  }

  .sm\:focus\:placeholder-transparent:focus::-ms-input-placeholder {
    color: transparent !important
  }

  .sm\:focus\:placeholder-transparent:focus::placeholder {
    color: transparent !important
  }

  .sm\:focus\:placeholder-black:focus::-webkit-input-placeholder {
    color: #22292f !important
  }

  .sm\:focus\:placeholder-black:focus::-moz-placeholder {
    color: #22292f !important
  }

  .sm\:focus\:placeholder-black:focus:-ms-input-placeholder {
    color: #22292f !important
  }

  .sm\:focus\:placeholder-black:focus::-ms-input-placeholder {
    color: #22292f !important
  }

  .sm\:focus\:placeholder-black:focus::placeholder {
    color: #22292f !important
  }

  .sm\:focus\:placeholder-white:focus::-webkit-input-placeholder {
    color: #ffffff !important
  }

  .sm\:focus\:placeholder-white:focus::-moz-placeholder {
    color: #ffffff !important
  }

  .sm\:focus\:placeholder-white:focus:-ms-input-placeholder {
    color: #ffffff !important
  }

  .sm\:focus\:placeholder-white:focus::-ms-input-placeholder {
    color: #ffffff !important
  }

  .sm\:focus\:placeholder-white:focus::placeholder {
    color: #ffffff !important
  }

  .sm\:focus\:placeholder-grey:focus::-webkit-input-placeholder {
    color: #b8c2cc !important
  }

  .sm\:focus\:placeholder-grey:focus::-moz-placeholder {
    color: #b8c2cc !important
  }

  .sm\:focus\:placeholder-grey:focus:-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .sm\:focus\:placeholder-grey:focus::-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .sm\:focus\:placeholder-grey:focus::placeholder {
    color: #b8c2cc !important
  }

  .sm\:focus\:placeholder-grey-light:focus::-webkit-input-placeholder {
    color: #dae1e7 !important
  }

  .sm\:focus\:placeholder-grey-light:focus::-moz-placeholder {
    color: #dae1e7 !important
  }

  .sm\:focus\:placeholder-grey-light:focus:-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .sm\:focus\:placeholder-grey-light:focus::-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .sm\:focus\:placeholder-grey-light:focus::placeholder {
    color: #dae1e7 !important
  }

  .sm\:pointer-events-none {
    pointer-events: none !important
  }

  .sm\:pointer-events-auto {
    pointer-events: auto !important
  }

  .sm\:static {
    position: static !important
  }

  .sm\:fixed {
    position: fixed !important
  }

  .sm\:absolute {
    position: absolute !important
  }

  .sm\:relative {
    position: relative !important
  }

  .sm\:sticky {
    position: -webkit-sticky !important;
    position: sticky !important
  }

  .sm\:inset-0 {
    top: 0 !important;
    bottom: 0 !important
  }

  [dir=ltr] .sm\:inset-0 {
right: 0 !important;
left: 0 !important
  }

  [dir=rtl] .sm\:inset-0 {
    left: 0 !important;
    right: 0 !important
  }

  .sm\:inset-auto {
    top: auto !important;
    bottom: auto !important
  }

  [dir=ltr] .sm\:inset-auto {
right: auto !important;
left: auto !important
  }

  [dir=rtl] .sm\:inset-auto {
    left: auto !important;
    right: auto !important
  }

  .sm\:inset-y-0 {
    top: 0 !important;
    bottom: 0 !important
  }

  [dir=ltr] .sm\:inset-x-0 {
right: 0 !important;
left: 0 !important
  }

  [dir=rtl] .sm\:inset-x-0 {
    left: 0 !important;
    right: 0 !important
  }

  .sm\:inset-y-auto {
    top: auto !important;
    bottom: auto !important
  }

  [dir=ltr] .sm\:inset-x-auto {
right: auto !important;
left: auto !important
  }

  [dir=rtl] .sm\:inset-x-auto {
    left: auto !important;
    right: auto !important
  }

  .sm\:top-0 {
    top: 0 !important
  }

  [dir=ltr] .sm\:right-0 {
right: 0 !important
  }

  [dir=rtl] .sm\:right-0 {
    left: 0 !important
  }

  .sm\:bottom-0 {
    bottom: 0 !important
  }

  [dir=ltr] .sm\:left-0 {
left: 0 !important
  }

  [dir=rtl] .sm\:left-0 {
    right: 0 !important
  }

  .sm\:top-auto {
    top: auto !important
  }

  [dir=ltr] .sm\:right-auto {
right: auto !important
  }

  [dir=rtl] .sm\:right-auto {
    left: auto !important
  }

  .sm\:bottom-auto {
    bottom: auto !important
  }

  [dir=ltr] .sm\:left-auto {
left: auto !important
  }

  [dir=rtl] .sm\:left-auto {
    right: auto !important
  }

  .sm\:resize-none {
    resize: none !important
  }

  .sm\:resize-y {
    resize: vertical !important
  }

  .sm\:resize-x {
    resize: horizontal !important
  }

  .sm\:resize {
    resize: both !important
  }

  [dir] .sm\:shadow {
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
  }

  [dir] .sm\:shadow-md {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .sm\:shadow-lg {
    box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .sm\:shadow-inner {
    box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
  }

  [dir] .sm\:shadow-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
  }

  [dir] .sm\:shadow-2xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
  }

  [dir] .sm\:shadow-outline {
    box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
  }

  [dir] .sm\:shadow-none {
    box-shadow: none !important
  }

  [dir] .sm\:shadow-drop {
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
  }

  [dir] .sm\:hover\:shadow:hover {
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
  }

  [dir] .sm\:hover\:shadow-md:hover {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .sm\:hover\:shadow-lg:hover {
    box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .sm\:hover\:shadow-inner:hover {
    box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
  }

  [dir] .sm\:hover\:shadow-xl:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
  }

  [dir] .sm\:hover\:shadow-2xl:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
  }

  [dir] .sm\:hover\:shadow-outline:hover {
    box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
  }

  [dir] .sm\:hover\:shadow-none:hover {
    box-shadow: none !important
  }

  [dir] .sm\:hover\:shadow-drop:hover {
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
  }

  [dir] .sm\:focus\:shadow:focus {
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
  }

  [dir] .sm\:focus\:shadow-md:focus {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .sm\:focus\:shadow-lg:focus {
    box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .sm\:focus\:shadow-inner:focus {
    box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
  }

  [dir] .sm\:focus\:shadow-xl:focus {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
  }

  [dir] .sm\:focus\:shadow-2xl:focus {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
  }

  [dir] .sm\:focus\:shadow-outline:focus {
    box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
  }

  [dir] .sm\:focus\:shadow-none:focus {
    box-shadow: none !important
  }

  [dir] .sm\:focus\:shadow-drop:focus {
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
  }

  .sm\:table-auto {
    table-layout: auto !important
  }

  .sm\:table-fixed {
    table-layout: fixed !important
  }

  [dir=ltr] .sm\:text-left {
text-align: left !important
  }

  [dir=rtl] .sm\:text-left {
    text-align: right !important
  }

  [dir] .sm\:text-center {
    text-align: center !important
  }

  [dir=ltr] .sm\:text-right {
text-align: right !important
  }

  [dir=rtl] .sm\:text-right {
    text-align: left !important
  }

  [dir] .sm\:text-justify {
    text-align: justify !important
  }

  .sm\:text-inherit {
    color: inherit !important
  }

  .sm\:text-transparent {
    color: transparent !important
  }

  .sm\:text-black {
    color: #22292f !important
  }

  .sm\:text-white {
    color: #ffffff !important
  }

  .sm\:text-grey {
    color: #b8c2cc !important
  }

  .sm\:text-grey-light {
    color: #dae1e7 !important
  }

  .sm\:hover\:text-inherit:hover {
    color: inherit !important
  }

  .sm\:hover\:text-transparent:hover {
    color: transparent !important
  }

  .sm\:hover\:text-black:hover {
    color: #22292f !important
  }

  .sm\:hover\:text-white:hover {
    color: #ffffff !important
  }

  .sm\:hover\:text-grey:hover {
    color: #b8c2cc !important
  }

  .sm\:hover\:text-grey-light:hover {
    color: #dae1e7 !important
  }

  .sm\:focus\:text-inherit:focus {
    color: inherit !important
  }

  .sm\:focus\:text-transparent:focus {
    color: transparent !important
  }

  .sm\:focus\:text-black:focus {
    color: #22292f !important
  }

  .sm\:focus\:text-white:focus {
    color: #ffffff !important
  }

  .sm\:focus\:text-grey:focus {
    color: #b8c2cc !important
  }

  .sm\:focus\:text-grey-light:focus {
    color: #dae1e7 !important
  }

  .sm\:text-xs {
    font-size: .75rem !important
  }

  .sm\:text-sm {
    font-size: .875rem !important
  }

  .sm\:text-base {
    font-size: 1rem !important
  }

  .sm\:text-lg {
    font-size: 1.125rem !important
  }

  .sm\:text-xl {
    font-size: 1.25rem !important
  }

  .sm\:text-2xl {
    font-size: 1.5rem !important
  }

  .sm\:text-3xl {
    font-size: 1.875rem !important
  }

  .sm\:text-4xl {
    font-size: 2.25rem !important
  }

  .sm\:text-5xl {
    font-size: 3rem !important
  }

  .sm\:text-6xl {
    font-size: 4rem !important
  }

  .sm\:italic {
    font-style: italic !important
  }

  .sm\:not-italic {
    font-style: normal !important
  }

  .sm\:hover\:italic:hover {
    font-style: italic !important
  }

  .sm\:hover\:not-italic:hover {
    font-style: normal !important
  }

  .sm\:focus\:italic:focus {
    font-style: italic !important
  }

  .sm\:focus\:not-italic:focus {
    font-style: normal !important
  }

  .sm\:uppercase {
    text-transform: uppercase !important
  }

  .sm\:lowercase {
    text-transform: lowercase !important
  }

  .sm\:capitalize {
    text-transform: capitalize !important
  }

  .sm\:normal-case {
    text-transform: none !important
  }

  .sm\:hover\:uppercase:hover {
    text-transform: uppercase !important
  }

  .sm\:hover\:lowercase:hover {
    text-transform: lowercase !important
  }

  .sm\:hover\:capitalize:hover {
    text-transform: capitalize !important
  }

  .sm\:hover\:normal-case:hover {
    text-transform: none !important
  }

  .sm\:focus\:uppercase:focus {
    text-transform: uppercase !important
  }

  .sm\:focus\:lowercase:focus {
    text-transform: lowercase !important
  }

  .sm\:focus\:capitalize:focus {
    text-transform: capitalize !important
  }

  .sm\:focus\:normal-case:focus {
    text-transform: none !important
  }

  .sm\:underline {
    text-decoration: underline !important
  }

  .sm\:line-through {
    text-decoration: line-through !important
  }

  .sm\:no-underline {
    text-decoration: none !important
  }

  .sm\:hover\:underline:hover {
    text-decoration: underline !important
  }

  .sm\:hover\:line-through:hover {
    text-decoration: line-through !important
  }

  .sm\:hover\:no-underline:hover {
    text-decoration: none !important
  }

  .sm\:focus\:underline:focus {
    text-decoration: underline !important
  }

  .sm\:focus\:line-through:focus {
    text-decoration: line-through !important
  }

  .sm\:focus\:no-underline:focus {
    text-decoration: none !important
  }

  .sm\:antialiased {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important
  }

  .sm\:subpixel-antialiased {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important
  }

  .sm\:hover\:antialiased:hover {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important
  }

  .sm\:hover\:subpixel-antialiased:hover {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important
  }

  .sm\:focus\:antialiased:focus {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important
  }

  .sm\:focus\:subpixel-antialiased:focus {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important
  }

  .sm\:select-none {
    -webkit-user-select: none !important;
       -moz-user-select: none !important;
        -ms-user-select: none !important;
            user-select: none !important
  }

  .sm\:select-text {
    -webkit-user-select: text !important;
       -moz-user-select: text !important;
        -ms-user-select: text !important;
            user-select: text !important
  }

  .sm\:select-all {
    -webkit-user-select: all !important;
       -moz-user-select: all !important;
        -ms-user-select: all !important;
            user-select: all !important
  }

  .sm\:select-auto {
    -webkit-user-select: auto !important;
       -moz-user-select: auto !important;
        -ms-user-select: auto !important;
            user-select: auto !important
  }

  .sm\:align-baseline {
    vertical-align: baseline !important
  }

  .sm\:align-top {
    vertical-align: top !important
  }

  .sm\:align-middle {
    vertical-align: middle !important
  }

  .sm\:align-bottom {
    vertical-align: bottom !important
  }

  .sm\:align-text-top {
    vertical-align: text-top !important
  }

  .sm\:align-text-bottom {
    vertical-align: text-bottom !important
  }

  .sm\:visible {
    visibility: visible !important
  }

  .sm\:invisible {
    visibility: hidden !important
  }

  .sm\:whitespace-normal {
    white-space: normal !important
  }

  .sm\:whitespace-no-wrap {
    white-space: nowrap !important
  }

  .sm\:whitespace-pre {
    white-space: pre !important
  }

  .sm\:whitespace-pre-line {
    white-space: pre-line !important
  }

  .sm\:whitespace-pre-wrap {
    white-space: pre-wrap !important
  }

  .sm\:break-normal {
    overflow-wrap: normal !important;
    word-break: normal !important
  }

  .sm\:break-words {
    overflow-wrap: break-word !important
  }

  .sm\:break-all {
    word-break: break-all !important
  }

  .sm\:truncate {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important
  }

  .sm\:w-1 {
    width: 0.25rem !important
  }

  .sm\:w-2 {
    width: 0.5rem !important
  }

  .sm\:w-3 {
    width: 0.75rem !important
  }

  .sm\:w-4 {
    width: 1rem !important
  }

  .sm\:w-5 {
    width: 1.25rem !important
  }

  .sm\:w-6 {
    width: 1.5rem !important
  }

  .sm\:w-8 {
    width: 2rem !important
  }

  .sm\:w-10 {
    width: 2.5rem !important
  }

  .sm\:w-12 {
    width: 3rem !important
  }

  .sm\:w-16 {
    width: 4rem !important
  }

  .sm\:w-24 {
    width: 6rem !important
  }

  .sm\:w-32 {
    width: 8rem !important
  }

  .sm\:w-48 {
    width: 12rem !important
  }

  .sm\:w-64 {
    width: 16rem !important
  }

  .sm\:w-auto {
    width: auto !important
  }

  .sm\:w-px {
    width: 1px !important
  }

  .sm\:w-1\/2 {
    width: 50% !important
  }

  .sm\:w-1\/3 {
    width: 33.33333% !important
  }

  .sm\:w-2\/3 {
    width: 66.66667% !important
  }

  .sm\:w-1\/4 {
    width: 25% !important
  }

  .sm\:w-3\/4 {
    width: 75% !important
  }

  .sm\:w-1\/5 {
    width: 20% !important
  }

  .sm\:w-2\/5 {
    width: 40% !important
  }

  .sm\:w-3\/5 {
    width: 60% !important
  }

  .sm\:w-4\/5 {
    width: 80% !important
  }

  .sm\:w-1\/6 {
    width: 16.66667% !important
  }

  .sm\:w-5\/6 {
    width: 83.33333% !important
  }

  .sm\:w-1\/12 {
    width: 8.33333% !important
  }

  .sm\:w-2\/12 {
    width: 16.66667% !important
  }

  .sm\:w-3\/12 {
    width: 25% !important
  }

  .sm\:w-4\/12 {
    width: 33.33333% !important
  }

  .sm\:w-5\/12 {
    width: 41.66667% !important
  }

  .sm\:w-6\/12 {
    width: 50% !important
  }

  .sm\:w-7\/12 {
    width: 58.33333% !important
  }

  .sm\:w-8\/12 {
    width: 66.66667% !important
  }

  .sm\:w-9\/12 {
    width: 75% !important
  }

  .sm\:w-10\/12 {
    width: 83.33333% !important
  }

  .sm\:w-11\/12 {
    width: 91.66667% !important
  }

  .sm\:w-full {
    width: 100% !important
  }

  .sm\:w-screen {
    width: 100vw !important
  }

  .sm\:z-0 {
    z-index: 0 !important
  }

  .sm\:z-10 {
    z-index: 10 !important
  }

  .sm\:z-20 {
    z-index: 20 !important
  }

  .sm\:z-30 {
    z-index: 30 !important
  }

  .sm\:z-40 {
    z-index: 40 !important
  }

  .sm\:z-50 {
    z-index: 50 !important
  }

  .sm\:z-auto {
    z-index: auto !important
  }
}

@media (min-width: 768px) {
  .md\:sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important
  }
  [dir] .md\:sr-only {
    padding: 0 !important;
    margin: -1px !important;
    border-width: 0 !important
  }

  .md\:not-sr-only {
    position: static !important;
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important
  }

  [dir] .md\:not-sr-only {
    padding: 0 !important;
    margin: 0 !important
  }

  .md\:focus\:sr-only:focus {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important
  }

  [dir] .md\:focus\:sr-only:focus {
    padding: 0 !important;
    margin: -1px !important;
    border-width: 0 !important
  }

  .md\:focus\:not-sr-only:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important
  }

  [dir] .md\:focus\:not-sr-only:focus {
    padding: 0 !important;
    margin: 0 !important
  }

  .md\:appearance-none {
    -webkit-appearance: none !important;
       -moz-appearance: none !important;
            appearance: none !important
  }

  [dir] .md\:bg-fixed {
    background-attachment: fixed !important
  }

  [dir] .md\:bg-local {
    background-attachment: local !important
  }

  [dir] .md\:bg-scroll {
    background-attachment: scroll !important
  }

  [dir] .md\:bg-transparent {
    background-color: transparent !important
  }

  [dir] .md\:bg-black {
    background-color: #22292f !important
  }

  [dir] .md\:bg-white {
    background-color: #ffffff !important
  }

  [dir] .md\:bg-grey {
    background-color: #b8c2cc !important
  }

  [dir] .md\:bg-grey-light {
    background-color: #dae1e7 !important
  }

  [dir] .md\:hover\:bg-transparent:hover {
    background-color: transparent !important
  }

  [dir] .md\:hover\:bg-black:hover {
    background-color: #22292f !important
  }

  [dir] .md\:hover\:bg-white:hover {
    background-color: #ffffff !important
  }

  [dir] .md\:hover\:bg-grey:hover {
    background-color: #b8c2cc !important
  }

  [dir] .md\:hover\:bg-grey-light:hover {
    background-color: #dae1e7 !important
  }

  [dir] .md\:focus\:bg-transparent:focus {
    background-color: transparent !important
  }

  [dir] .md\:focus\:bg-black:focus {
    background-color: #22292f !important
  }

  [dir] .md\:focus\:bg-white:focus {
    background-color: #ffffff !important
  }

  [dir] .md\:focus\:bg-grey:focus {
    background-color: #b8c2cc !important
  }

  [dir] .md\:focus\:bg-grey-light:focus {
    background-color: #dae1e7 !important
  }

  [dir] .md\:bg-auto {
    background-size: auto !important
  }

  [dir] .md\:bg-cover {
    background-size: cover !important
  }

  [dir] .md\:bg-contain {
    background-size: contain !important
  }

  [dir] .md\:border-transparent {
    border-color: transparent !important
  }

  [dir] .md\:border-black {
    border-color: #22292f !important
  }

  [dir] .md\:border-white {
    border-color: #ffffff !important
  }

  [dir] .md\:border-grey {
    border-color: #b8c2cc !important
  }

  [dir] .md\:border-grey-light {
    border-color: #dae1e7 !important
  }

  [dir] .md\:hover\:border-transparent:hover {
    border-color: transparent !important
  }

  [dir] .md\:hover\:border-black:hover {
    border-color: #22292f !important
  }

  [dir] .md\:hover\:border-white:hover {
    border-color: #ffffff !important
  }

  [dir] .md\:hover\:border-grey:hover {
    border-color: #b8c2cc !important
  }

  [dir] .md\:hover\:border-grey-light:hover {
    border-color: #dae1e7 !important
  }

  [dir] .md\:border-solid {
    border-style: solid !important
  }

  [dir] .md\:border-dashed {
    border-style: dashed !important
  }

  [dir] .md\:border-dotted {
    border-style: dotted !important
  }

  [dir] .md\:border-double {
    border-style: double !important
  }

  [dir] .md\:border-none {
    border-style: none !important
  }

  [dir] .md\:border-0 {
    border-width: 0 !important
  }

  [dir] .md\:border-2 {
    border-width: 2px !important
  }

  [dir] .md\:border-4 {
    border-width: 4px !important
  }

  [dir] .md\:border-8 {
    border-width: 8px !important
  }

  [dir] .md\:border {
    border-width: 1px !important
  }

  [dir] .md\:border-t-0 {
    border-top-width: 0 !important
  }

  [dir=ltr] .md\:border-r-0 {
border-right-width: 0 !important
  }

  [dir=rtl] .md\:border-r-0 {
    border-left-width: 0 !important
  }

  [dir] .md\:border-b-0 {
    border-bottom-width: 0 !important
  }

  [dir=ltr] .md\:border-l-0 {
border-left-width: 0 !important
  }

  [dir=rtl] .md\:border-l-0 {
    border-right-width: 0 !important
  }

  [dir] .md\:border-t-2 {
    border-top-width: 2px !important
  }

  [dir=ltr] .md\:border-r-2 {
border-right-width: 2px !important
  }

  [dir=rtl] .md\:border-r-2 {
    border-left-width: 2px !important
  }

  [dir] .md\:border-b-2 {
    border-bottom-width: 2px !important
  }

  [dir=ltr] .md\:border-l-2 {
border-left-width: 2px !important
  }

  [dir=rtl] .md\:border-l-2 {
    border-right-width: 2px !important
  }

  [dir] .md\:border-t-4 {
    border-top-width: 4px !important
  }

  [dir=ltr] .md\:border-r-4 {
border-right-width: 4px !important
  }

  [dir=rtl] .md\:border-r-4 {
    border-left-width: 4px !important
  }

  [dir] .md\:border-b-4 {
    border-bottom-width: 4px !important
  }

  [dir=ltr] .md\:border-l-4 {
border-left-width: 4px !important
  }

  [dir=rtl] .md\:border-l-4 {
    border-right-width: 4px !important
  }

  [dir] .md\:border-t-8 {
    border-top-width: 8px !important
  }

  [dir=ltr] .md\:border-r-8 {
border-right-width: 8px !important
  }

  [dir=rtl] .md\:border-r-8 {
    border-left-width: 8px !important
  }

  [dir] .md\:border-b-8 {
    border-bottom-width: 8px !important
  }

  [dir=ltr] .md\:border-l-8 {
border-left-width: 8px !important
  }

  [dir=rtl] .md\:border-l-8 {
    border-right-width: 8px !important
  }

  [dir] .md\:border-t {
    border-top-width: 1px !important
  }

  [dir=ltr] .md\:border-r {
border-right-width: 1px !important
  }

  [dir=rtl] .md\:border-r {
    border-left-width: 1px !important
  }

  [dir] .md\:border-b {
    border-bottom-width: 1px !important
  }

  [dir=ltr] .md\:border-l {
border-left-width: 1px !important
  }

  [dir=rtl] .md\:border-l {
    border-right-width: 1px !important
  }

  .md\:block {
    display: block !important
  }

  .md\:inline-block {
    display: inline-block !important
  }

  .md\:inline {
    display: inline !important
  }

  .md\:flex {
    display: -webkit-box !important;
    display: flex !important
  }

  .md\:inline-flex {
    display: -webkit-inline-box !important;
    display: inline-flex !important
  }

  .md\:table {
    display: table !important
  }

  .md\:table-row {
    display: table-row !important
  }

  .md\:table-cell {
    display: table-cell !important
  }

  .md\:hidden {
    display: none !important
  }

  .md\:flex-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
            flex-direction: row !important
  }

  .md\:flex-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
            flex-direction: row-reverse !important
  }

  .md\:flex-col {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
            flex-direction: column !important
  }

  .md\:flex-col-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
            flex-direction: column-reverse !important
  }

  .md\:flex-wrap {
    flex-wrap: wrap !important
  }

  .md\:flex-wrap-reverse {
    flex-wrap: wrap-reverse !important
  }

  .md\:flex-no-wrap {
    flex-wrap: nowrap !important
  }

  .md\:items-start {
    -webkit-box-align: start !important;
            align-items: flex-start !important
  }

  .md\:items-end {
    -webkit-box-align: end !important;
            align-items: flex-end !important
  }

  .md\:items-center {
    -webkit-box-align: center !important;
            align-items: center !important
  }

  .md\:items-baseline {
    -webkit-box-align: baseline !important;
            align-items: baseline !important
  }

  .md\:items-stretch {
    -webkit-box-align: stretch !important;
            align-items: stretch !important
  }

  .md\:self-auto {
    align-self: auto !important
  }

  .md\:self-start {
    align-self: flex-start !important
  }

  .md\:self-end {
    align-self: flex-end !important
  }

  .md\:self-center {
    align-self: center !important
  }

  .md\:self-stretch {
    align-self: stretch !important
  }

  .md\:justify-start {
    -webkit-box-pack: start !important;
            justify-content: flex-start !important
  }

  .md\:justify-end {
    -webkit-box-pack: end !important;
            justify-content: flex-end !important
  }

  .md\:justify-center {
    -webkit-box-pack: center !important;
            justify-content: center !important
  }

  .md\:justify-between {
    -webkit-box-pack: justify !important;
            justify-content: space-between !important
  }

  .md\:justify-around {
    justify-content: space-around !important
  }

  .md\:content-center {
    align-content: center !important
  }

  .md\:content-start {
    align-content: flex-start !important
  }

  .md\:content-end {
    align-content: flex-end !important
  }

  .md\:content-between {
    align-content: space-between !important
  }

  .md\:content-around {
    align-content: space-around !important
  }

  .md\:flex-1 {
    -webkit-box-flex: 1 !important;
            flex: 1 1 0% !important
  }

  .md\:flex-auto {
    -webkit-box-flex: 1 !important;
            flex: 1 1 auto !important
  }

  .md\:flex-initial {
    -webkit-box-flex: 0 !important;
            flex: 0 1 auto !important
  }

  .md\:flex-none {
    -webkit-box-flex: 0 !important;
            flex: none !important
  }

  .md\:flex-grow-0 {
    -webkit-box-flex: 0 !important;
            flex-grow: 0 !important
  }

  .md\:flex-grow {
    -webkit-box-flex: 1 !important;
            flex-grow: 1 !important
  }

  .md\:flex-shrink-0 {
    flex-shrink: 0 !important
  }

  .md\:flex-shrink {
    flex-shrink: 1 !important
  }

  .md\:order-1 {
    -webkit-box-ordinal-group: 2 !important;
            order: 1 !important
  }

  .md\:order-2 {
    -webkit-box-ordinal-group: 3 !important;
            order: 2 !important
  }

  .md\:order-3 {
    -webkit-box-ordinal-group: 4 !important;
            order: 3 !important
  }

  .md\:order-4 {
    -webkit-box-ordinal-group: 5 !important;
            order: 4 !important
  }

  .md\:order-5 {
    -webkit-box-ordinal-group: 6 !important;
            order: 5 !important
  }

  .md\:order-6 {
    -webkit-box-ordinal-group: 7 !important;
            order: 6 !important
  }

  .md\:order-first {
    -webkit-box-ordinal-group: 0 !important;
            order: -1 !important
  }

  .md\:order-last {
    -webkit-box-ordinal-group: 1000 !important;
            order: 999 !important
  }

  .md\:order-normal {
    -webkit-box-ordinal-group: 1 !important;
            order: 0 !important
  }

  .md\:hover\:order-1:hover {
    -webkit-box-ordinal-group: 2 !important;
            order: 1 !important
  }

  .md\:hover\:order-2:hover {
    -webkit-box-ordinal-group: 3 !important;
            order: 2 !important
  }

  .md\:hover\:order-3:hover {
    -webkit-box-ordinal-group: 4 !important;
            order: 3 !important
  }

  .md\:hover\:order-4:hover {
    -webkit-box-ordinal-group: 5 !important;
            order: 4 !important
  }

  .md\:hover\:order-5:hover {
    -webkit-box-ordinal-group: 6 !important;
            order: 5 !important
  }

  .md\:hover\:order-6:hover {
    -webkit-box-ordinal-group: 7 !important;
            order: 6 !important
  }

  .md\:hover\:order-first:hover {
    -webkit-box-ordinal-group: 0 !important;
            order: -1 !important
  }

  .md\:hover\:order-last:hover {
    -webkit-box-ordinal-group: 1000 !important;
            order: 999 !important
  }

  .md\:hover\:order-normal:hover {
    -webkit-box-ordinal-group: 1 !important;
            order: 0 !important
  }

  .md\:focus\:order-1:focus {
    -webkit-box-ordinal-group: 2 !important;
            order: 1 !important
  }

  .md\:focus\:order-2:focus {
    -webkit-box-ordinal-group: 3 !important;
            order: 2 !important
  }

  .md\:focus\:order-3:focus {
    -webkit-box-ordinal-group: 4 !important;
            order: 3 !important
  }

  .md\:focus\:order-4:focus {
    -webkit-box-ordinal-group: 5 !important;
            order: 4 !important
  }

  .md\:focus\:order-5:focus {
    -webkit-box-ordinal-group: 6 !important;
            order: 5 !important
  }

  .md\:focus\:order-6:focus {
    -webkit-box-ordinal-group: 7 !important;
            order: 6 !important
  }

  .md\:focus\:order-first:focus {
    -webkit-box-ordinal-group: 0 !important;
            order: -1 !important
  }

  .md\:focus\:order-last:focus {
    -webkit-box-ordinal-group: 1000 !important;
            order: 999 !important
  }

  .md\:focus\:order-normal:focus {
    -webkit-box-ordinal-group: 1 !important;
            order: 0 !important
  }

  [dir=ltr] .md\:float-right {
float: right !important
  }

  [dir=rtl] .md\:float-right {
    float: left !important
  }

  [dir=ltr] .md\:float-left {
float: left !important
  }

  [dir=rtl] .md\:float-left {
    float: right !important
  }

  [dir] .md\:float-none {
    float: none !important
  }

  .md\:clearfix:after {
    content: "" !important;
    display: table !important
  }

  [dir] .md\:clearfix:after {
    clear: both !important
  }

  .md\:font-light {
    font-weight: 300 !important
  }

  .md\:font-normal {
    font-weight: 400 !important
  }

  .md\:font-medium {
    font-weight: 500 !important
  }

  .md\:font-semibold {
    font-weight: 600 !important
  }

  .md\:font-bold {
    font-weight: 700 !important
  }

  .md\:font-extrabold {
    font-weight: 800 !important
  }

  .md\:font-black {
    font-weight: 900 !important
  }

  .md\:hover\:font-light:hover {
    font-weight: 300 !important
  }

  .md\:hover\:font-normal:hover {
    font-weight: 400 !important
  }

  .md\:hover\:font-medium:hover {
    font-weight: 500 !important
  }

  .md\:hover\:font-semibold:hover {
    font-weight: 600 !important
  }

  .md\:hover\:font-bold:hover {
    font-weight: 700 !important
  }

  .md\:hover\:font-extrabold:hover {
    font-weight: 800 !important
  }

  .md\:hover\:font-black:hover {
    font-weight: 900 !important
  }

  .md\:h-1 {
    height: 0.25rem !important
  }

  .md\:h-2 {
    height: 0.5rem !important
  }

  .md\:h-3 {
    height: 0.75rem !important
  }

  .md\:h-4 {
    height: 1rem !important
  }

  .md\:h-5 {
    height: 1.25rem !important
  }

  .md\:h-6 {
    height: 1.5rem !important
  }

  .md\:h-8 {
    height: 2rem !important
  }

  .md\:h-10 {
    height: 2.5rem !important
  }

  .md\:h-12 {
    height: 3rem !important
  }

  .md\:h-16 {
    height: 4rem !important
  }

  .md\:h-24 {
    height: 6rem !important
  }

  .md\:h-32 {
    height: 8rem !important
  }

  .md\:h-48 {
    height: 12rem !important
  }

  .md\:h-64 {
    height: 16rem !important
  }

  .md\:h-auto {
    height: auto !important
  }

  .md\:h-px {
    height: 1px !important
  }

  .md\:h-full {
    height: 100% !important
  }

  .md\:h-screen {
    height: 100vh !important
  }

  .md\:leading-none {
    line-height: 1 !important
  }

  .md\:leading-tight {
    line-height: 1.25 !important
  }

  .md\:leading-normal {
    line-height: 1.5 !important
  }

  .md\:leading-loose {
    line-height: 2 !important
  }

  .md\:list-inside {
    list-style-position: inside !important
  }

  .md\:list-outside {
    list-style-position: outside !important
  }

  [dir] .md\:m-0 {
    margin: 0 !important
  }

  [dir] .md\:m-1 {
    margin: 0.25rem !important
  }

  [dir] .md\:m-2 {
    margin: 0.5rem !important
  }

  [dir] .md\:m-3 {
    margin: 0.75rem !important
  }

  [dir] .md\:m-4 {
    margin: 1rem !important
  }

  [dir] .md\:m-5 {
    margin: 1.25rem !important
  }

  [dir] .md\:m-6 {
    margin: 1.5rem !important
  }

  [dir] .md\:m-8 {
    margin: 2rem !important
  }

  [dir] .md\:m-10 {
    margin: 2.5rem !important
  }

  [dir] .md\:m-12 {
    margin: 3rem !important
  }

  [dir] .md\:m-16 {
    margin: 4rem !important
  }

  [dir] .md\:m-20 {
    margin: 5rem !important
  }

  [dir] .md\:m-24 {
    margin: 6rem !important
  }

  [dir] .md\:m-32 {
    margin: 8rem !important
  }

  [dir] .md\:m-auto {
    margin: auto !important
  }

  [dir] .md\:m-px {
    margin: 1px !important
  }

  [dir] .md\:m-base {
    margin: 2.2rem !important
  }

  [dir] .md\:-m-px {
    margin: -1px !important
  }

  [dir] .md\:-m-1 {
    margin: -0.25rem !important
  }

  [dir] .md\:-m-2 {
    margin: -0.5rem !important
  }

  [dir] .md\:-m-3 {
    margin: -0.75rem !important
  }

  [dir] .md\:-m-4 {
    margin: -1rem !important
  }

  [dir] .md\:my-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important
  }

  [dir=ltr] .md\:mx-0 {
margin-left: 0 !important;
margin-right: 0 !important
  }

  [dir=rtl] .md\:mx-0 {
    margin-right: 0 !important;
    margin-left: 0 !important
  }

  [dir] .md\:my-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important
  }

  [dir=ltr] .md\:mx-1 {
margin-left: 0.25rem !important;
margin-right: 0.25rem !important
  }

  [dir=rtl] .md\:mx-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important
  }

  [dir] .md\:my-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important
  }

  [dir=ltr] .md\:mx-2 {
margin-left: 0.5rem !important;
margin-right: 0.5rem !important
  }

  [dir=rtl] .md\:mx-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important
  }

  [dir] .md\:my-3 {
    margin-top: 0.75rem !important;
    margin-bottom: 0.75rem !important
  }

  [dir=ltr] .md\:mx-3 {
margin-left: 0.75rem !important;
margin-right: 0.75rem !important
  }

  [dir=rtl] .md\:mx-3 {
    margin-right: 0.75rem !important;
    margin-left: 0.75rem !important
  }

  [dir] .md\:my-4 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important
  }

  [dir=ltr] .md\:mx-4 {
margin-left: 1rem !important;
margin-right: 1rem !important
  }

  [dir=rtl] .md\:mx-4 {
    margin-right: 1rem !important;
    margin-left: 1rem !important
  }

  [dir] .md\:my-5 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important
  }

  [dir=ltr] .md\:mx-5 {
margin-left: 1.25rem !important;
margin-right: 1.25rem !important
  }

  [dir=rtl] .md\:mx-5 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important
  }

  [dir] .md\:my-6 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important
  }

  [dir=ltr] .md\:mx-6 {
margin-left: 1.5rem !important;
margin-right: 1.5rem !important
  }

  [dir=rtl] .md\:mx-6 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important
  }

  [dir] .md\:my-8 {
    margin-top: 2rem !important;
    margin-bottom: 2rem !important
  }

  [dir=ltr] .md\:mx-8 {
margin-left: 2rem !important;
margin-right: 2rem !important
  }

  [dir=rtl] .md\:mx-8 {
    margin-right: 2rem !important;
    margin-left: 2rem !important
  }

  [dir] .md\:my-10 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important
  }

  [dir=ltr] .md\:mx-10 {
margin-left: 2.5rem !important;
margin-right: 2.5rem !important
  }

  [dir=rtl] .md\:mx-10 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important
  }

  [dir] .md\:my-12 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important
  }

  [dir=ltr] .md\:mx-12 {
margin-left: 3rem !important;
margin-right: 3rem !important
  }

  [dir=rtl] .md\:mx-12 {
    margin-right: 3rem !important;
    margin-left: 3rem !important
  }

  [dir] .md\:my-16 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important
  }

  [dir=ltr] .md\:mx-16 {
margin-left: 4rem !important;
margin-right: 4rem !important
  }

  [dir=rtl] .md\:mx-16 {
    margin-right: 4rem !important;
    margin-left: 4rem !important
  }

  [dir] .md\:my-20 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important
  }

  [dir=ltr] .md\:mx-20 {
margin-left: 5rem !important;
margin-right: 5rem !important
  }

  [dir=rtl] .md\:mx-20 {
    margin-right: 5rem !important;
    margin-left: 5rem !important
  }

  [dir] .md\:my-24 {
    margin-top: 6rem !important;
    margin-bottom: 6rem !important
  }

  [dir=ltr] .md\:mx-24 {
margin-left: 6rem !important;
margin-right: 6rem !important
  }

  [dir=rtl] .md\:mx-24 {
    margin-right: 6rem !important;
    margin-left: 6rem !important
  }

  [dir] .md\:my-32 {
    margin-top: 8rem !important;
    margin-bottom: 8rem !important
  }

  [dir=ltr] .md\:mx-32 {
margin-left: 8rem !important;
margin-right: 8rem !important
  }

  [dir=rtl] .md\:mx-32 {
    margin-right: 8rem !important;
    margin-left: 8rem !important
  }

  [dir] .md\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important
  }

  [dir=ltr] .md\:mx-auto {
margin-left: auto !important;
margin-right: auto !important
  }

  [dir=rtl] .md\:mx-auto {
    margin-right: auto !important;
    margin-left: auto !important
  }

  [dir] .md\:my-px {
    margin-top: 1px !important;
    margin-bottom: 1px !important
  }

  [dir=ltr] .md\:mx-px {
margin-left: 1px !important;
margin-right: 1px !important
  }

  [dir=rtl] .md\:mx-px {
    margin-right: 1px !important;
    margin-left: 1px !important
  }

  [dir] .md\:my-base {
    margin-top: 2.2rem !important;
    margin-bottom: 2.2rem !important
  }

  [dir=ltr] .md\:mx-base {
margin-left: 2.2rem !important;
margin-right: 2.2rem !important
  }

  [dir=rtl] .md\:mx-base {
    margin-right: 2.2rem !important;
    margin-left: 2.2rem !important
  }

  [dir] .md\:-my-px {
    margin-top: -1px !important;
    margin-bottom: -1px !important
  }

  [dir=ltr] .md\:-mx-px {
margin-left: -1px !important;
margin-right: -1px !important
  }

  [dir=rtl] .md\:-mx-px {
    margin-right: -1px !important;
    margin-left: -1px !important
  }

  [dir] .md\:-my-1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important
  }

  [dir=ltr] .md\:-mx-1 {
margin-left: -0.25rem !important;
margin-right: -0.25rem !important
  }

  [dir=rtl] .md\:-mx-1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important
  }

  [dir] .md\:-my-2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important
  }

  [dir=ltr] .md\:-mx-2 {
margin-left: -0.5rem !important;
margin-right: -0.5rem !important
  }

  [dir=rtl] .md\:-mx-2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important
  }

  [dir] .md\:-my-3 {
    margin-top: -0.75rem !important;
    margin-bottom: -0.75rem !important
  }

  [dir=ltr] .md\:-mx-3 {
margin-left: -0.75rem !important;
margin-right: -0.75rem !important
  }

  [dir=rtl] .md\:-mx-3 {
    margin-right: -0.75rem !important;
    margin-left: -0.75rem !important
  }

  [dir] .md\:-my-4 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important
  }

  [dir=ltr] .md\:-mx-4 {
margin-left: -1rem !important;
margin-right: -1rem !important
  }

  [dir=rtl] .md\:-mx-4 {
    margin-right: -1rem !important;
    margin-left: -1rem !important
  }

  [dir] .md\:mt-0 {
    margin-top: 0 !important
  }

  [dir=ltr] .md\:mr-0 {
margin-right: 0 !important
  }

  [dir=rtl] .md\:mr-0 {
    margin-left: 0 !important
  }

  [dir] .md\:mb-0 {
    margin-bottom: 0 !important
  }

  [dir=ltr] .md\:ml-0 {
margin-left: 0 !important
  }

  [dir=rtl] .md\:ml-0 {
    margin-right: 0 !important
  }

  [dir] .md\:mt-1 {
    margin-top: 0.25rem !important
  }

  [dir=ltr] .md\:mr-1 {
margin-right: 0.25rem !important
  }

  [dir=rtl] .md\:mr-1 {
    margin-left: 0.25rem !important
  }

  [dir] .md\:mb-1 {
    margin-bottom: 0.25rem !important
  }

  [dir=ltr] .md\:ml-1 {
margin-left: 0.25rem !important
  }

  [dir=rtl] .md\:ml-1 {
    margin-right: 0.25rem !important
  }

  [dir] .md\:mt-2 {
    margin-top: 0.5rem !important
  }

  [dir=ltr] .md\:mr-2 {
margin-right: 0.5rem !important
  }

  [dir=rtl] .md\:mr-2 {
    margin-left: 0.5rem !important
  }

  [dir] .md\:mb-2 {
    margin-bottom: 0.5rem !important
  }

  [dir=ltr] .md\:ml-2 {
margin-left: 0.5rem !important
  }

  [dir=rtl] .md\:ml-2 {
    margin-right: 0.5rem !important
  }

  [dir] .md\:mt-3 {
    margin-top: 0.75rem !important
  }

  [dir=ltr] .md\:mr-3 {
margin-right: 0.75rem !important
  }

  [dir=rtl] .md\:mr-3 {
    margin-left: 0.75rem !important
  }

  [dir] .md\:mb-3 {
    margin-bottom: 0.75rem !important
  }

  [dir=ltr] .md\:ml-3 {
margin-left: 0.75rem !important
  }

  [dir=rtl] .md\:ml-3 {
    margin-right: 0.75rem !important
  }

  [dir] .md\:mt-4 {
    margin-top: 1rem !important
  }

  [dir=ltr] .md\:mr-4 {
margin-right: 1rem !important
  }

  [dir=rtl] .md\:mr-4 {
    margin-left: 1rem !important
  }

  [dir] .md\:mb-4 {
    margin-bottom: 1rem !important
  }

  [dir=ltr] .md\:ml-4 {
margin-left: 1rem !important
  }

  [dir=rtl] .md\:ml-4 {
    margin-right: 1rem !important
  }

  [dir] .md\:mt-5 {
    margin-top: 1.25rem !important
  }

  [dir=ltr] .md\:mr-5 {
margin-right: 1.25rem !important
  }

  [dir=rtl] .md\:mr-5 {
    margin-left: 1.25rem !important
  }

  [dir] .md\:mb-5 {
    margin-bottom: 1.25rem !important
  }

  [dir=ltr] .md\:ml-5 {
margin-left: 1.25rem !important
  }

  [dir=rtl] .md\:ml-5 {
    margin-right: 1.25rem !important
  }

  [dir] .md\:mt-6 {
    margin-top: 1.5rem !important
  }

  [dir=ltr] .md\:mr-6 {
margin-right: 1.5rem !important
  }

  [dir=rtl] .md\:mr-6 {
    margin-left: 1.5rem !important
  }

  [dir] .md\:mb-6 {
    margin-bottom: 1.5rem !important
  }

  [dir=ltr] .md\:ml-6 {
margin-left: 1.5rem !important
  }

  [dir=rtl] .md\:ml-6 {
    margin-right: 1.5rem !important
  }

  [dir] .md\:mt-8 {
    margin-top: 2rem !important
  }

  [dir=ltr] .md\:mr-8 {
margin-right: 2rem !important
  }

  [dir=rtl] .md\:mr-8 {
    margin-left: 2rem !important
  }

  [dir] .md\:mb-8 {
    margin-bottom: 2rem !important
  }

  [dir=ltr] .md\:ml-8 {
margin-left: 2rem !important
  }

  [dir=rtl] .md\:ml-8 {
    margin-right: 2rem !important
  }

  [dir] .md\:mt-10 {
    margin-top: 2.5rem !important
  }

  [dir=ltr] .md\:mr-10 {
margin-right: 2.5rem !important
  }

  [dir=rtl] .md\:mr-10 {
    margin-left: 2.5rem !important
  }

  [dir] .md\:mb-10 {
    margin-bottom: 2.5rem !important
  }

  [dir=ltr] .md\:ml-10 {
margin-left: 2.5rem !important
  }

  [dir=rtl] .md\:ml-10 {
    margin-right: 2.5rem !important
  }

  [dir] .md\:mt-12 {
    margin-top: 3rem !important
  }

  [dir=ltr] .md\:mr-12 {
margin-right: 3rem !important
  }

  [dir=rtl] .md\:mr-12 {
    margin-left: 3rem !important
  }

  [dir] .md\:mb-12 {
    margin-bottom: 3rem !important
  }

  [dir=ltr] .md\:ml-12 {
margin-left: 3rem !important
  }

  [dir=rtl] .md\:ml-12 {
    margin-right: 3rem !important
  }

  [dir] .md\:mt-16 {
    margin-top: 4rem !important
  }

  [dir=ltr] .md\:mr-16 {
margin-right: 4rem !important
  }

  [dir=rtl] .md\:mr-16 {
    margin-left: 4rem !important
  }

  [dir] .md\:mb-16 {
    margin-bottom: 4rem !important
  }

  [dir=ltr] .md\:ml-16 {
margin-left: 4rem !important
  }

  [dir=rtl] .md\:ml-16 {
    margin-right: 4rem !important
  }

  [dir] .md\:mt-20 {
    margin-top: 5rem !important
  }

  [dir=ltr] .md\:mr-20 {
margin-right: 5rem !important
  }

  [dir=rtl] .md\:mr-20 {
    margin-left: 5rem !important
  }

  [dir] .md\:mb-20 {
    margin-bottom: 5rem !important
  }

  [dir=ltr] .md\:ml-20 {
margin-left: 5rem !important
  }

  [dir=rtl] .md\:ml-20 {
    margin-right: 5rem !important
  }

  [dir] .md\:mt-24 {
    margin-top: 6rem !important
  }

  [dir=ltr] .md\:mr-24 {
margin-right: 6rem !important
  }

  [dir=rtl] .md\:mr-24 {
    margin-left: 6rem !important
  }

  [dir] .md\:mb-24 {
    margin-bottom: 6rem !important
  }

  [dir=ltr] .md\:ml-24 {
margin-left: 6rem !important
  }

  [dir=rtl] .md\:ml-24 {
    margin-right: 6rem !important
  }

  [dir] .md\:mt-32 {
    margin-top: 8rem !important
  }

  [dir=ltr] .md\:mr-32 {
margin-right: 8rem !important
  }

  [dir=rtl] .md\:mr-32 {
    margin-left: 8rem !important
  }

  [dir] .md\:mb-32 {
    margin-bottom: 8rem !important
  }

  [dir=ltr] .md\:ml-32 {
margin-left: 8rem !important
  }

  [dir=rtl] .md\:ml-32 {
    margin-right: 8rem !important
  }

  [dir] .md\:mt-auto {
    margin-top: auto !important
  }

  [dir=ltr] .md\:mr-auto {
margin-right: auto !important
  }

  [dir=rtl] .md\:mr-auto {
    margin-left: auto !important
  }

  [dir] .md\:mb-auto {
    margin-bottom: auto !important
  }

  [dir=ltr] .md\:ml-auto {
margin-left: auto !important
  }

  [dir=rtl] .md\:ml-auto {
    margin-right: auto !important
  }

  [dir] .md\:mt-px {
    margin-top: 1px !important
  }

  [dir=ltr] .md\:mr-px {
margin-right: 1px !important
  }

  [dir=rtl] .md\:mr-px {
    margin-left: 1px !important
  }

  [dir] .md\:mb-px {
    margin-bottom: 1px !important
  }

  [dir=ltr] .md\:ml-px {
margin-left: 1px !important
  }

  [dir=rtl] .md\:ml-px {
    margin-right: 1px !important
  }

  [dir] .md\:mt-base {
    margin-top: 2.2rem !important
  }

  [dir=ltr] .md\:mr-base {
margin-right: 2.2rem !important
  }

  [dir=rtl] .md\:mr-base {
    margin-left: 2.2rem !important
  }

  [dir] .md\:mb-base {
    margin-bottom: 2.2rem !important
  }

  [dir=ltr] .md\:ml-base {
margin-left: 2.2rem !important
  }

  [dir=rtl] .md\:ml-base {
    margin-right: 2.2rem !important
  }

  [dir] .md\:-mt-px {
    margin-top: -1px !important
  }

  [dir=ltr] .md\:-mr-px {
margin-right: -1px !important
  }

  [dir=rtl] .md\:-mr-px {
    margin-left: -1px !important
  }

  [dir] .md\:-mb-px {
    margin-bottom: -1px !important
  }

  [dir=ltr] .md\:-ml-px {
margin-left: -1px !important
  }

  [dir=rtl] .md\:-ml-px {
    margin-right: -1px !important
  }

  [dir] .md\:-mt-1 {
    margin-top: -0.25rem !important
  }

  [dir=ltr] .md\:-mr-1 {
margin-right: -0.25rem !important
  }

  [dir=rtl] .md\:-mr-1 {
    margin-left: -0.25rem !important
  }

  [dir] .md\:-mb-1 {
    margin-bottom: -0.25rem !important
  }

  [dir=ltr] .md\:-ml-1 {
margin-left: -0.25rem !important
  }

  [dir=rtl] .md\:-ml-1 {
    margin-right: -0.25rem !important
  }

  [dir] .md\:-mt-2 {
    margin-top: -0.5rem !important
  }

  [dir=ltr] .md\:-mr-2 {
margin-right: -0.5rem !important
  }

  [dir=rtl] .md\:-mr-2 {
    margin-left: -0.5rem !important
  }

  [dir] .md\:-mb-2 {
    margin-bottom: -0.5rem !important
  }

  [dir=ltr] .md\:-ml-2 {
margin-left: -0.5rem !important
  }

  [dir=rtl] .md\:-ml-2 {
    margin-right: -0.5rem !important
  }

  [dir] .md\:-mt-3 {
    margin-top: -0.75rem !important
  }

  [dir=ltr] .md\:-mr-3 {
margin-right: -0.75rem !important
  }

  [dir=rtl] .md\:-mr-3 {
    margin-left: -0.75rem !important
  }

  [dir] .md\:-mb-3 {
    margin-bottom: -0.75rem !important
  }

  [dir=ltr] .md\:-ml-3 {
margin-left: -0.75rem !important
  }

  [dir=rtl] .md\:-ml-3 {
    margin-right: -0.75rem !important
  }

  [dir] .md\:-mt-4 {
    margin-top: -1rem !important
  }

  [dir=ltr] .md\:-mr-4 {
margin-right: -1rem !important
  }

  [dir=rtl] .md\:-mr-4 {
    margin-left: -1rem !important
  }

  [dir] .md\:-mb-4 {
    margin-bottom: -1rem !important
  }

  [dir=ltr] .md\:-ml-4 {
margin-left: -1rem !important
  }

  [dir=rtl] .md\:-ml-4 {
    margin-right: -1rem !important
  }

  .md\:max-h-full {
    max-height: 100% !important
  }

  .md\:max-h-screen {
    max-height: 100vh !important
  }

  .md\:max-w-xs {
    max-width: 20rem !important
  }

  .md\:max-w-sm {
    max-width: 30rem !important
  }

  .md\:max-w-md {
    max-width: 40rem !important
  }

  .md\:max-w-lg {
    max-width: 50rem !important
  }

  .md\:max-w-xl {
    max-width: 60rem !important
  }

  .md\:max-w-2xl {
    max-width: 70rem !important
  }

  .md\:max-w-3xl {
    max-width: 80rem !important
  }

  .md\:max-w-4xl {
    max-width: 90rem !important
  }

  .md\:max-w-5xl {
    max-width: 100rem !important
  }

  .md\:max-w-full {
    max-width: 100% !important
  }

  .md\:min-h-0 {
    min-height: 0 !important
  }

  .md\:min-h-full {
    min-height: 100% !important
  }

  .md\:min-h-screen {
    min-height: 100vh !important
  }

  .md\:min-w-0 {
    min-width: 0 !important
  }

  .md\:min-w-full {
    min-width: 100% !important
  }

  .md\:object-contain {
    -o-object-fit: contain !important;
       object-fit: contain !important
  }

  .md\:object-cover {
    -o-object-fit: cover !important;
       object-fit: cover !important
  }

  .md\:object-fill {
    -o-object-fit: fill !important;
       object-fit: fill !important
  }

  .md\:object-none {
    -o-object-fit: none !important;
       object-fit: none !important
  }

  .md\:object-scale-down {
    -o-object-fit: scale-down !important;
       object-fit: scale-down !important
  }

  .md\:object-bottom {
    -o-object-position: bottom !important;
       object-position: bottom !important
  }

  .md\:object-center {
    -o-object-position: center !important;
       object-position: center !important
  }

  .md\:object-left {
    -o-object-position: left !important;
       object-position: left !important
  }

  .md\:object-left-bottom {
    -o-object-position: left bottom !important;
       object-position: left bottom !important
  }

  .md\:object-left-top {
    -o-object-position: left top !important;
       object-position: left top !important
  }

  .md\:object-right {
    -o-object-position: right !important;
       object-position: right !important
  }

  .md\:object-right-bottom {
    -o-object-position: right bottom !important;
       object-position: right bottom !important
  }

  .md\:object-right-top {
    -o-object-position: right top !important;
       object-position: right top !important
  }

  .md\:object-top {
    -o-object-position: top !important;
       object-position: top !important
  }

  .md\:opacity-0 {
    opacity: 0 !important
  }

  .md\:opacity-25 {
    opacity: 0.25 !important
  }

  .md\:opacity-50 {
    opacity: 0.5 !important
  }

  .md\:opacity-75 {
    opacity: 0.75 !important
  }

  .md\:opacity-100 {
    opacity: 1 !important
  }

  .md\:overflow-auto {
    overflow: auto !important
  }

  .md\:overflow-hidden {
    overflow: hidden !important
  }

  .md\:overflow-visible {
    overflow: visible !important
  }

  .md\:overflow-scroll {
    overflow: scroll !important
  }

  .md\:overflow-x-auto {
    overflow-x: auto !important
  }

  .md\:overflow-y-auto {
    overflow-y: auto !important
  }

  .md\:overflow-x-hidden {
    overflow-x: hidden !important
  }

  .md\:overflow-y-hidden {
    overflow-y: hidden !important
  }

  .md\:overflow-x-visible {
    overflow-x: visible !important
  }

  .md\:overflow-y-visible {
    overflow-y: visible !important
  }

  .md\:overflow-x-scroll {
    overflow-x: scroll !important
  }

  .md\:overflow-y-scroll {
    overflow-y: scroll !important
  }

  .md\:scrolling-touch {
    -webkit-overflow-scrolling: touch !important
  }

  .md\:scrolling-auto {
    -webkit-overflow-scrolling: auto !important
  }

  [dir] .md\:p-0 {
    padding: 0 !important
  }

  [dir] .md\:p-1 {
    padding: 0.25rem !important
  }

  [dir] .md\:p-2 {
    padding: 0.5rem !important
  }

  [dir] .md\:p-3 {
    padding: 0.75rem !important
  }

  [dir] .md\:p-4 {
    padding: 1rem !important
  }

  [dir] .md\:p-5 {
    padding: 1.25rem !important
  }

  [dir] .md\:p-6 {
    padding: 1.5rem !important
  }

  [dir] .md\:p-8 {
    padding: 2rem !important
  }

  [dir] .md\:p-10 {
    padding: 2.5rem !important
  }

  [dir] .md\:p-12 {
    padding: 3rem !important
  }

  [dir] .md\:p-16 {
    padding: 4rem !important
  }

  [dir] .md\:p-20 {
    padding: 5rem !important
  }

  [dir] .md\:p-24 {
    padding: 6rem !important
  }

  [dir] .md\:p-32 {
    padding: 8rem !important
  }

  [dir] .md\:p-px {
    padding: 1px !important
  }

  [dir] .md\:p-base {
    padding: 2.2rem !important
  }

  [dir] .md\:py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important
  }

  [dir=ltr] .md\:px-0 {
padding-left: 0 !important;
padding-right: 0 !important
  }

  [dir=rtl] .md\:px-0 {
    padding-right: 0 !important;
    padding-left: 0 !important
  }

  [dir] .md\:py-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important
  }

  [dir=ltr] .md\:px-1 {
padding-left: 0.25rem !important;
padding-right: 0.25rem !important
  }

  [dir=rtl] .md\:px-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important
  }

  [dir] .md\:py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important
  }

  [dir=ltr] .md\:px-2 {
padding-left: 0.5rem !important;
padding-right: 0.5rem !important
  }

  [dir=rtl] .md\:px-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important
  }

  [dir] .md\:py-3 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important
  }

  [dir=ltr] .md\:px-3 {
padding-left: 0.75rem !important;
padding-right: 0.75rem !important
  }

  [dir=rtl] .md\:px-3 {
    padding-right: 0.75rem !important;
    padding-left: 0.75rem !important
  }

  [dir] .md\:py-4 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important
  }

  [dir=ltr] .md\:px-4 {
padding-left: 1rem !important;
padding-right: 1rem !important
  }

  [dir=rtl] .md\:px-4 {
    padding-right: 1rem !important;
    padding-left: 1rem !important
  }

  [dir] .md\:py-5 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important
  }

  [dir=ltr] .md\:px-5 {
padding-left: 1.25rem !important;
padding-right: 1.25rem !important
  }

  [dir=rtl] .md\:px-5 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important
  }

  [dir] .md\:py-6 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important
  }

  [dir=ltr] .md\:px-6 {
padding-left: 1.5rem !important;
padding-right: 1.5rem !important
  }

  [dir=rtl] .md\:px-6 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important
  }

  [dir] .md\:py-8 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important
  }

  [dir=ltr] .md\:px-8 {
padding-left: 2rem !important;
padding-right: 2rem !important
  }

  [dir=rtl] .md\:px-8 {
    padding-right: 2rem !important;
    padding-left: 2rem !important
  }

  [dir] .md\:py-10 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important
  }

  [dir=ltr] .md\:px-10 {
padding-left: 2.5rem !important;
padding-right: 2.5rem !important
  }

  [dir=rtl] .md\:px-10 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important
  }

  [dir] .md\:py-12 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important
  }

  [dir=ltr] .md\:px-12 {
padding-left: 3rem !important;
padding-right: 3rem !important
  }

  [dir=rtl] .md\:px-12 {
    padding-right: 3rem !important;
    padding-left: 3rem !important
  }

  [dir] .md\:py-16 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important
  }

  [dir=ltr] .md\:px-16 {
padding-left: 4rem !important;
padding-right: 4rem !important
  }

  [dir=rtl] .md\:px-16 {
    padding-right: 4rem !important;
    padding-left: 4rem !important
  }

  [dir] .md\:py-20 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important
  }

  [dir=ltr] .md\:px-20 {
padding-left: 5rem !important;
padding-right: 5rem !important
  }

  [dir=rtl] .md\:px-20 {
    padding-right: 5rem !important;
    padding-left: 5rem !important
  }

  [dir] .md\:py-24 {
    padding-top: 6rem !important;
    padding-bottom: 6rem !important
  }

  [dir=ltr] .md\:px-24 {
padding-left: 6rem !important;
padding-right: 6rem !important
  }

  [dir=rtl] .md\:px-24 {
    padding-right: 6rem !important;
    padding-left: 6rem !important
  }

  [dir] .md\:py-32 {
    padding-top: 8rem !important;
    padding-bottom: 8rem !important
  }

  [dir=ltr] .md\:px-32 {
padding-left: 8rem !important;
padding-right: 8rem !important
  }

  [dir=rtl] .md\:px-32 {
    padding-right: 8rem !important;
    padding-left: 8rem !important
  }

  [dir] .md\:py-px {
    padding-top: 1px !important;
    padding-bottom: 1px !important
  }

  [dir=ltr] .md\:px-px {
padding-left: 1px !important;
padding-right: 1px !important
  }

  [dir=rtl] .md\:px-px {
    padding-right: 1px !important;
    padding-left: 1px !important
  }

  [dir] .md\:py-base {
    padding-top: 2.2rem !important;
    padding-bottom: 2.2rem !important
  }

  [dir=ltr] .md\:px-base {
padding-left: 2.2rem !important;
padding-right: 2.2rem !important
  }

  [dir=rtl] .md\:px-base {
    padding-right: 2.2rem !important;
    padding-left: 2.2rem !important
  }

  [dir] .md\:pt-0 {
    padding-top: 0 !important
  }

  [dir=ltr] .md\:pr-0 {
padding-right: 0 !important
  }

  [dir=rtl] .md\:pr-0 {
    padding-left: 0 !important
  }

  [dir] .md\:pb-0 {
    padding-bottom: 0 !important
  }

  [dir=ltr] .md\:pl-0 {
padding-left: 0 !important
  }

  [dir=rtl] .md\:pl-0 {
    padding-right: 0 !important
  }

  [dir] .md\:pt-1 {
    padding-top: 0.25rem !important
  }

  [dir=ltr] .md\:pr-1 {
padding-right: 0.25rem !important
  }

  [dir=rtl] .md\:pr-1 {
    padding-left: 0.25rem !important
  }

  [dir] .md\:pb-1 {
    padding-bottom: 0.25rem !important
  }

  [dir=ltr] .md\:pl-1 {
padding-left: 0.25rem !important
  }

  [dir=rtl] .md\:pl-1 {
    padding-right: 0.25rem !important
  }

  [dir] .md\:pt-2 {
    padding-top: 0.5rem !important
  }

  [dir=ltr] .md\:pr-2 {
padding-right: 0.5rem !important
  }

  [dir=rtl] .md\:pr-2 {
    padding-left: 0.5rem !important
  }

  [dir] .md\:pb-2 {
    padding-bottom: 0.5rem !important
  }

  [dir=ltr] .md\:pl-2 {
padding-left: 0.5rem !important
  }

  [dir=rtl] .md\:pl-2 {
    padding-right: 0.5rem !important
  }

  [dir] .md\:pt-3 {
    padding-top: 0.75rem !important
  }

  [dir=ltr] .md\:pr-3 {
padding-right: 0.75rem !important
  }

  [dir=rtl] .md\:pr-3 {
    padding-left: 0.75rem !important
  }

  [dir] .md\:pb-3 {
    padding-bottom: 0.75rem !important
  }

  [dir=ltr] .md\:pl-3 {
padding-left: 0.75rem !important
  }

  [dir=rtl] .md\:pl-3 {
    padding-right: 0.75rem !important
  }

  [dir] .md\:pt-4 {
    padding-top: 1rem !important
  }

  [dir=ltr] .md\:pr-4 {
padding-right: 1rem !important
  }

  [dir=rtl] .md\:pr-4 {
    padding-left: 1rem !important
  }

  [dir] .md\:pb-4 {
    padding-bottom: 1rem !important
  }

  [dir=ltr] .md\:pl-4 {
padding-left: 1rem !important
  }

  [dir=rtl] .md\:pl-4 {
    padding-right: 1rem !important
  }

  [dir] .md\:pt-5 {
    padding-top: 1.25rem !important
  }

  [dir=ltr] .md\:pr-5 {
padding-right: 1.25rem !important
  }

  [dir=rtl] .md\:pr-5 {
    padding-left: 1.25rem !important
  }

  [dir] .md\:pb-5 {
    padding-bottom: 1.25rem !important
  }

  [dir=ltr] .md\:pl-5 {
padding-left: 1.25rem !important
  }

  [dir=rtl] .md\:pl-5 {
    padding-right: 1.25rem !important
  }

  [dir] .md\:pt-6 {
    padding-top: 1.5rem !important
  }

  [dir=ltr] .md\:pr-6 {
padding-right: 1.5rem !important
  }

  [dir=rtl] .md\:pr-6 {
    padding-left: 1.5rem !important
  }

  [dir] .md\:pb-6 {
    padding-bottom: 1.5rem !important
  }

  [dir=ltr] .md\:pl-6 {
padding-left: 1.5rem !important
  }

  [dir=rtl] .md\:pl-6 {
    padding-right: 1.5rem !important
  }

  [dir] .md\:pt-8 {
    padding-top: 2rem !important
  }

  [dir=ltr] .md\:pr-8 {
padding-right: 2rem !important
  }

  [dir=rtl] .md\:pr-8 {
    padding-left: 2rem !important
  }

  [dir] .md\:pb-8 {
    padding-bottom: 2rem !important
  }

  [dir=ltr] .md\:pl-8 {
padding-left: 2rem !important
  }

  [dir=rtl] .md\:pl-8 {
    padding-right: 2rem !important
  }

  [dir] .md\:pt-10 {
    padding-top: 2.5rem !important
  }

  [dir=ltr] .md\:pr-10 {
padding-right: 2.5rem !important
  }

  [dir=rtl] .md\:pr-10 {
    padding-left: 2.5rem !important
  }

  [dir] .md\:pb-10 {
    padding-bottom: 2.5rem !important
  }

  [dir=ltr] .md\:pl-10 {
padding-left: 2.5rem !important
  }

  [dir=rtl] .md\:pl-10 {
    padding-right: 2.5rem !important
  }

  [dir] .md\:pt-12 {
    padding-top: 3rem !important
  }

  [dir=ltr] .md\:pr-12 {
padding-right: 3rem !important
  }

  [dir=rtl] .md\:pr-12 {
    padding-left: 3rem !important
  }

  [dir] .md\:pb-12 {
    padding-bottom: 3rem !important
  }

  [dir=ltr] .md\:pl-12 {
padding-left: 3rem !important
  }

  [dir=rtl] .md\:pl-12 {
    padding-right: 3rem !important
  }

  [dir] .md\:pt-16 {
    padding-top: 4rem !important
  }

  [dir=ltr] .md\:pr-16 {
padding-right: 4rem !important
  }

  [dir=rtl] .md\:pr-16 {
    padding-left: 4rem !important
  }

  [dir] .md\:pb-16 {
    padding-bottom: 4rem !important
  }

  [dir=ltr] .md\:pl-16 {
padding-left: 4rem !important
  }

  [dir=rtl] .md\:pl-16 {
    padding-right: 4rem !important
  }

  [dir] .md\:pt-20 {
    padding-top: 5rem !important
  }

  [dir=ltr] .md\:pr-20 {
padding-right: 5rem !important
  }

  [dir=rtl] .md\:pr-20 {
    padding-left: 5rem !important
  }

  [dir] .md\:pb-20 {
    padding-bottom: 5rem !important
  }

  [dir=ltr] .md\:pl-20 {
padding-left: 5rem !important
  }

  [dir=rtl] .md\:pl-20 {
    padding-right: 5rem !important
  }

  [dir] .md\:pt-24 {
    padding-top: 6rem !important
  }

  [dir=ltr] .md\:pr-24 {
padding-right: 6rem !important
  }

  [dir=rtl] .md\:pr-24 {
    padding-left: 6rem !important
  }

  [dir] .md\:pb-24 {
    padding-bottom: 6rem !important
  }

  [dir=ltr] .md\:pl-24 {
padding-left: 6rem !important
  }

  [dir=rtl] .md\:pl-24 {
    padding-right: 6rem !important
  }

  [dir] .md\:pt-32 {
    padding-top: 8rem !important
  }

  [dir=ltr] .md\:pr-32 {
padding-right: 8rem !important
  }

  [dir=rtl] .md\:pr-32 {
    padding-left: 8rem !important
  }

  [dir] .md\:pb-32 {
    padding-bottom: 8rem !important
  }

  [dir=ltr] .md\:pl-32 {
padding-left: 8rem !important
  }

  [dir=rtl] .md\:pl-32 {
    padding-right: 8rem !important
  }

  [dir] .md\:pt-px {
    padding-top: 1px !important
  }

  [dir=ltr] .md\:pr-px {
padding-right: 1px !important
  }

  [dir=rtl] .md\:pr-px {
    padding-left: 1px !important
  }

  [dir] .md\:pb-px {
    padding-bottom: 1px !important
  }

  [dir=ltr] .md\:pl-px {
padding-left: 1px !important
  }

  [dir=rtl] .md\:pl-px {
    padding-right: 1px !important
  }

  [dir] .md\:pt-base {
    padding-top: 2.2rem !important
  }

  [dir=ltr] .md\:pr-base {
padding-right: 2.2rem !important
  }

  [dir=rtl] .md\:pr-base {
    padding-left: 2.2rem !important
  }

  [dir] .md\:pb-base {
    padding-bottom: 2.2rem !important
  }

  [dir=ltr] .md\:pl-base {
padding-left: 2.2rem !important
  }

  [dir=rtl] .md\:pl-base {
    padding-right: 2.2rem !important
  }

  .md\:placeholder-transparent::-webkit-input-placeholder {
    color: transparent !important
  }

  .md\:placeholder-transparent::-moz-placeholder {
    color: transparent !important
  }

  .md\:placeholder-transparent:-ms-input-placeholder {
    color: transparent !important
  }

  .md\:placeholder-transparent::-ms-input-placeholder {
    color: transparent !important
  }

  .md\:placeholder-transparent::placeholder {
    color: transparent !important
  }

  .md\:placeholder-black::-webkit-input-placeholder {
    color: #22292f !important
  }

  .md\:placeholder-black::-moz-placeholder {
    color: #22292f !important
  }

  .md\:placeholder-black:-ms-input-placeholder {
    color: #22292f !important
  }

  .md\:placeholder-black::-ms-input-placeholder {
    color: #22292f !important
  }

  .md\:placeholder-black::placeholder {
    color: #22292f !important
  }

  .md\:placeholder-white::-webkit-input-placeholder {
    color: #ffffff !important
  }

  .md\:placeholder-white::-moz-placeholder {
    color: #ffffff !important
  }

  .md\:placeholder-white:-ms-input-placeholder {
    color: #ffffff !important
  }

  .md\:placeholder-white::-ms-input-placeholder {
    color: #ffffff !important
  }

  .md\:placeholder-white::placeholder {
    color: #ffffff !important
  }

  .md\:placeholder-grey::-webkit-input-placeholder {
    color: #b8c2cc !important
  }

  .md\:placeholder-grey::-moz-placeholder {
    color: #b8c2cc !important
  }

  .md\:placeholder-grey:-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .md\:placeholder-grey::-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .md\:placeholder-grey::placeholder {
    color: #b8c2cc !important
  }

  .md\:placeholder-grey-light::-webkit-input-placeholder {
    color: #dae1e7 !important
  }

  .md\:placeholder-grey-light::-moz-placeholder {
    color: #dae1e7 !important
  }

  .md\:placeholder-grey-light:-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .md\:placeholder-grey-light::-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .md\:placeholder-grey-light::placeholder {
    color: #dae1e7 !important
  }

  .md\:focus\:placeholder-transparent:focus::-webkit-input-placeholder {
    color: transparent !important
  }

  .md\:focus\:placeholder-transparent:focus::-moz-placeholder {
    color: transparent !important
  }

  .md\:focus\:placeholder-transparent:focus:-ms-input-placeholder {
    color: transparent !important
  }

  .md\:focus\:placeholder-transparent:focus::-ms-input-placeholder {
    color: transparent !important
  }

  .md\:focus\:placeholder-transparent:focus::placeholder {
    color: transparent !important
  }

  .md\:focus\:placeholder-black:focus::-webkit-input-placeholder {
    color: #22292f !important
  }

  .md\:focus\:placeholder-black:focus::-moz-placeholder {
    color: #22292f !important
  }

  .md\:focus\:placeholder-black:focus:-ms-input-placeholder {
    color: #22292f !important
  }

  .md\:focus\:placeholder-black:focus::-ms-input-placeholder {
    color: #22292f !important
  }

  .md\:focus\:placeholder-black:focus::placeholder {
    color: #22292f !important
  }

  .md\:focus\:placeholder-white:focus::-webkit-input-placeholder {
    color: #ffffff !important
  }

  .md\:focus\:placeholder-white:focus::-moz-placeholder {
    color: #ffffff !important
  }

  .md\:focus\:placeholder-white:focus:-ms-input-placeholder {
    color: #ffffff !important
  }

  .md\:focus\:placeholder-white:focus::-ms-input-placeholder {
    color: #ffffff !important
  }

  .md\:focus\:placeholder-white:focus::placeholder {
    color: #ffffff !important
  }

  .md\:focus\:placeholder-grey:focus::-webkit-input-placeholder {
    color: #b8c2cc !important
  }

  .md\:focus\:placeholder-grey:focus::-moz-placeholder {
    color: #b8c2cc !important
  }

  .md\:focus\:placeholder-grey:focus:-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .md\:focus\:placeholder-grey:focus::-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .md\:focus\:placeholder-grey:focus::placeholder {
    color: #b8c2cc !important
  }

  .md\:focus\:placeholder-grey-light:focus::-webkit-input-placeholder {
    color: #dae1e7 !important
  }

  .md\:focus\:placeholder-grey-light:focus::-moz-placeholder {
    color: #dae1e7 !important
  }

  .md\:focus\:placeholder-grey-light:focus:-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .md\:focus\:placeholder-grey-light:focus::-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .md\:focus\:placeholder-grey-light:focus::placeholder {
    color: #dae1e7 !important
  }

  .md\:pointer-events-none {
    pointer-events: none !important
  }

  .md\:pointer-events-auto {
    pointer-events: auto !important
  }

  .md\:static {
    position: static !important
  }

  .md\:fixed {
    position: fixed !important
  }

  .md\:absolute {
    position: absolute !important
  }

  .md\:relative {
    position: relative !important
  }

  .md\:sticky {
    position: -webkit-sticky !important;
    position: sticky !important
  }

  .md\:inset-0 {
    top: 0 !important;
    bottom: 0 !important
  }

  [dir=ltr] .md\:inset-0 {
right: 0 !important;
left: 0 !important
  }

  [dir=rtl] .md\:inset-0 {
    left: 0 !important;
    right: 0 !important
  }

  .md\:inset-auto {
    top: auto !important;
    bottom: auto !important
  }

  [dir=ltr] .md\:inset-auto {
right: auto !important;
left: auto !important
  }

  [dir=rtl] .md\:inset-auto {
    left: auto !important;
    right: auto !important
  }

  .md\:inset-y-0 {
    top: 0 !important;
    bottom: 0 !important
  }

  [dir=ltr] .md\:inset-x-0 {
right: 0 !important;
left: 0 !important
  }

  [dir=rtl] .md\:inset-x-0 {
    left: 0 !important;
    right: 0 !important
  }

  .md\:inset-y-auto {
    top: auto !important;
    bottom: auto !important
  }

  [dir=ltr] .md\:inset-x-auto {
right: auto !important;
left: auto !important
  }

  [dir=rtl] .md\:inset-x-auto {
    left: auto !important;
    right: auto !important
  }

  .md\:top-0 {
    top: 0 !important
  }

  [dir=ltr] .md\:right-0 {
right: 0 !important
  }

  [dir=rtl] .md\:right-0 {
    left: 0 !important
  }

  .md\:bottom-0 {
    bottom: 0 !important
  }

  [dir=ltr] .md\:left-0 {
left: 0 !important
  }

  [dir=rtl] .md\:left-0 {
    right: 0 !important
  }

  .md\:top-auto {
    top: auto !important
  }

  [dir=ltr] .md\:right-auto {
right: auto !important
  }

  [dir=rtl] .md\:right-auto {
    left: auto !important
  }

  .md\:bottom-auto {
    bottom: auto !important
  }

  [dir=ltr] .md\:left-auto {
left: auto !important
  }

  [dir=rtl] .md\:left-auto {
    right: auto !important
  }

  .md\:resize-none {
    resize: none !important
  }

  .md\:resize-y {
    resize: vertical !important
  }

  .md\:resize-x {
    resize: horizontal !important
  }

  .md\:resize {
    resize: both !important
  }

  [dir] .md\:shadow {
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
  }

  [dir] .md\:shadow-md {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .md\:shadow-lg {
    box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .md\:shadow-inner {
    box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
  }

  [dir] .md\:shadow-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
  }

  [dir] .md\:shadow-2xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
  }

  [dir] .md\:shadow-outline {
    box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
  }

  [dir] .md\:shadow-none {
    box-shadow: none !important
  }

  [dir] .md\:shadow-drop {
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
  }

  [dir] .md\:hover\:shadow:hover {
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
  }

  [dir] .md\:hover\:shadow-md:hover {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .md\:hover\:shadow-lg:hover {
    box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .md\:hover\:shadow-inner:hover {
    box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
  }

  [dir] .md\:hover\:shadow-xl:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
  }

  [dir] .md\:hover\:shadow-2xl:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
  }

  [dir] .md\:hover\:shadow-outline:hover {
    box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
  }

  [dir] .md\:hover\:shadow-none:hover {
    box-shadow: none !important
  }

  [dir] .md\:hover\:shadow-drop:hover {
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
  }

  [dir] .md\:focus\:shadow:focus {
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
  }

  [dir] .md\:focus\:shadow-md:focus {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .md\:focus\:shadow-lg:focus {
    box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .md\:focus\:shadow-inner:focus {
    box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
  }

  [dir] .md\:focus\:shadow-xl:focus {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
  }

  [dir] .md\:focus\:shadow-2xl:focus {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
  }

  [dir] .md\:focus\:shadow-outline:focus {
    box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
  }

  [dir] .md\:focus\:shadow-none:focus {
    box-shadow: none !important
  }

  [dir] .md\:focus\:shadow-drop:focus {
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
  }

  .md\:table-auto {
    table-layout: auto !important
  }

  .md\:table-fixed {
    table-layout: fixed !important
  }

  [dir=ltr] .md\:text-left {
text-align: left !important
  }

  [dir=rtl] .md\:text-left {
    text-align: right !important
  }

  [dir] .md\:text-center {
    text-align: center !important
  }

  [dir=ltr] .md\:text-right {
text-align: right !important
  }

  [dir=rtl] .md\:text-right {
    text-align: left !important
  }

  [dir] .md\:text-justify {
    text-align: justify !important
  }

  .md\:text-inherit {
    color: inherit !important
  }

  .md\:text-transparent {
    color: transparent !important
  }

  .md\:text-black {
    color: #22292f !important
  }

  .md\:text-white {
    color: #ffffff !important
  }

  .md\:text-grey {
    color: #b8c2cc !important
  }

  .md\:text-grey-light {
    color: #dae1e7 !important
  }

  .md\:hover\:text-inherit:hover {
    color: inherit !important
  }

  .md\:hover\:text-transparent:hover {
    color: transparent !important
  }

  .md\:hover\:text-black:hover {
    color: #22292f !important
  }

  .md\:hover\:text-white:hover {
    color: #ffffff !important
  }

  .md\:hover\:text-grey:hover {
    color: #b8c2cc !important
  }

  .md\:hover\:text-grey-light:hover {
    color: #dae1e7 !important
  }

  .md\:focus\:text-inherit:focus {
    color: inherit !important
  }

  .md\:focus\:text-transparent:focus {
    color: transparent !important
  }

  .md\:focus\:text-black:focus {
    color: #22292f !important
  }

  .md\:focus\:text-white:focus {
    color: #ffffff !important
  }

  .md\:focus\:text-grey:focus {
    color: #b8c2cc !important
  }

  .md\:focus\:text-grey-light:focus {
    color: #dae1e7 !important
  }

  .md\:text-xs {
    font-size: .75rem !important
  }

  .md\:text-sm {
    font-size: .875rem !important
  }

  .md\:text-base {
    font-size: 1rem !important
  }

  .md\:text-lg {
    font-size: 1.125rem !important
  }

  .md\:text-xl {
    font-size: 1.25rem !important
  }

  .md\:text-2xl {
    font-size: 1.5rem !important
  }

  .md\:text-3xl {
    font-size: 1.875rem !important
  }

  .md\:text-4xl {
    font-size: 2.25rem !important
  }

  .md\:text-5xl {
    font-size: 3rem !important
  }

  .md\:text-6xl {
    font-size: 4rem !important
  }

  .md\:italic {
    font-style: italic !important
  }

  .md\:not-italic {
    font-style: normal !important
  }

  .md\:hover\:italic:hover {
    font-style: italic !important
  }

  .md\:hover\:not-italic:hover {
    font-style: normal !important
  }

  .md\:focus\:italic:focus {
    font-style: italic !important
  }

  .md\:focus\:not-italic:focus {
    font-style: normal !important
  }

  .md\:uppercase {
    text-transform: uppercase !important
  }

  .md\:lowercase {
    text-transform: lowercase !important
  }

  .md\:capitalize {
    text-transform: capitalize !important
  }

  .md\:normal-case {
    text-transform: none !important
  }

  .md\:hover\:uppercase:hover {
    text-transform: uppercase !important
  }

  .md\:hover\:lowercase:hover {
    text-transform: lowercase !important
  }

  .md\:hover\:capitalize:hover {
    text-transform: capitalize !important
  }

  .md\:hover\:normal-case:hover {
    text-transform: none !important
  }

  .md\:focus\:uppercase:focus {
    text-transform: uppercase !important
  }

  .md\:focus\:lowercase:focus {
    text-transform: lowercase !important
  }

  .md\:focus\:capitalize:focus {
    text-transform: capitalize !important
  }

  .md\:focus\:normal-case:focus {
    text-transform: none !important
  }

  .md\:underline {
    text-decoration: underline !important
  }

  .md\:line-through {
    text-decoration: line-through !important
  }

  .md\:no-underline {
    text-decoration: none !important
  }

  .md\:hover\:underline:hover {
    text-decoration: underline !important
  }

  .md\:hover\:line-through:hover {
    text-decoration: line-through !important
  }

  .md\:hover\:no-underline:hover {
    text-decoration: none !important
  }

  .md\:focus\:underline:focus {
    text-decoration: underline !important
  }

  .md\:focus\:line-through:focus {
    text-decoration: line-through !important
  }

  .md\:focus\:no-underline:focus {
    text-decoration: none !important
  }

  .md\:antialiased {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important
  }

  .md\:subpixel-antialiased {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important
  }

  .md\:hover\:antialiased:hover {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important
  }

  .md\:hover\:subpixel-antialiased:hover {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important
  }

  .md\:focus\:antialiased:focus {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important
  }

  .md\:focus\:subpixel-antialiased:focus {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important
  }

  .md\:select-none {
    -webkit-user-select: none !important;
       -moz-user-select: none !important;
        -ms-user-select: none !important;
            user-select: none !important
  }

  .md\:select-text {
    -webkit-user-select: text !important;
       -moz-user-select: text !important;
        -ms-user-select: text !important;
            user-select: text !important
  }

  .md\:select-all {
    -webkit-user-select: all !important;
       -moz-user-select: all !important;
        -ms-user-select: all !important;
            user-select: all !important
  }

  .md\:select-auto {
    -webkit-user-select: auto !important;
       -moz-user-select: auto !important;
        -ms-user-select: auto !important;
            user-select: auto !important
  }

  .md\:align-baseline {
    vertical-align: baseline !important
  }

  .md\:align-top {
    vertical-align: top !important
  }

  .md\:align-middle {
    vertical-align: middle !important
  }

  .md\:align-bottom {
    vertical-align: bottom !important
  }

  .md\:align-text-top {
    vertical-align: text-top !important
  }

  .md\:align-text-bottom {
    vertical-align: text-bottom !important
  }

  .md\:visible {
    visibility: visible !important
  }

  .md\:invisible {
    visibility: hidden !important
  }

  .md\:whitespace-normal {
    white-space: normal !important
  }

  .md\:whitespace-no-wrap {
    white-space: nowrap !important
  }

  .md\:whitespace-pre {
    white-space: pre !important
  }

  .md\:whitespace-pre-line {
    white-space: pre-line !important
  }

  .md\:whitespace-pre-wrap {
    white-space: pre-wrap !important
  }

  .md\:break-normal {
    overflow-wrap: normal !important;
    word-break: normal !important
  }

  .md\:break-words {
    overflow-wrap: break-word !important
  }

  .md\:break-all {
    word-break: break-all !important
  }

  .md\:truncate {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important
  }

  .md\:w-1 {
    width: 0.25rem !important
  }

  .md\:w-2 {
    width: 0.5rem !important
  }

  .md\:w-3 {
    width: 0.75rem !important
  }

  .md\:w-4 {
    width: 1rem !important
  }

  .md\:w-5 {
    width: 1.25rem !important
  }

  .md\:w-6 {
    width: 1.5rem !important
  }

  .md\:w-8 {
    width: 2rem !important
  }

  .md\:w-10 {
    width: 2.5rem !important
  }

  .md\:w-12 {
    width: 3rem !important
  }

  .md\:w-16 {
    width: 4rem !important
  }

  .md\:w-24 {
    width: 6rem !important
  }

  .md\:w-32 {
    width: 8rem !important
  }

  .md\:w-48 {
    width: 12rem !important
  }

  .md\:w-64 {
    width: 16rem !important
  }

  .md\:w-auto {
    width: auto !important
  }

  .md\:w-px {
    width: 1px !important
  }

  .md\:w-1\/2 {
    width: 50% !important
  }

  .md\:w-1\/3 {
    width: 33.33333% !important
  }

  .md\:w-2\/3 {
    width: 66.66667% !important
  }

  .md\:w-1\/4 {
    width: 25% !important
  }

  .md\:w-3\/4 {
    width: 75% !important
  }

  .md\:w-1\/5 {
    width: 20% !important
  }

  .md\:w-2\/5 {
    width: 40% !important
  }

  .md\:w-3\/5 {
    width: 60% !important
  }

  .md\:w-4\/5 {
    width: 80% !important
  }

  .md\:w-1\/6 {
    width: 16.66667% !important
  }

  .md\:w-5\/6 {
    width: 83.33333% !important
  }

  .md\:w-1\/12 {
    width: 8.33333% !important
  }

  .md\:w-2\/12 {
    width: 16.66667% !important
  }

  .md\:w-3\/12 {
    width: 25% !important
  }

  .md\:w-4\/12 {
    width: 33.33333% !important
  }

  .md\:w-5\/12 {
    width: 41.66667% !important
  }

  .md\:w-6\/12 {
    width: 50% !important
  }

  .md\:w-7\/12 {
    width: 58.33333% !important
  }

  .md\:w-8\/12 {
    width: 66.66667% !important
  }

  .md\:w-9\/12 {
    width: 75% !important
  }

  .md\:w-10\/12 {
    width: 83.33333% !important
  }

  .md\:w-11\/12 {
    width: 91.66667% !important
  }

  .md\:w-full {
    width: 100% !important
  }

  .md\:w-screen {
    width: 100vw !important
  }

  .md\:z-0 {
    z-index: 0 !important
  }

  .md\:z-10 {
    z-index: 10 !important
  }

  .md\:z-20 {
    z-index: 20 !important
  }

  .md\:z-30 {
    z-index: 30 !important
  }

  .md\:z-40 {
    z-index: 40 !important
  }

  .md\:z-50 {
    z-index: 50 !important
  }

  .md\:z-auto {
    z-index: auto !important
  }
}

@media (min-width: 992px) {
  .lg\:sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important
  }
  [dir] .lg\:sr-only {
    padding: 0 !important;
    margin: -1px !important;
    border-width: 0 !important
  }

  .lg\:not-sr-only {
    position: static !important;
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important
  }

  [dir] .lg\:not-sr-only {
    padding: 0 !important;
    margin: 0 !important
  }

  .lg\:focus\:sr-only:focus {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important
  }

  [dir] .lg\:focus\:sr-only:focus {
    padding: 0 !important;
    margin: -1px !important;
    border-width: 0 !important
  }

  .lg\:focus\:not-sr-only:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important
  }

  [dir] .lg\:focus\:not-sr-only:focus {
    padding: 0 !important;
    margin: 0 !important
  }

  .lg\:appearance-none {
    -webkit-appearance: none !important;
       -moz-appearance: none !important;
            appearance: none !important
  }

  [dir] .lg\:bg-fixed {
    background-attachment: fixed !important
  }

  [dir] .lg\:bg-local {
    background-attachment: local !important
  }

  [dir] .lg\:bg-scroll {
    background-attachment: scroll !important
  }

  [dir] .lg\:bg-transparent {
    background-color: transparent !important
  }

  [dir] .lg\:bg-black {
    background-color: #22292f !important
  }

  [dir] .lg\:bg-white {
    background-color: #ffffff !important
  }

  [dir] .lg\:bg-grey {
    background-color: #b8c2cc !important
  }

  [dir] .lg\:bg-grey-light {
    background-color: #dae1e7 !important
  }

  [dir] .lg\:hover\:bg-transparent:hover {
    background-color: transparent !important
  }

  [dir] .lg\:hover\:bg-black:hover {
    background-color: #22292f !important
  }

  [dir] .lg\:hover\:bg-white:hover {
    background-color: #ffffff !important
  }

  [dir] .lg\:hover\:bg-grey:hover {
    background-color: #b8c2cc !important
  }

  [dir] .lg\:hover\:bg-grey-light:hover {
    background-color: #dae1e7 !important
  }

  [dir] .lg\:focus\:bg-transparent:focus {
    background-color: transparent !important
  }

  [dir] .lg\:focus\:bg-black:focus {
    background-color: #22292f !important
  }

  [dir] .lg\:focus\:bg-white:focus {
    background-color: #ffffff !important
  }

  [dir] .lg\:focus\:bg-grey:focus {
    background-color: #b8c2cc !important
  }

  [dir] .lg\:focus\:bg-grey-light:focus {
    background-color: #dae1e7 !important
  }

  [dir] .lg\:bg-auto {
    background-size: auto !important
  }

  [dir] .lg\:bg-cover {
    background-size: cover !important
  }

  [dir] .lg\:bg-contain {
    background-size: contain !important
  }

  [dir] .lg\:border-transparent {
    border-color: transparent !important
  }

  [dir] .lg\:border-black {
    border-color: #22292f !important
  }

  [dir] .lg\:border-white {
    border-color: #ffffff !important
  }

  [dir] .lg\:border-grey {
    border-color: #b8c2cc !important
  }

  [dir] .lg\:border-grey-light {
    border-color: #dae1e7 !important
  }

  [dir] .lg\:hover\:border-transparent:hover {
    border-color: transparent !important
  }

  [dir] .lg\:hover\:border-black:hover {
    border-color: #22292f !important
  }

  [dir] .lg\:hover\:border-white:hover {
    border-color: #ffffff !important
  }

  [dir] .lg\:hover\:border-grey:hover {
    border-color: #b8c2cc !important
  }

  [dir] .lg\:hover\:border-grey-light:hover {
    border-color: #dae1e7 !important
  }

  [dir] .lg\:border-solid {
    border-style: solid !important
  }

  [dir] .lg\:border-dashed {
    border-style: dashed !important
  }

  [dir] .lg\:border-dotted {
    border-style: dotted !important
  }

  [dir] .lg\:border-double {
    border-style: double !important
  }

  [dir] .lg\:border-none {
    border-style: none !important
  }

  [dir] .lg\:border-0 {
    border-width: 0 !important
  }

  [dir] .lg\:border-2 {
    border-width: 2px !important
  }

  [dir] .lg\:border-4 {
    border-width: 4px !important
  }

  [dir] .lg\:border-8 {
    border-width: 8px !important
  }

  [dir] .lg\:border {
    border-width: 1px !important
  }

  [dir] .lg\:border-t-0 {
    border-top-width: 0 !important
  }

  [dir=ltr] .lg\:border-r-0 {
border-right-width: 0 !important
  }

  [dir=rtl] .lg\:border-r-0 {
    border-left-width: 0 !important
  }

  [dir] .lg\:border-b-0 {
    border-bottom-width: 0 !important
  }

  [dir=ltr] .lg\:border-l-0 {
border-left-width: 0 !important
  }

  [dir=rtl] .lg\:border-l-0 {
    border-right-width: 0 !important
  }

  [dir] .lg\:border-t-2 {
    border-top-width: 2px !important
  }

  [dir=ltr] .lg\:border-r-2 {
border-right-width: 2px !important
  }

  [dir=rtl] .lg\:border-r-2 {
    border-left-width: 2px !important
  }

  [dir] .lg\:border-b-2 {
    border-bottom-width: 2px !important
  }

  [dir=ltr] .lg\:border-l-2 {
border-left-width: 2px !important
  }

  [dir=rtl] .lg\:border-l-2 {
    border-right-width: 2px !important
  }

  [dir] .lg\:border-t-4 {
    border-top-width: 4px !important
  }

  [dir=ltr] .lg\:border-r-4 {
border-right-width: 4px !important
  }

  [dir=rtl] .lg\:border-r-4 {
    border-left-width: 4px !important
  }

  [dir] .lg\:border-b-4 {
    border-bottom-width: 4px !important
  }

  [dir=ltr] .lg\:border-l-4 {
border-left-width: 4px !important
  }

  [dir=rtl] .lg\:border-l-4 {
    border-right-width: 4px !important
  }

  [dir] .lg\:border-t-8 {
    border-top-width: 8px !important
  }

  [dir=ltr] .lg\:border-r-8 {
border-right-width: 8px !important
  }

  [dir=rtl] .lg\:border-r-8 {
    border-left-width: 8px !important
  }

  [dir] .lg\:border-b-8 {
    border-bottom-width: 8px !important
  }

  [dir=ltr] .lg\:border-l-8 {
border-left-width: 8px !important
  }

  [dir=rtl] .lg\:border-l-8 {
    border-right-width: 8px !important
  }

  [dir] .lg\:border-t {
    border-top-width: 1px !important
  }

  [dir=ltr] .lg\:border-r {
border-right-width: 1px !important
  }

  [dir=rtl] .lg\:border-r {
    border-left-width: 1px !important
  }

  [dir] .lg\:border-b {
    border-bottom-width: 1px !important
  }

  [dir=ltr] .lg\:border-l {
border-left-width: 1px !important
  }

  [dir=rtl] .lg\:border-l {
    border-right-width: 1px !important
  }

  .lg\:block {
    display: block !important
  }

  .lg\:inline-block {
    display: inline-block !important
  }

  .lg\:inline {
    display: inline !important
  }

  .lg\:flex {
    display: -webkit-box !important;
    display: flex !important
  }

  .lg\:inline-flex {
    display: -webkit-inline-box !important;
    display: inline-flex !important
  }

  .lg\:table {
    display: table !important
  }

  .lg\:table-row {
    display: table-row !important
  }

  .lg\:table-cell {
    display: table-cell !important
  }

  .lg\:hidden {
    display: none !important
  }

  .lg\:flex-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
            flex-direction: row !important
  }

  .lg\:flex-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
            flex-direction: row-reverse !important
  }

  .lg\:flex-col {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
            flex-direction: column !important
  }

  .lg\:flex-col-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
            flex-direction: column-reverse !important
  }

  .lg\:flex-wrap {
    flex-wrap: wrap !important
  }

  .lg\:flex-wrap-reverse {
    flex-wrap: wrap-reverse !important
  }

  .lg\:flex-no-wrap {
    flex-wrap: nowrap !important
  }

  .lg\:items-start {
    -webkit-box-align: start !important;
            align-items: flex-start !important
  }

  .lg\:items-end {
    -webkit-box-align: end !important;
            align-items: flex-end !important
  }

  .lg\:items-center {
    -webkit-box-align: center !important;
            align-items: center !important
  }

  .lg\:items-baseline {
    -webkit-box-align: baseline !important;
            align-items: baseline !important
  }

  .lg\:items-stretch {
    -webkit-box-align: stretch !important;
            align-items: stretch !important
  }

  .lg\:self-auto {
    align-self: auto !important
  }

  .lg\:self-start {
    align-self: flex-start !important
  }

  .lg\:self-end {
    align-self: flex-end !important
  }

  .lg\:self-center {
    align-self: center !important
  }

  .lg\:self-stretch {
    align-self: stretch !important
  }

  .lg\:justify-start {
    -webkit-box-pack: start !important;
            justify-content: flex-start !important
  }

  .lg\:justify-end {
    -webkit-box-pack: end !important;
            justify-content: flex-end !important
  }

  .lg\:justify-center {
    -webkit-box-pack: center !important;
            justify-content: center !important
  }

  .lg\:justify-between {
    -webkit-box-pack: justify !important;
            justify-content: space-between !important
  }

  .lg\:justify-around {
    justify-content: space-around !important
  }

  .lg\:content-center {
    align-content: center !important
  }

  .lg\:content-start {
    align-content: flex-start !important
  }

  .lg\:content-end {
    align-content: flex-end !important
  }

  .lg\:content-between {
    align-content: space-between !important
  }

  .lg\:content-around {
    align-content: space-around !important
  }

  .lg\:flex-1 {
    -webkit-box-flex: 1 !important;
            flex: 1 1 0% !important
  }

  .lg\:flex-auto {
    -webkit-box-flex: 1 !important;
            flex: 1 1 auto !important
  }

  .lg\:flex-initial {
    -webkit-box-flex: 0 !important;
            flex: 0 1 auto !important
  }

  .lg\:flex-none {
    -webkit-box-flex: 0 !important;
            flex: none !important
  }

  .lg\:flex-grow-0 {
    -webkit-box-flex: 0 !important;
            flex-grow: 0 !important
  }

  .lg\:flex-grow {
    -webkit-box-flex: 1 !important;
            flex-grow: 1 !important
  }

  .lg\:flex-shrink-0 {
    flex-shrink: 0 !important
  }

  .lg\:flex-shrink {
    flex-shrink: 1 !important
  }

  .lg\:order-1 {
    -webkit-box-ordinal-group: 2 !important;
            order: 1 !important
  }

  .lg\:order-2 {
    -webkit-box-ordinal-group: 3 !important;
            order: 2 !important
  }

  .lg\:order-3 {
    -webkit-box-ordinal-group: 4 !important;
            order: 3 !important
  }

  .lg\:order-4 {
    -webkit-box-ordinal-group: 5 !important;
            order: 4 !important
  }

  .lg\:order-5 {
    -webkit-box-ordinal-group: 6 !important;
            order: 5 !important
  }

  .lg\:order-6 {
    -webkit-box-ordinal-group: 7 !important;
            order: 6 !important
  }

  .lg\:order-first {
    -webkit-box-ordinal-group: 0 !important;
            order: -1 !important
  }

  .lg\:order-last {
    -webkit-box-ordinal-group: 1000 !important;
            order: 999 !important
  }

  .lg\:order-normal {
    -webkit-box-ordinal-group: 1 !important;
            order: 0 !important
  }

  .lg\:hover\:order-1:hover {
    -webkit-box-ordinal-group: 2 !important;
            order: 1 !important
  }

  .lg\:hover\:order-2:hover {
    -webkit-box-ordinal-group: 3 !important;
            order: 2 !important
  }

  .lg\:hover\:order-3:hover {
    -webkit-box-ordinal-group: 4 !important;
            order: 3 !important
  }

  .lg\:hover\:order-4:hover {
    -webkit-box-ordinal-group: 5 !important;
            order: 4 !important
  }

  .lg\:hover\:order-5:hover {
    -webkit-box-ordinal-group: 6 !important;
            order: 5 !important
  }

  .lg\:hover\:order-6:hover {
    -webkit-box-ordinal-group: 7 !important;
            order: 6 !important
  }

  .lg\:hover\:order-first:hover {
    -webkit-box-ordinal-group: 0 !important;
            order: -1 !important
  }

  .lg\:hover\:order-last:hover {
    -webkit-box-ordinal-group: 1000 !important;
            order: 999 !important
  }

  .lg\:hover\:order-normal:hover {
    -webkit-box-ordinal-group: 1 !important;
            order: 0 !important
  }

  .lg\:focus\:order-1:focus {
    -webkit-box-ordinal-group: 2 !important;
            order: 1 !important
  }

  .lg\:focus\:order-2:focus {
    -webkit-box-ordinal-group: 3 !important;
            order: 2 !important
  }

  .lg\:focus\:order-3:focus {
    -webkit-box-ordinal-group: 4 !important;
            order: 3 !important
  }

  .lg\:focus\:order-4:focus {
    -webkit-box-ordinal-group: 5 !important;
            order: 4 !important
  }

  .lg\:focus\:order-5:focus {
    -webkit-box-ordinal-group: 6 !important;
            order: 5 !important
  }

  .lg\:focus\:order-6:focus {
    -webkit-box-ordinal-group: 7 !important;
            order: 6 !important
  }

  .lg\:focus\:order-first:focus {
    -webkit-box-ordinal-group: 0 !important;
            order: -1 !important
  }

  .lg\:focus\:order-last:focus {
    -webkit-box-ordinal-group: 1000 !important;
            order: 999 !important
  }

  .lg\:focus\:order-normal:focus {
    -webkit-box-ordinal-group: 1 !important;
            order: 0 !important
  }

  [dir=ltr] .lg\:float-right {
float: right !important
  }

  [dir=rtl] .lg\:float-right {
    float: left !important
  }

  [dir=ltr] .lg\:float-left {
float: left !important
  }

  [dir=rtl] .lg\:float-left {
    float: right !important
  }

  [dir] .lg\:float-none {
    float: none !important
  }

  .lg\:clearfix:after {
    content: "" !important;
    display: table !important
  }

  [dir] .lg\:clearfix:after {
    clear: both !important
  }

  .lg\:font-light {
    font-weight: 300 !important
  }

  .lg\:font-normal {
    font-weight: 400 !important
  }

  .lg\:font-medium {
    font-weight: 500 !important
  }

  .lg\:font-semibold {
    font-weight: 600 !important
  }

  .lg\:font-bold {
    font-weight: 700 !important
  }

  .lg\:font-extrabold {
    font-weight: 800 !important
  }

  .lg\:font-black {
    font-weight: 900 !important
  }

  .lg\:hover\:font-light:hover {
    font-weight: 300 !important
  }

  .lg\:hover\:font-normal:hover {
    font-weight: 400 !important
  }

  .lg\:hover\:font-medium:hover {
    font-weight: 500 !important
  }

  .lg\:hover\:font-semibold:hover {
    font-weight: 600 !important
  }

  .lg\:hover\:font-bold:hover {
    font-weight: 700 !important
  }

  .lg\:hover\:font-extrabold:hover {
    font-weight: 800 !important
  }

  .lg\:hover\:font-black:hover {
    font-weight: 900 !important
  }

  .lg\:h-1 {
    height: 0.25rem !important
  }

  .lg\:h-2 {
    height: 0.5rem !important
  }

  .lg\:h-3 {
    height: 0.75rem !important
  }

  .lg\:h-4 {
    height: 1rem !important
  }

  .lg\:h-5 {
    height: 1.25rem !important
  }

  .lg\:h-6 {
    height: 1.5rem !important
  }

  .lg\:h-8 {
    height: 2rem !important
  }

  .lg\:h-10 {
    height: 2.5rem !important
  }

  .lg\:h-12 {
    height: 3rem !important
  }

  .lg\:h-16 {
    height: 4rem !important
  }

  .lg\:h-24 {
    height: 6rem !important
  }

  .lg\:h-32 {
    height: 8rem !important
  }

  .lg\:h-48 {
    height: 12rem !important
  }

  .lg\:h-64 {
    height: 16rem !important
  }

  .lg\:h-auto {
    height: auto !important
  }

  .lg\:h-px {
    height: 1px !important
  }

  .lg\:h-full {
    height: 100% !important
  }

  .lg\:h-screen {
    height: 100vh !important
  }

  .lg\:leading-none {
    line-height: 1 !important
  }

  .lg\:leading-tight {
    line-height: 1.25 !important
  }

  .lg\:leading-normal {
    line-height: 1.5 !important
  }

  .lg\:leading-loose {
    line-height: 2 !important
  }

  .lg\:list-inside {
    list-style-position: inside !important
  }

  .lg\:list-outside {
    list-style-position: outside !important
  }

  [dir] .lg\:m-0 {
    margin: 0 !important
  }

  [dir] .lg\:m-1 {
    margin: 0.25rem !important
  }

  [dir] .lg\:m-2 {
    margin: 0.5rem !important
  }

  [dir] .lg\:m-3 {
    margin: 0.75rem !important
  }

  [dir] .lg\:m-4 {
    margin: 1rem !important
  }

  [dir] .lg\:m-5 {
    margin: 1.25rem !important
  }

  [dir] .lg\:m-6 {
    margin: 1.5rem !important
  }

  [dir] .lg\:m-8 {
    margin: 2rem !important
  }

  [dir] .lg\:m-10 {
    margin: 2.5rem !important
  }

  [dir] .lg\:m-12 {
    margin: 3rem !important
  }

  [dir] .lg\:m-16 {
    margin: 4rem !important
  }

  [dir] .lg\:m-20 {
    margin: 5rem !important
  }

  [dir] .lg\:m-24 {
    margin: 6rem !important
  }

  [dir] .lg\:m-32 {
    margin: 8rem !important
  }

  [dir] .lg\:m-auto {
    margin: auto !important
  }

  [dir] .lg\:m-px {
    margin: 1px !important
  }

  [dir] .lg\:m-base {
    margin: 2.2rem !important
  }

  [dir] .lg\:-m-px {
    margin: -1px !important
  }

  [dir] .lg\:-m-1 {
    margin: -0.25rem !important
  }

  [dir] .lg\:-m-2 {
    margin: -0.5rem !important
  }

  [dir] .lg\:-m-3 {
    margin: -0.75rem !important
  }

  [dir] .lg\:-m-4 {
    margin: -1rem !important
  }

  [dir] .lg\:my-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important
  }

  [dir=ltr] .lg\:mx-0 {
margin-left: 0 !important;
margin-right: 0 !important
  }

  [dir=rtl] .lg\:mx-0 {
    margin-right: 0 !important;
    margin-left: 0 !important
  }

  [dir] .lg\:my-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important
  }

  [dir=ltr] .lg\:mx-1 {
margin-left: 0.25rem !important;
margin-right: 0.25rem !important
  }

  [dir=rtl] .lg\:mx-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important
  }

  [dir] .lg\:my-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important
  }

  [dir=ltr] .lg\:mx-2 {
margin-left: 0.5rem !important;
margin-right: 0.5rem !important
  }

  [dir=rtl] .lg\:mx-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important
  }

  [dir] .lg\:my-3 {
    margin-top: 0.75rem !important;
    margin-bottom: 0.75rem !important
  }

  [dir=ltr] .lg\:mx-3 {
margin-left: 0.75rem !important;
margin-right: 0.75rem !important
  }

  [dir=rtl] .lg\:mx-3 {
    margin-right: 0.75rem !important;
    margin-left: 0.75rem !important
  }

  [dir] .lg\:my-4 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important
  }

  [dir=ltr] .lg\:mx-4 {
margin-left: 1rem !important;
margin-right: 1rem !important
  }

  [dir=rtl] .lg\:mx-4 {
    margin-right: 1rem !important;
    margin-left: 1rem !important
  }

  [dir] .lg\:my-5 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important
  }

  [dir=ltr] .lg\:mx-5 {
margin-left: 1.25rem !important;
margin-right: 1.25rem !important
  }

  [dir=rtl] .lg\:mx-5 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important
  }

  [dir] .lg\:my-6 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important
  }

  [dir=ltr] .lg\:mx-6 {
margin-left: 1.5rem !important;
margin-right: 1.5rem !important
  }

  [dir=rtl] .lg\:mx-6 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important
  }

  [dir] .lg\:my-8 {
    margin-top: 2rem !important;
    margin-bottom: 2rem !important
  }

  [dir=ltr] .lg\:mx-8 {
margin-left: 2rem !important;
margin-right: 2rem !important
  }

  [dir=rtl] .lg\:mx-8 {
    margin-right: 2rem !important;
    margin-left: 2rem !important
  }

  [dir] .lg\:my-10 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important
  }

  [dir=ltr] .lg\:mx-10 {
margin-left: 2.5rem !important;
margin-right: 2.5rem !important
  }

  [dir=rtl] .lg\:mx-10 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important
  }

  [dir] .lg\:my-12 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important
  }

  [dir=ltr] .lg\:mx-12 {
margin-left: 3rem !important;
margin-right: 3rem !important
  }

  [dir=rtl] .lg\:mx-12 {
    margin-right: 3rem !important;
    margin-left: 3rem !important
  }

  [dir] .lg\:my-16 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important
  }

  [dir=ltr] .lg\:mx-16 {
margin-left: 4rem !important;
margin-right: 4rem !important
  }

  [dir=rtl] .lg\:mx-16 {
    margin-right: 4rem !important;
    margin-left: 4rem !important
  }

  [dir] .lg\:my-20 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important
  }

  [dir=ltr] .lg\:mx-20 {
margin-left: 5rem !important;
margin-right: 5rem !important
  }

  [dir=rtl] .lg\:mx-20 {
    margin-right: 5rem !important;
    margin-left: 5rem !important
  }

  [dir] .lg\:my-24 {
    margin-top: 6rem !important;
    margin-bottom: 6rem !important
  }

  [dir=ltr] .lg\:mx-24 {
margin-left: 6rem !important;
margin-right: 6rem !important
  }

  [dir=rtl] .lg\:mx-24 {
    margin-right: 6rem !important;
    margin-left: 6rem !important
  }

  [dir] .lg\:my-32 {
    margin-top: 8rem !important;
    margin-bottom: 8rem !important
  }

  [dir=ltr] .lg\:mx-32 {
margin-left: 8rem !important;
margin-right: 8rem !important
  }

  [dir=rtl] .lg\:mx-32 {
    margin-right: 8rem !important;
    margin-left: 8rem !important
  }

  [dir] .lg\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important
  }

  [dir=ltr] .lg\:mx-auto {
margin-left: auto !important;
margin-right: auto !important
  }

  [dir=rtl] .lg\:mx-auto {
    margin-right: auto !important;
    margin-left: auto !important
  }

  [dir] .lg\:my-px {
    margin-top: 1px !important;
    margin-bottom: 1px !important
  }

  [dir=ltr] .lg\:mx-px {
margin-left: 1px !important;
margin-right: 1px !important
  }

  [dir=rtl] .lg\:mx-px {
    margin-right: 1px !important;
    margin-left: 1px !important
  }

  [dir] .lg\:my-base {
    margin-top: 2.2rem !important;
    margin-bottom: 2.2rem !important
  }

  [dir=ltr] .lg\:mx-base {
margin-left: 2.2rem !important;
margin-right: 2.2rem !important
  }

  [dir=rtl] .lg\:mx-base {
    margin-right: 2.2rem !important;
    margin-left: 2.2rem !important
  }

  [dir] .lg\:-my-px {
    margin-top: -1px !important;
    margin-bottom: -1px !important
  }

  [dir=ltr] .lg\:-mx-px {
margin-left: -1px !important;
margin-right: -1px !important
  }

  [dir=rtl] .lg\:-mx-px {
    margin-right: -1px !important;
    margin-left: -1px !important
  }

  [dir] .lg\:-my-1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important
  }

  [dir=ltr] .lg\:-mx-1 {
margin-left: -0.25rem !important;
margin-right: -0.25rem !important
  }

  [dir=rtl] .lg\:-mx-1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important
  }

  [dir] .lg\:-my-2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important
  }

  [dir=ltr] .lg\:-mx-2 {
margin-left: -0.5rem !important;
margin-right: -0.5rem !important
  }

  [dir=rtl] .lg\:-mx-2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important
  }

  [dir] .lg\:-my-3 {
    margin-top: -0.75rem !important;
    margin-bottom: -0.75rem !important
  }

  [dir=ltr] .lg\:-mx-3 {
margin-left: -0.75rem !important;
margin-right: -0.75rem !important
  }

  [dir=rtl] .lg\:-mx-3 {
    margin-right: -0.75rem !important;
    margin-left: -0.75rem !important
  }

  [dir] .lg\:-my-4 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important
  }

  [dir=ltr] .lg\:-mx-4 {
margin-left: -1rem !important;
margin-right: -1rem !important
  }

  [dir=rtl] .lg\:-mx-4 {
    margin-right: -1rem !important;
    margin-left: -1rem !important
  }

  [dir] .lg\:mt-0 {
    margin-top: 0 !important
  }

  [dir=ltr] .lg\:mr-0 {
margin-right: 0 !important
  }

  [dir=rtl] .lg\:mr-0 {
    margin-left: 0 !important
  }

  [dir] .lg\:mb-0 {
    margin-bottom: 0 !important
  }

  [dir=ltr] .lg\:ml-0 {
margin-left: 0 !important
  }

  [dir=rtl] .lg\:ml-0 {
    margin-right: 0 !important
  }

  [dir] .lg\:mt-1 {
    margin-top: 0.25rem !important
  }

  [dir=ltr] .lg\:mr-1 {
margin-right: 0.25rem !important
  }

  [dir=rtl] .lg\:mr-1 {
    margin-left: 0.25rem !important
  }

  [dir] .lg\:mb-1 {
    margin-bottom: 0.25rem !important
  }

  [dir=ltr] .lg\:ml-1 {
margin-left: 0.25rem !important
  }

  [dir=rtl] .lg\:ml-1 {
    margin-right: 0.25rem !important
  }

  [dir] .lg\:mt-2 {
    margin-top: 0.5rem !important
  }

  [dir=ltr] .lg\:mr-2 {
margin-right: 0.5rem !important
  }

  [dir=rtl] .lg\:mr-2 {
    margin-left: 0.5rem !important
  }

  [dir] .lg\:mb-2 {
    margin-bottom: 0.5rem !important
  }

  [dir=ltr] .lg\:ml-2 {
margin-left: 0.5rem !important
  }

  [dir=rtl] .lg\:ml-2 {
    margin-right: 0.5rem !important
  }

  [dir] .lg\:mt-3 {
    margin-top: 0.75rem !important
  }

  [dir=ltr] .lg\:mr-3 {
margin-right: 0.75rem !important
  }

  [dir=rtl] .lg\:mr-3 {
    margin-left: 0.75rem !important
  }

  [dir] .lg\:mb-3 {
    margin-bottom: 0.75rem !important
  }

  [dir=ltr] .lg\:ml-3 {
margin-left: 0.75rem !important
  }

  [dir=rtl] .lg\:ml-3 {
    margin-right: 0.75rem !important
  }

  [dir] .lg\:mt-4 {
    margin-top: 1rem !important
  }

  [dir=ltr] .lg\:mr-4 {
margin-right: 1rem !important
  }

  [dir=rtl] .lg\:mr-4 {
    margin-left: 1rem !important
  }

  [dir] .lg\:mb-4 {
    margin-bottom: 1rem !important
  }

  [dir=ltr] .lg\:ml-4 {
margin-left: 1rem !important
  }

  [dir=rtl] .lg\:ml-4 {
    margin-right: 1rem !important
  }

  [dir] .lg\:mt-5 {
    margin-top: 1.25rem !important
  }

  [dir=ltr] .lg\:mr-5 {
margin-right: 1.25rem !important
  }

  [dir=rtl] .lg\:mr-5 {
    margin-left: 1.25rem !important
  }

  [dir] .lg\:mb-5 {
    margin-bottom: 1.25rem !important
  }

  [dir=ltr] .lg\:ml-5 {
margin-left: 1.25rem !important
  }

  [dir=rtl] .lg\:ml-5 {
    margin-right: 1.25rem !important
  }

  [dir] .lg\:mt-6 {
    margin-top: 1.5rem !important
  }

  [dir=ltr] .lg\:mr-6 {
margin-right: 1.5rem !important
  }

  [dir=rtl] .lg\:mr-6 {
    margin-left: 1.5rem !important
  }

  [dir] .lg\:mb-6 {
    margin-bottom: 1.5rem !important
  }

  [dir=ltr] .lg\:ml-6 {
margin-left: 1.5rem !important
  }

  [dir=rtl] .lg\:ml-6 {
    margin-right: 1.5rem !important
  }

  [dir] .lg\:mt-8 {
    margin-top: 2rem !important
  }

  [dir=ltr] .lg\:mr-8 {
margin-right: 2rem !important
  }

  [dir=rtl] .lg\:mr-8 {
    margin-left: 2rem !important
  }

  [dir] .lg\:mb-8 {
    margin-bottom: 2rem !important
  }

  [dir=ltr] .lg\:ml-8 {
margin-left: 2rem !important
  }

  [dir=rtl] .lg\:ml-8 {
    margin-right: 2rem !important
  }

  [dir] .lg\:mt-10 {
    margin-top: 2.5rem !important
  }

  [dir=ltr] .lg\:mr-10 {
margin-right: 2.5rem !important
  }

  [dir=rtl] .lg\:mr-10 {
    margin-left: 2.5rem !important
  }

  [dir] .lg\:mb-10 {
    margin-bottom: 2.5rem !important
  }

  [dir=ltr] .lg\:ml-10 {
margin-left: 2.5rem !important
  }

  [dir=rtl] .lg\:ml-10 {
    margin-right: 2.5rem !important
  }

  [dir] .lg\:mt-12 {
    margin-top: 3rem !important
  }

  [dir=ltr] .lg\:mr-12 {
margin-right: 3rem !important
  }

  [dir=rtl] .lg\:mr-12 {
    margin-left: 3rem !important
  }

  [dir] .lg\:mb-12 {
    margin-bottom: 3rem !important
  }

  [dir=ltr] .lg\:ml-12 {
margin-left: 3rem !important
  }

  [dir=rtl] .lg\:ml-12 {
    margin-right: 3rem !important
  }

  [dir] .lg\:mt-16 {
    margin-top: 4rem !important
  }

  [dir=ltr] .lg\:mr-16 {
margin-right: 4rem !important
  }

  [dir=rtl] .lg\:mr-16 {
    margin-left: 4rem !important
  }

  [dir] .lg\:mb-16 {
    margin-bottom: 4rem !important
  }

  [dir=ltr] .lg\:ml-16 {
margin-left: 4rem !important
  }

  [dir=rtl] .lg\:ml-16 {
    margin-right: 4rem !important
  }

  [dir] .lg\:mt-20 {
    margin-top: 5rem !important
  }

  [dir=ltr] .lg\:mr-20 {
margin-right: 5rem !important
  }

  [dir=rtl] .lg\:mr-20 {
    margin-left: 5rem !important
  }

  [dir] .lg\:mb-20 {
    margin-bottom: 5rem !important
  }

  [dir=ltr] .lg\:ml-20 {
margin-left: 5rem !important
  }

  [dir=rtl] .lg\:ml-20 {
    margin-right: 5rem !important
  }

  [dir] .lg\:mt-24 {
    margin-top: 6rem !important
  }

  [dir=ltr] .lg\:mr-24 {
margin-right: 6rem !important
  }

  [dir=rtl] .lg\:mr-24 {
    margin-left: 6rem !important
  }

  [dir] .lg\:mb-24 {
    margin-bottom: 6rem !important
  }

  [dir=ltr] .lg\:ml-24 {
margin-left: 6rem !important
  }

  [dir=rtl] .lg\:ml-24 {
    margin-right: 6rem !important
  }

  [dir] .lg\:mt-32 {
    margin-top: 8rem !important
  }

  [dir=ltr] .lg\:mr-32 {
margin-right: 8rem !important
  }

  [dir=rtl] .lg\:mr-32 {
    margin-left: 8rem !important
  }

  [dir] .lg\:mb-32 {
    margin-bottom: 8rem !important
  }

  [dir=ltr] .lg\:ml-32 {
margin-left: 8rem !important
  }

  [dir=rtl] .lg\:ml-32 {
    margin-right: 8rem !important
  }

  [dir] .lg\:mt-auto {
    margin-top: auto !important
  }

  [dir=ltr] .lg\:mr-auto {
margin-right: auto !important
  }

  [dir=rtl] .lg\:mr-auto {
    margin-left: auto !important
  }

  [dir] .lg\:mb-auto {
    margin-bottom: auto !important
  }

  [dir=ltr] .lg\:ml-auto {
margin-left: auto !important
  }

  [dir=rtl] .lg\:ml-auto {
    margin-right: auto !important
  }

  [dir] .lg\:mt-px {
    margin-top: 1px !important
  }

  [dir=ltr] .lg\:mr-px {
margin-right: 1px !important
  }

  [dir=rtl] .lg\:mr-px {
    margin-left: 1px !important
  }

  [dir] .lg\:mb-px {
    margin-bottom: 1px !important
  }

  [dir=ltr] .lg\:ml-px {
margin-left: 1px !important
  }

  [dir=rtl] .lg\:ml-px {
    margin-right: 1px !important
  }

  [dir] .lg\:mt-base {
    margin-top: 2.2rem !important
  }

  [dir=ltr] .lg\:mr-base {
margin-right: 2.2rem !important
  }

  [dir=rtl] .lg\:mr-base {
    margin-left: 2.2rem !important
  }

  [dir] .lg\:mb-base {
    margin-bottom: 2.2rem !important
  }

  [dir=ltr] .lg\:ml-base {
margin-left: 2.2rem !important
  }

  [dir=rtl] .lg\:ml-base {
    margin-right: 2.2rem !important
  }

  [dir] .lg\:-mt-px {
    margin-top: -1px !important
  }

  [dir=ltr] .lg\:-mr-px {
margin-right: -1px !important
  }

  [dir=rtl] .lg\:-mr-px {
    margin-left: -1px !important
  }

  [dir] .lg\:-mb-px {
    margin-bottom: -1px !important
  }

  [dir=ltr] .lg\:-ml-px {
margin-left: -1px !important
  }

  [dir=rtl] .lg\:-ml-px {
    margin-right: -1px !important
  }

  [dir] .lg\:-mt-1 {
    margin-top: -0.25rem !important
  }

  [dir=ltr] .lg\:-mr-1 {
margin-right: -0.25rem !important
  }

  [dir=rtl] .lg\:-mr-1 {
    margin-left: -0.25rem !important
  }

  [dir] .lg\:-mb-1 {
    margin-bottom: -0.25rem !important
  }

  [dir=ltr] .lg\:-ml-1 {
margin-left: -0.25rem !important
  }

  [dir=rtl] .lg\:-ml-1 {
    margin-right: -0.25rem !important
  }

  [dir] .lg\:-mt-2 {
    margin-top: -0.5rem !important
  }

  [dir=ltr] .lg\:-mr-2 {
margin-right: -0.5rem !important
  }

  [dir=rtl] .lg\:-mr-2 {
    margin-left: -0.5rem !important
  }

  [dir] .lg\:-mb-2 {
    margin-bottom: -0.5rem !important
  }

  [dir=ltr] .lg\:-ml-2 {
margin-left: -0.5rem !important
  }

  [dir=rtl] .lg\:-ml-2 {
    margin-right: -0.5rem !important
  }

  [dir] .lg\:-mt-3 {
    margin-top: -0.75rem !important
  }

  [dir=ltr] .lg\:-mr-3 {
margin-right: -0.75rem !important
  }

  [dir=rtl] .lg\:-mr-3 {
    margin-left: -0.75rem !important
  }

  [dir] .lg\:-mb-3 {
    margin-bottom: -0.75rem !important
  }

  [dir=ltr] .lg\:-ml-3 {
margin-left: -0.75rem !important
  }

  [dir=rtl] .lg\:-ml-3 {
    margin-right: -0.75rem !important
  }

  [dir] .lg\:-mt-4 {
    margin-top: -1rem !important
  }

  [dir=ltr] .lg\:-mr-4 {
margin-right: -1rem !important
  }

  [dir=rtl] .lg\:-mr-4 {
    margin-left: -1rem !important
  }

  [dir] .lg\:-mb-4 {
    margin-bottom: -1rem !important
  }

  [dir=ltr] .lg\:-ml-4 {
margin-left: -1rem !important
  }

  [dir=rtl] .lg\:-ml-4 {
    margin-right: -1rem !important
  }

  .lg\:max-h-full {
    max-height: 100% !important
  }

  .lg\:max-h-screen {
    max-height: 100vh !important
  }

  .lg\:max-w-xs {
    max-width: 20rem !important
  }

  .lg\:max-w-sm {
    max-width: 30rem !important
  }

  .lg\:max-w-md {
    max-width: 40rem !important
  }

  .lg\:max-w-lg {
    max-width: 50rem !important
  }

  .lg\:max-w-xl {
    max-width: 60rem !important
  }

  .lg\:max-w-2xl {
    max-width: 70rem !important
  }

  .lg\:max-w-3xl {
    max-width: 80rem !important
  }

  .lg\:max-w-4xl {
    max-width: 90rem !important
  }

  .lg\:max-w-5xl {
    max-width: 100rem !important
  }

  .lg\:max-w-full {
    max-width: 100% !important
  }

  .lg\:min-h-0 {
    min-height: 0 !important
  }

  .lg\:min-h-full {
    min-height: 100% !important
  }

  .lg\:min-h-screen {
    min-height: 100vh !important
  }

  .lg\:min-w-0 {
    min-width: 0 !important
  }

  .lg\:min-w-full {
    min-width: 100% !important
  }

  .lg\:object-contain {
    -o-object-fit: contain !important;
       object-fit: contain !important
  }

  .lg\:object-cover {
    -o-object-fit: cover !important;
       object-fit: cover !important
  }

  .lg\:object-fill {
    -o-object-fit: fill !important;
       object-fit: fill !important
  }

  .lg\:object-none {
    -o-object-fit: none !important;
       object-fit: none !important
  }

  .lg\:object-scale-down {
    -o-object-fit: scale-down !important;
       object-fit: scale-down !important
  }

  .lg\:object-bottom {
    -o-object-position: bottom !important;
       object-position: bottom !important
  }

  .lg\:object-center {
    -o-object-position: center !important;
       object-position: center !important
  }

  .lg\:object-left {
    -o-object-position: left !important;
       object-position: left !important
  }

  .lg\:object-left-bottom {
    -o-object-position: left bottom !important;
       object-position: left bottom !important
  }

  .lg\:object-left-top {
    -o-object-position: left top !important;
       object-position: left top !important
  }

  .lg\:object-right {
    -o-object-position: right !important;
       object-position: right !important
  }

  .lg\:object-right-bottom {
    -o-object-position: right bottom !important;
       object-position: right bottom !important
  }

  .lg\:object-right-top {
    -o-object-position: right top !important;
       object-position: right top !important
  }

  .lg\:object-top {
    -o-object-position: top !important;
       object-position: top !important
  }

  .lg\:opacity-0 {
    opacity: 0 !important
  }

  .lg\:opacity-25 {
    opacity: 0.25 !important
  }

  .lg\:opacity-50 {
    opacity: 0.5 !important
  }

  .lg\:opacity-75 {
    opacity: 0.75 !important
  }

  .lg\:opacity-100 {
    opacity: 1 !important
  }

  .lg\:overflow-auto {
    overflow: auto !important
  }

  .lg\:overflow-hidden {
    overflow: hidden !important
  }

  .lg\:overflow-visible {
    overflow: visible !important
  }

  .lg\:overflow-scroll {
    overflow: scroll !important
  }

  .lg\:overflow-x-auto {
    overflow-x: auto !important
  }

  .lg\:overflow-y-auto {
    overflow-y: auto !important
  }

  .lg\:overflow-x-hidden {
    overflow-x: hidden !important
  }

  .lg\:overflow-y-hidden {
    overflow-y: hidden !important
  }

  .lg\:overflow-x-visible {
    overflow-x: visible !important
  }

  .lg\:overflow-y-visible {
    overflow-y: visible !important
  }

  .lg\:overflow-x-scroll {
    overflow-x: scroll !important
  }

  .lg\:overflow-y-scroll {
    overflow-y: scroll !important
  }

  .lg\:scrolling-touch {
    -webkit-overflow-scrolling: touch !important
  }

  .lg\:scrolling-auto {
    -webkit-overflow-scrolling: auto !important
  }

  [dir] .lg\:p-0 {
    padding: 0 !important
  }

  [dir] .lg\:p-1 {
    padding: 0.25rem !important
  }

  [dir] .lg\:p-2 {
    padding: 0.5rem !important
  }

  [dir] .lg\:p-3 {
    padding: 0.75rem !important
  }

  [dir] .lg\:p-4 {
    padding: 1rem !important
  }

  [dir] .lg\:p-5 {
    padding: 1.25rem !important
  }

  [dir] .lg\:p-6 {
    padding: 1.5rem !important
  }

  [dir] .lg\:p-8 {
    padding: 2rem !important
  }

  [dir] .lg\:p-10 {
    padding: 2.5rem !important
  }

  [dir] .lg\:p-12 {
    padding: 3rem !important
  }

  [dir] .lg\:p-16 {
    padding: 4rem !important
  }

  [dir] .lg\:p-20 {
    padding: 5rem !important
  }

  [dir] .lg\:p-24 {
    padding: 6rem !important
  }

  [dir] .lg\:p-32 {
    padding: 8rem !important
  }

  [dir] .lg\:p-px {
    padding: 1px !important
  }

  [dir] .lg\:p-base {
    padding: 2.2rem !important
  }

  [dir] .lg\:py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important
  }

  [dir=ltr] .lg\:px-0 {
padding-left: 0 !important;
padding-right: 0 !important
  }

  [dir=rtl] .lg\:px-0 {
    padding-right: 0 !important;
    padding-left: 0 !important
  }

  [dir] .lg\:py-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important
  }

  [dir=ltr] .lg\:px-1 {
padding-left: 0.25rem !important;
padding-right: 0.25rem !important
  }

  [dir=rtl] .lg\:px-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important
  }

  [dir] .lg\:py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important
  }

  [dir=ltr] .lg\:px-2 {
padding-left: 0.5rem !important;
padding-right: 0.5rem !important
  }

  [dir=rtl] .lg\:px-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important
  }

  [dir] .lg\:py-3 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important
  }

  [dir=ltr] .lg\:px-3 {
padding-left: 0.75rem !important;
padding-right: 0.75rem !important
  }

  [dir=rtl] .lg\:px-3 {
    padding-right: 0.75rem !important;
    padding-left: 0.75rem !important
  }

  [dir] .lg\:py-4 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important
  }

  [dir=ltr] .lg\:px-4 {
padding-left: 1rem !important;
padding-right: 1rem !important
  }

  [dir=rtl] .lg\:px-4 {
    padding-right: 1rem !important;
    padding-left: 1rem !important
  }

  [dir] .lg\:py-5 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important
  }

  [dir=ltr] .lg\:px-5 {
padding-left: 1.25rem !important;
padding-right: 1.25rem !important
  }

  [dir=rtl] .lg\:px-5 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important
  }

  [dir] .lg\:py-6 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important
  }

  [dir=ltr] .lg\:px-6 {
padding-left: 1.5rem !important;
padding-right: 1.5rem !important
  }

  [dir=rtl] .lg\:px-6 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important
  }

  [dir] .lg\:py-8 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important
  }

  [dir=ltr] .lg\:px-8 {
padding-left: 2rem !important;
padding-right: 2rem !important
  }

  [dir=rtl] .lg\:px-8 {
    padding-right: 2rem !important;
    padding-left: 2rem !important
  }

  [dir] .lg\:py-10 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important
  }

  [dir=ltr] .lg\:px-10 {
padding-left: 2.5rem !important;
padding-right: 2.5rem !important
  }

  [dir=rtl] .lg\:px-10 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important
  }

  [dir] .lg\:py-12 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important
  }

  [dir=ltr] .lg\:px-12 {
padding-left: 3rem !important;
padding-right: 3rem !important
  }

  [dir=rtl] .lg\:px-12 {
    padding-right: 3rem !important;
    padding-left: 3rem !important
  }

  [dir] .lg\:py-16 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important
  }

  [dir=ltr] .lg\:px-16 {
padding-left: 4rem !important;
padding-right: 4rem !important
  }

  [dir=rtl] .lg\:px-16 {
    padding-right: 4rem !important;
    padding-left: 4rem !important
  }

  [dir] .lg\:py-20 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important
  }

  [dir=ltr] .lg\:px-20 {
padding-left: 5rem !important;
padding-right: 5rem !important
  }

  [dir=rtl] .lg\:px-20 {
    padding-right: 5rem !important;
    padding-left: 5rem !important
  }

  [dir] .lg\:py-24 {
    padding-top: 6rem !important;
    padding-bottom: 6rem !important
  }

  [dir=ltr] .lg\:px-24 {
padding-left: 6rem !important;
padding-right: 6rem !important
  }

  [dir=rtl] .lg\:px-24 {
    padding-right: 6rem !important;
    padding-left: 6rem !important
  }

  [dir] .lg\:py-32 {
    padding-top: 8rem !important;
    padding-bottom: 8rem !important
  }

  [dir=ltr] .lg\:px-32 {
padding-left: 8rem !important;
padding-right: 8rem !important
  }

  [dir=rtl] .lg\:px-32 {
    padding-right: 8rem !important;
    padding-left: 8rem !important
  }

  [dir] .lg\:py-px {
    padding-top: 1px !important;
    padding-bottom: 1px !important
  }

  [dir=ltr] .lg\:px-px {
padding-left: 1px !important;
padding-right: 1px !important
  }

  [dir=rtl] .lg\:px-px {
    padding-right: 1px !important;
    padding-left: 1px !important
  }

  [dir] .lg\:py-base {
    padding-top: 2.2rem !important;
    padding-bottom: 2.2rem !important
  }

  [dir=ltr] .lg\:px-base {
padding-left: 2.2rem !important;
padding-right: 2.2rem !important
  }

  [dir=rtl] .lg\:px-base {
    padding-right: 2.2rem !important;
    padding-left: 2.2rem !important
  }

  [dir] .lg\:pt-0 {
    padding-top: 0 !important
  }

  [dir=ltr] .lg\:pr-0 {
padding-right: 0 !important
  }

  [dir=rtl] .lg\:pr-0 {
    padding-left: 0 !important
  }

  [dir] .lg\:pb-0 {
    padding-bottom: 0 !important
  }

  [dir=ltr] .lg\:pl-0 {
padding-left: 0 !important
  }

  [dir=rtl] .lg\:pl-0 {
    padding-right: 0 !important
  }

  [dir] .lg\:pt-1 {
    padding-top: 0.25rem !important
  }

  [dir=ltr] .lg\:pr-1 {
padding-right: 0.25rem !important
  }

  [dir=rtl] .lg\:pr-1 {
    padding-left: 0.25rem !important
  }

  [dir] .lg\:pb-1 {
    padding-bottom: 0.25rem !important
  }

  [dir=ltr] .lg\:pl-1 {
padding-left: 0.25rem !important
  }

  [dir=rtl] .lg\:pl-1 {
    padding-right: 0.25rem !important
  }

  [dir] .lg\:pt-2 {
    padding-top: 0.5rem !important
  }

  [dir=ltr] .lg\:pr-2 {
padding-right: 0.5rem !important
  }

  [dir=rtl] .lg\:pr-2 {
    padding-left: 0.5rem !important
  }

  [dir] .lg\:pb-2 {
    padding-bottom: 0.5rem !important
  }

  [dir=ltr] .lg\:pl-2 {
padding-left: 0.5rem !important
  }

  [dir=rtl] .lg\:pl-2 {
    padding-right: 0.5rem !important
  }

  [dir] .lg\:pt-3 {
    padding-top: 0.75rem !important
  }

  [dir=ltr] .lg\:pr-3 {
padding-right: 0.75rem !important
  }

  [dir=rtl] .lg\:pr-3 {
    padding-left: 0.75rem !important
  }

  [dir] .lg\:pb-3 {
    padding-bottom: 0.75rem !important
  }

  [dir=ltr] .lg\:pl-3 {
padding-left: 0.75rem !important
  }

  [dir=rtl] .lg\:pl-3 {
    padding-right: 0.75rem !important
  }

  [dir] .lg\:pt-4 {
    padding-top: 1rem !important
  }

  [dir=ltr] .lg\:pr-4 {
padding-right: 1rem !important
  }

  [dir=rtl] .lg\:pr-4 {
    padding-left: 1rem !important
  }

  [dir] .lg\:pb-4 {
    padding-bottom: 1rem !important
  }

  [dir=ltr] .lg\:pl-4 {
padding-left: 1rem !important
  }

  [dir=rtl] .lg\:pl-4 {
    padding-right: 1rem !important
  }

  [dir] .lg\:pt-5 {
    padding-top: 1.25rem !important
  }

  [dir=ltr] .lg\:pr-5 {
padding-right: 1.25rem !important
  }

  [dir=rtl] .lg\:pr-5 {
    padding-left: 1.25rem !important
  }

  [dir] .lg\:pb-5 {
    padding-bottom: 1.25rem !important
  }

  [dir=ltr] .lg\:pl-5 {
padding-left: 1.25rem !important
  }

  [dir=rtl] .lg\:pl-5 {
    padding-right: 1.25rem !important
  }

  [dir] .lg\:pt-6 {
    padding-top: 1.5rem !important
  }

  [dir=ltr] .lg\:pr-6 {
padding-right: 1.5rem !important
  }

  [dir=rtl] .lg\:pr-6 {
    padding-left: 1.5rem !important
  }

  [dir] .lg\:pb-6 {
    padding-bottom: 1.5rem !important
  }

  [dir=ltr] .lg\:pl-6 {
padding-left: 1.5rem !important
  }

  [dir=rtl] .lg\:pl-6 {
    padding-right: 1.5rem !important
  }

  [dir] .lg\:pt-8 {
    padding-top: 2rem !important
  }

  [dir=ltr] .lg\:pr-8 {
padding-right: 2rem !important
  }

  [dir=rtl] .lg\:pr-8 {
    padding-left: 2rem !important
  }

  [dir] .lg\:pb-8 {
    padding-bottom: 2rem !important
  }

  [dir=ltr] .lg\:pl-8 {
padding-left: 2rem !important
  }

  [dir=rtl] .lg\:pl-8 {
    padding-right: 2rem !important
  }

  [dir] .lg\:pt-10 {
    padding-top: 2.5rem !important
  }

  [dir=ltr] .lg\:pr-10 {
padding-right: 2.5rem !important
  }

  [dir=rtl] .lg\:pr-10 {
    padding-left: 2.5rem !important
  }

  [dir] .lg\:pb-10 {
    padding-bottom: 2.5rem !important
  }

  [dir=ltr] .lg\:pl-10 {
padding-left: 2.5rem !important
  }

  [dir=rtl] .lg\:pl-10 {
    padding-right: 2.5rem !important
  }

  [dir] .lg\:pt-12 {
    padding-top: 3rem !important
  }

  [dir=ltr] .lg\:pr-12 {
padding-right: 3rem !important
  }

  [dir=rtl] .lg\:pr-12 {
    padding-left: 3rem !important
  }

  [dir] .lg\:pb-12 {
    padding-bottom: 3rem !important
  }

  [dir=ltr] .lg\:pl-12 {
padding-left: 3rem !important
  }

  [dir=rtl] .lg\:pl-12 {
    padding-right: 3rem !important
  }

  [dir] .lg\:pt-16 {
    padding-top: 4rem !important
  }

  [dir=ltr] .lg\:pr-16 {
padding-right: 4rem !important
  }

  [dir=rtl] .lg\:pr-16 {
    padding-left: 4rem !important
  }

  [dir] .lg\:pb-16 {
    padding-bottom: 4rem !important
  }

  [dir=ltr] .lg\:pl-16 {
padding-left: 4rem !important
  }

  [dir=rtl] .lg\:pl-16 {
    padding-right: 4rem !important
  }

  [dir] .lg\:pt-20 {
    padding-top: 5rem !important
  }

  [dir=ltr] .lg\:pr-20 {
padding-right: 5rem !important
  }

  [dir=rtl] .lg\:pr-20 {
    padding-left: 5rem !important
  }

  [dir] .lg\:pb-20 {
    padding-bottom: 5rem !important
  }

  [dir=ltr] .lg\:pl-20 {
padding-left: 5rem !important
  }

  [dir=rtl] .lg\:pl-20 {
    padding-right: 5rem !important
  }

  [dir] .lg\:pt-24 {
    padding-top: 6rem !important
  }

  [dir=ltr] .lg\:pr-24 {
padding-right: 6rem !important
  }

  [dir=rtl] .lg\:pr-24 {
    padding-left: 6rem !important
  }

  [dir] .lg\:pb-24 {
    padding-bottom: 6rem !important
  }

  [dir=ltr] .lg\:pl-24 {
padding-left: 6rem !important
  }

  [dir=rtl] .lg\:pl-24 {
    padding-right: 6rem !important
  }

  [dir] .lg\:pt-32 {
    padding-top: 8rem !important
  }

  [dir=ltr] .lg\:pr-32 {
padding-right: 8rem !important
  }

  [dir=rtl] .lg\:pr-32 {
    padding-left: 8rem !important
  }

  [dir] .lg\:pb-32 {
    padding-bottom: 8rem !important
  }

  [dir=ltr] .lg\:pl-32 {
padding-left: 8rem !important
  }

  [dir=rtl] .lg\:pl-32 {
    padding-right: 8rem !important
  }

  [dir] .lg\:pt-px {
    padding-top: 1px !important
  }

  [dir=ltr] .lg\:pr-px {
padding-right: 1px !important
  }

  [dir=rtl] .lg\:pr-px {
    padding-left: 1px !important
  }

  [dir] .lg\:pb-px {
    padding-bottom: 1px !important
  }

  [dir=ltr] .lg\:pl-px {
padding-left: 1px !important
  }

  [dir=rtl] .lg\:pl-px {
    padding-right: 1px !important
  }

  [dir] .lg\:pt-base {
    padding-top: 2.2rem !important
  }

  [dir=ltr] .lg\:pr-base {
padding-right: 2.2rem !important
  }

  [dir=rtl] .lg\:pr-base {
    padding-left: 2.2rem !important
  }

  [dir] .lg\:pb-base {
    padding-bottom: 2.2rem !important
  }

  [dir=ltr] .lg\:pl-base {
padding-left: 2.2rem !important
  }

  [dir=rtl] .lg\:pl-base {
    padding-right: 2.2rem !important
  }

  .lg\:placeholder-transparent::-webkit-input-placeholder {
    color: transparent !important
  }

  .lg\:placeholder-transparent::-moz-placeholder {
    color: transparent !important
  }

  .lg\:placeholder-transparent:-ms-input-placeholder {
    color: transparent !important
  }

  .lg\:placeholder-transparent::-ms-input-placeholder {
    color: transparent !important
  }

  .lg\:placeholder-transparent::placeholder {
    color: transparent !important
  }

  .lg\:placeholder-black::-webkit-input-placeholder {
    color: #22292f !important
  }

  .lg\:placeholder-black::-moz-placeholder {
    color: #22292f !important
  }

  .lg\:placeholder-black:-ms-input-placeholder {
    color: #22292f !important
  }

  .lg\:placeholder-black::-ms-input-placeholder {
    color: #22292f !important
  }

  .lg\:placeholder-black::placeholder {
    color: #22292f !important
  }

  .lg\:placeholder-white::-webkit-input-placeholder {
    color: #ffffff !important
  }

  .lg\:placeholder-white::-moz-placeholder {
    color: #ffffff !important
  }

  .lg\:placeholder-white:-ms-input-placeholder {
    color: #ffffff !important
  }

  .lg\:placeholder-white::-ms-input-placeholder {
    color: #ffffff !important
  }

  .lg\:placeholder-white::placeholder {
    color: #ffffff !important
  }

  .lg\:placeholder-grey::-webkit-input-placeholder {
    color: #b8c2cc !important
  }

  .lg\:placeholder-grey::-moz-placeholder {
    color: #b8c2cc !important
  }

  .lg\:placeholder-grey:-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .lg\:placeholder-grey::-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .lg\:placeholder-grey::placeholder {
    color: #b8c2cc !important
  }

  .lg\:placeholder-grey-light::-webkit-input-placeholder {
    color: #dae1e7 !important
  }

  .lg\:placeholder-grey-light::-moz-placeholder {
    color: #dae1e7 !important
  }

  .lg\:placeholder-grey-light:-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .lg\:placeholder-grey-light::-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .lg\:placeholder-grey-light::placeholder {
    color: #dae1e7 !important
  }

  .lg\:focus\:placeholder-transparent:focus::-webkit-input-placeholder {
    color: transparent !important
  }

  .lg\:focus\:placeholder-transparent:focus::-moz-placeholder {
    color: transparent !important
  }

  .lg\:focus\:placeholder-transparent:focus:-ms-input-placeholder {
    color: transparent !important
  }

  .lg\:focus\:placeholder-transparent:focus::-ms-input-placeholder {
    color: transparent !important
  }

  .lg\:focus\:placeholder-transparent:focus::placeholder {
    color: transparent !important
  }

  .lg\:focus\:placeholder-black:focus::-webkit-input-placeholder {
    color: #22292f !important
  }

  .lg\:focus\:placeholder-black:focus::-moz-placeholder {
    color: #22292f !important
  }

  .lg\:focus\:placeholder-black:focus:-ms-input-placeholder {
    color: #22292f !important
  }

  .lg\:focus\:placeholder-black:focus::-ms-input-placeholder {
    color: #22292f !important
  }

  .lg\:focus\:placeholder-black:focus::placeholder {
    color: #22292f !important
  }

  .lg\:focus\:placeholder-white:focus::-webkit-input-placeholder {
    color: #ffffff !important
  }

  .lg\:focus\:placeholder-white:focus::-moz-placeholder {
    color: #ffffff !important
  }

  .lg\:focus\:placeholder-white:focus:-ms-input-placeholder {
    color: #ffffff !important
  }

  .lg\:focus\:placeholder-white:focus::-ms-input-placeholder {
    color: #ffffff !important
  }

  .lg\:focus\:placeholder-white:focus::placeholder {
    color: #ffffff !important
  }

  .lg\:focus\:placeholder-grey:focus::-webkit-input-placeholder {
    color: #b8c2cc !important
  }

  .lg\:focus\:placeholder-grey:focus::-moz-placeholder {
    color: #b8c2cc !important
  }

  .lg\:focus\:placeholder-grey:focus:-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .lg\:focus\:placeholder-grey:focus::-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .lg\:focus\:placeholder-grey:focus::placeholder {
    color: #b8c2cc !important
  }

  .lg\:focus\:placeholder-grey-light:focus::-webkit-input-placeholder {
    color: #dae1e7 !important
  }

  .lg\:focus\:placeholder-grey-light:focus::-moz-placeholder {
    color: #dae1e7 !important
  }

  .lg\:focus\:placeholder-grey-light:focus:-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .lg\:focus\:placeholder-grey-light:focus::-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .lg\:focus\:placeholder-grey-light:focus::placeholder {
    color: #dae1e7 !important
  }

  .lg\:pointer-events-none {
    pointer-events: none !important
  }

  .lg\:pointer-events-auto {
    pointer-events: auto !important
  }

  .lg\:static {
    position: static !important
  }

  .lg\:fixed {
    position: fixed !important
  }

  .lg\:absolute {
    position: absolute !important
  }

  .lg\:relative {
    position: relative !important
  }

  .lg\:sticky {
    position: -webkit-sticky !important;
    position: sticky !important
  }

  .lg\:inset-0 {
    top: 0 !important;
    bottom: 0 !important
  }

  [dir=ltr] .lg\:inset-0 {
right: 0 !important;
left: 0 !important
  }

  [dir=rtl] .lg\:inset-0 {
    left: 0 !important;
    right: 0 !important
  }

  .lg\:inset-auto {
    top: auto !important;
    bottom: auto !important
  }

  [dir=ltr] .lg\:inset-auto {
right: auto !important;
left: auto !important
  }

  [dir=rtl] .lg\:inset-auto {
    left: auto !important;
    right: auto !important
  }

  .lg\:inset-y-0 {
    top: 0 !important;
    bottom: 0 !important
  }

  [dir=ltr] .lg\:inset-x-0 {
right: 0 !important;
left: 0 !important
  }

  [dir=rtl] .lg\:inset-x-0 {
    left: 0 !important;
    right: 0 !important
  }

  .lg\:inset-y-auto {
    top: auto !important;
    bottom: auto !important
  }

  [dir=ltr] .lg\:inset-x-auto {
right: auto !important;
left: auto !important
  }

  [dir=rtl] .lg\:inset-x-auto {
    left: auto !important;
    right: auto !important
  }

  .lg\:top-0 {
    top: 0 !important
  }

  [dir=ltr] .lg\:right-0 {
right: 0 !important
  }

  [dir=rtl] .lg\:right-0 {
    left: 0 !important
  }

  .lg\:bottom-0 {
    bottom: 0 !important
  }

  [dir=ltr] .lg\:left-0 {
left: 0 !important
  }

  [dir=rtl] .lg\:left-0 {
    right: 0 !important
  }

  .lg\:top-auto {
    top: auto !important
  }

  [dir=ltr] .lg\:right-auto {
right: auto !important
  }

  [dir=rtl] .lg\:right-auto {
    left: auto !important
  }

  .lg\:bottom-auto {
    bottom: auto !important
  }

  [dir=ltr] .lg\:left-auto {
left: auto !important
  }

  [dir=rtl] .lg\:left-auto {
    right: auto !important
  }

  .lg\:resize-none {
    resize: none !important
  }

  .lg\:resize-y {
    resize: vertical !important
  }

  .lg\:resize-x {
    resize: horizontal !important
  }

  .lg\:resize {
    resize: both !important
  }

  [dir] .lg\:shadow {
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
  }

  [dir] .lg\:shadow-md {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .lg\:shadow-lg {
    box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .lg\:shadow-inner {
    box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
  }

  [dir] .lg\:shadow-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
  }

  [dir] .lg\:shadow-2xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
  }

  [dir] .lg\:shadow-outline {
    box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
  }

  [dir] .lg\:shadow-none {
    box-shadow: none !important
  }

  [dir] .lg\:shadow-drop {
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
  }

  [dir] .lg\:hover\:shadow:hover {
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
  }

  [dir] .lg\:hover\:shadow-md:hover {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .lg\:hover\:shadow-lg:hover {
    box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .lg\:hover\:shadow-inner:hover {
    box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
  }

  [dir] .lg\:hover\:shadow-xl:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
  }

  [dir] .lg\:hover\:shadow-2xl:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
  }

  [dir] .lg\:hover\:shadow-outline:hover {
    box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
  }

  [dir] .lg\:hover\:shadow-none:hover {
    box-shadow: none !important
  }

  [dir] .lg\:hover\:shadow-drop:hover {
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
  }

  [dir] .lg\:focus\:shadow:focus {
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
  }

  [dir] .lg\:focus\:shadow-md:focus {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .lg\:focus\:shadow-lg:focus {
    box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .lg\:focus\:shadow-inner:focus {
    box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
  }

  [dir] .lg\:focus\:shadow-xl:focus {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
  }

  [dir] .lg\:focus\:shadow-2xl:focus {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
  }

  [dir] .lg\:focus\:shadow-outline:focus {
    box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
  }

  [dir] .lg\:focus\:shadow-none:focus {
    box-shadow: none !important
  }

  [dir] .lg\:focus\:shadow-drop:focus {
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
  }

  .lg\:table-auto {
    table-layout: auto !important
  }

  .lg\:table-fixed {
    table-layout: fixed !important
  }

  [dir=ltr] .lg\:text-left {
text-align: left !important
  }

  [dir=rtl] .lg\:text-left {
    text-align: right !important
  }

  [dir] .lg\:text-center {
    text-align: center !important
  }

  [dir=ltr] .lg\:text-right {
text-align: right !important
  }

  [dir=rtl] .lg\:text-right {
    text-align: left !important
  }

  [dir] .lg\:text-justify {
    text-align: justify !important
  }

  .lg\:text-inherit {
    color: inherit !important
  }

  .lg\:text-transparent {
    color: transparent !important
  }

  .lg\:text-black {
    color: #22292f !important
  }

  .lg\:text-white {
    color: #ffffff !important
  }

  .lg\:text-grey {
    color: #b8c2cc !important
  }

  .lg\:text-grey-light {
    color: #dae1e7 !important
  }

  .lg\:hover\:text-inherit:hover {
    color: inherit !important
  }

  .lg\:hover\:text-transparent:hover {
    color: transparent !important
  }

  .lg\:hover\:text-black:hover {
    color: #22292f !important
  }

  .lg\:hover\:text-white:hover {
    color: #ffffff !important
  }

  .lg\:hover\:text-grey:hover {
    color: #b8c2cc !important
  }

  .lg\:hover\:text-grey-light:hover {
    color: #dae1e7 !important
  }

  .lg\:focus\:text-inherit:focus {
    color: inherit !important
  }

  .lg\:focus\:text-transparent:focus {
    color: transparent !important
  }

  .lg\:focus\:text-black:focus {
    color: #22292f !important
  }

  .lg\:focus\:text-white:focus {
    color: #ffffff !important
  }

  .lg\:focus\:text-grey:focus {
    color: #b8c2cc !important
  }

  .lg\:focus\:text-grey-light:focus {
    color: #dae1e7 !important
  }

  .lg\:text-xs {
    font-size: .75rem !important
  }

  .lg\:text-sm {
    font-size: .875rem !important
  }

  .lg\:text-base {
    font-size: 1rem !important
  }

  .lg\:text-lg {
    font-size: 1.125rem !important
  }

  .lg\:text-xl {
    font-size: 1.25rem !important
  }

  .lg\:text-2xl {
    font-size: 1.5rem !important
  }

  .lg\:text-3xl {
    font-size: 1.875rem !important
  }

  .lg\:text-4xl {
    font-size: 2.25rem !important
  }

  .lg\:text-5xl {
    font-size: 3rem !important
  }

  .lg\:text-6xl {
    font-size: 4rem !important
  }

  .lg\:italic {
    font-style: italic !important
  }

  .lg\:not-italic {
    font-style: normal !important
  }

  .lg\:hover\:italic:hover {
    font-style: italic !important
  }

  .lg\:hover\:not-italic:hover {
    font-style: normal !important
  }

  .lg\:focus\:italic:focus {
    font-style: italic !important
  }

  .lg\:focus\:not-italic:focus {
    font-style: normal !important
  }

  .lg\:uppercase {
    text-transform: uppercase !important
  }

  .lg\:lowercase {
    text-transform: lowercase !important
  }

  .lg\:capitalize {
    text-transform: capitalize !important
  }

  .lg\:normal-case {
    text-transform: none !important
  }

  .lg\:hover\:uppercase:hover {
    text-transform: uppercase !important
  }

  .lg\:hover\:lowercase:hover {
    text-transform: lowercase !important
  }

  .lg\:hover\:capitalize:hover {
    text-transform: capitalize !important
  }

  .lg\:hover\:normal-case:hover {
    text-transform: none !important
  }

  .lg\:focus\:uppercase:focus {
    text-transform: uppercase !important
  }

  .lg\:focus\:lowercase:focus {
    text-transform: lowercase !important
  }

  .lg\:focus\:capitalize:focus {
    text-transform: capitalize !important
  }

  .lg\:focus\:normal-case:focus {
    text-transform: none !important
  }

  .lg\:underline {
    text-decoration: underline !important
  }

  .lg\:line-through {
    text-decoration: line-through !important
  }

  .lg\:no-underline {
    text-decoration: none !important
  }

  .lg\:hover\:underline:hover {
    text-decoration: underline !important
  }

  .lg\:hover\:line-through:hover {
    text-decoration: line-through !important
  }

  .lg\:hover\:no-underline:hover {
    text-decoration: none !important
  }

  .lg\:focus\:underline:focus {
    text-decoration: underline !important
  }

  .lg\:focus\:line-through:focus {
    text-decoration: line-through !important
  }

  .lg\:focus\:no-underline:focus {
    text-decoration: none !important
  }

  .lg\:antialiased {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important
  }

  .lg\:subpixel-antialiased {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important
  }

  .lg\:hover\:antialiased:hover {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important
  }

  .lg\:hover\:subpixel-antialiased:hover {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important
  }

  .lg\:focus\:antialiased:focus {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important
  }

  .lg\:focus\:subpixel-antialiased:focus {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important
  }

  .lg\:select-none {
    -webkit-user-select: none !important;
       -moz-user-select: none !important;
        -ms-user-select: none !important;
            user-select: none !important
  }

  .lg\:select-text {
    -webkit-user-select: text !important;
       -moz-user-select: text !important;
        -ms-user-select: text !important;
            user-select: text !important
  }

  .lg\:select-all {
    -webkit-user-select: all !important;
       -moz-user-select: all !important;
        -ms-user-select: all !important;
            user-select: all !important
  }

  .lg\:select-auto {
    -webkit-user-select: auto !important;
       -moz-user-select: auto !important;
        -ms-user-select: auto !important;
            user-select: auto !important
  }

  .lg\:align-baseline {
    vertical-align: baseline !important
  }

  .lg\:align-top {
    vertical-align: top !important
  }

  .lg\:align-middle {
    vertical-align: middle !important
  }

  .lg\:align-bottom {
    vertical-align: bottom !important
  }

  .lg\:align-text-top {
    vertical-align: text-top !important
  }

  .lg\:align-text-bottom {
    vertical-align: text-bottom !important
  }

  .lg\:visible {
    visibility: visible !important
  }

  .lg\:invisible {
    visibility: hidden !important
  }

  .lg\:whitespace-normal {
    white-space: normal !important
  }

  .lg\:whitespace-no-wrap {
    white-space: nowrap !important
  }

  .lg\:whitespace-pre {
    white-space: pre !important
  }

  .lg\:whitespace-pre-line {
    white-space: pre-line !important
  }

  .lg\:whitespace-pre-wrap {
    white-space: pre-wrap !important
  }

  .lg\:break-normal {
    overflow-wrap: normal !important;
    word-break: normal !important
  }

  .lg\:break-words {
    overflow-wrap: break-word !important
  }

  .lg\:break-all {
    word-break: break-all !important
  }

  .lg\:truncate {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important
  }

  .lg\:w-1 {
    width: 0.25rem !important
  }

  .lg\:w-2 {
    width: 0.5rem !important
  }

  .lg\:w-3 {
    width: 0.75rem !important
  }

  .lg\:w-4 {
    width: 1rem !important
  }

  .lg\:w-5 {
    width: 1.25rem !important
  }

  .lg\:w-6 {
    width: 1.5rem !important
  }

  .lg\:w-8 {
    width: 2rem !important
  }

  .lg\:w-10 {
    width: 2.5rem !important
  }

  .lg\:w-12 {
    width: 3rem !important
  }

  .lg\:w-16 {
    width: 4rem !important
  }

  .lg\:w-24 {
    width: 6rem !important
  }

  .lg\:w-32 {
    width: 8rem !important
  }

  .lg\:w-48 {
    width: 12rem !important
  }

  .lg\:w-64 {
    width: 16rem !important
  }

  .lg\:w-auto {
    width: auto !important
  }

  .lg\:w-px {
    width: 1px !important
  }

  .lg\:w-1\/2 {
    width: 50% !important
  }

  .lg\:w-1\/3 {
    width: 33.33333% !important
  }

  .lg\:w-2\/3 {
    width: 66.66667% !important
  }

  .lg\:w-1\/4 {
    width: 25% !important
  }

  .lg\:w-3\/4 {
    width: 75% !important
  }

  .lg\:w-1\/5 {
    width: 20% !important
  }

  .lg\:w-2\/5 {
    width: 40% !important
  }

  .lg\:w-3\/5 {
    width: 60% !important
  }

  .lg\:w-4\/5 {
    width: 80% !important
  }

  .lg\:w-1\/6 {
    width: 16.66667% !important
  }

  .lg\:w-5\/6 {
    width: 83.33333% !important
  }

  .lg\:w-1\/12 {
    width: 8.33333% !important
  }

  .lg\:w-2\/12 {
    width: 16.66667% !important
  }

  .lg\:w-3\/12 {
    width: 25% !important
  }

  .lg\:w-4\/12 {
    width: 33.33333% !important
  }

  .lg\:w-5\/12 {
    width: 41.66667% !important
  }

  .lg\:w-6\/12 {
    width: 50% !important
  }

  .lg\:w-7\/12 {
    width: 58.33333% !important
  }

  .lg\:w-8\/12 {
    width: 66.66667% !important
  }

  .lg\:w-9\/12 {
    width: 75% !important
  }

  .lg\:w-10\/12 {
    width: 83.33333% !important
  }

  .lg\:w-11\/12 {
    width: 91.66667% !important
  }

  .lg\:w-full {
    width: 100% !important
  }

  .lg\:w-screen {
    width: 100vw !important
  }

  .lg\:z-0 {
    z-index: 0 !important
  }

  .lg\:z-10 {
    z-index: 10 !important
  }

  .lg\:z-20 {
    z-index: 20 !important
  }

  .lg\:z-30 {
    z-index: 30 !important
  }

  .lg\:z-40 {
    z-index: 40 !important
  }

  .lg\:z-50 {
    z-index: 50 !important
  }

  .lg\:z-auto {
    z-index: auto !important
  }
}

@media (min-width: 1200px) {
  .xl\:sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important
  }
  [dir] .xl\:sr-only {
    padding: 0 !important;
    margin: -1px !important;
    border-width: 0 !important
  }

  .xl\:not-sr-only {
    position: static !important;
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important
  }

  [dir] .xl\:not-sr-only {
    padding: 0 !important;
    margin: 0 !important
  }

  .xl\:focus\:sr-only:focus {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important
  }

  [dir] .xl\:focus\:sr-only:focus {
    padding: 0 !important;
    margin: -1px !important;
    border-width: 0 !important
  }

  .xl\:focus\:not-sr-only:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important
  }

  [dir] .xl\:focus\:not-sr-only:focus {
    padding: 0 !important;
    margin: 0 !important
  }

  .xl\:appearance-none {
    -webkit-appearance: none !important;
       -moz-appearance: none !important;
            appearance: none !important
  }

  [dir] .xl\:bg-fixed {
    background-attachment: fixed !important
  }

  [dir] .xl\:bg-local {
    background-attachment: local !important
  }

  [dir] .xl\:bg-scroll {
    background-attachment: scroll !important
  }

  [dir] .xl\:bg-transparent {
    background-color: transparent !important
  }

  [dir] .xl\:bg-black {
    background-color: #22292f !important
  }

  [dir] .xl\:bg-white {
    background-color: #ffffff !important
  }

  [dir] .xl\:bg-grey {
    background-color: #b8c2cc !important
  }

  [dir] .xl\:bg-grey-light {
    background-color: #dae1e7 !important
  }

  [dir] .xl\:hover\:bg-transparent:hover {
    background-color: transparent !important
  }

  [dir] .xl\:hover\:bg-black:hover {
    background-color: #22292f !important
  }

  [dir] .xl\:hover\:bg-white:hover {
    background-color: #ffffff !important
  }

  [dir] .xl\:hover\:bg-grey:hover {
    background-color: #b8c2cc !important
  }

  [dir] .xl\:hover\:bg-grey-light:hover {
    background-color: #dae1e7 !important
  }

  [dir] .xl\:focus\:bg-transparent:focus {
    background-color: transparent !important
  }

  [dir] .xl\:focus\:bg-black:focus {
    background-color: #22292f !important
  }

  [dir] .xl\:focus\:bg-white:focus {
    background-color: #ffffff !important
  }

  [dir] .xl\:focus\:bg-grey:focus {
    background-color: #b8c2cc !important
  }

  [dir] .xl\:focus\:bg-grey-light:focus {
    background-color: #dae1e7 !important
  }

  [dir] .xl\:bg-auto {
    background-size: auto !important
  }

  [dir] .xl\:bg-cover {
    background-size: cover !important
  }

  [dir] .xl\:bg-contain {
    background-size: contain !important
  }

  [dir] .xl\:border-transparent {
    border-color: transparent !important
  }

  [dir] .xl\:border-black {
    border-color: #22292f !important
  }

  [dir] .xl\:border-white {
    border-color: #ffffff !important
  }

  [dir] .xl\:border-grey {
    border-color: #b8c2cc !important
  }

  [dir] .xl\:border-grey-light {
    border-color: #dae1e7 !important
  }

  [dir] .xl\:hover\:border-transparent:hover {
    border-color: transparent !important
  }

  [dir] .xl\:hover\:border-black:hover {
    border-color: #22292f !important
  }

  [dir] .xl\:hover\:border-white:hover {
    border-color: #ffffff !important
  }

  [dir] .xl\:hover\:border-grey:hover {
    border-color: #b8c2cc !important
  }

  [dir] .xl\:hover\:border-grey-light:hover {
    border-color: #dae1e7 !important
  }

  [dir] .xl\:border-solid {
    border-style: solid !important
  }

  [dir] .xl\:border-dashed {
    border-style: dashed !important
  }

  [dir] .xl\:border-dotted {
    border-style: dotted !important
  }

  [dir] .xl\:border-double {
    border-style: double !important
  }

  [dir] .xl\:border-none {
    border-style: none !important
  }

  [dir] .xl\:border-0 {
    border-width: 0 !important
  }

  [dir] .xl\:border-2 {
    border-width: 2px !important
  }

  [dir] .xl\:border-4 {
    border-width: 4px !important
  }

  [dir] .xl\:border-8 {
    border-width: 8px !important
  }

  [dir] .xl\:border {
    border-width: 1px !important
  }

  [dir] .xl\:border-t-0 {
    border-top-width: 0 !important
  }

  [dir=ltr] .xl\:border-r-0 {
border-right-width: 0 !important
  }

  [dir=rtl] .xl\:border-r-0 {
    border-left-width: 0 !important
  }

  [dir] .xl\:border-b-0 {
    border-bottom-width: 0 !important
  }

  [dir=ltr] .xl\:border-l-0 {
border-left-width: 0 !important
  }

  [dir=rtl] .xl\:border-l-0 {
    border-right-width: 0 !important
  }

  [dir] .xl\:border-t-2 {
    border-top-width: 2px !important
  }

  [dir=ltr] .xl\:border-r-2 {
border-right-width: 2px !important
  }

  [dir=rtl] .xl\:border-r-2 {
    border-left-width: 2px !important
  }

  [dir] .xl\:border-b-2 {
    border-bottom-width: 2px !important
  }

  [dir=ltr] .xl\:border-l-2 {
border-left-width: 2px !important
  }

  [dir=rtl] .xl\:border-l-2 {
    border-right-width: 2px !important
  }

  [dir] .xl\:border-t-4 {
    border-top-width: 4px !important
  }

  [dir=ltr] .xl\:border-r-4 {
border-right-width: 4px !important
  }

  [dir=rtl] .xl\:border-r-4 {
    border-left-width: 4px !important
  }

  [dir] .xl\:border-b-4 {
    border-bottom-width: 4px !important
  }

  [dir=ltr] .xl\:border-l-4 {
border-left-width: 4px !important
  }

  [dir=rtl] .xl\:border-l-4 {
    border-right-width: 4px !important
  }

  [dir] .xl\:border-t-8 {
    border-top-width: 8px !important
  }

  [dir=ltr] .xl\:border-r-8 {
border-right-width: 8px !important
  }

  [dir=rtl] .xl\:border-r-8 {
    border-left-width: 8px !important
  }

  [dir] .xl\:border-b-8 {
    border-bottom-width: 8px !important
  }

  [dir=ltr] .xl\:border-l-8 {
border-left-width: 8px !important
  }

  [dir=rtl] .xl\:border-l-8 {
    border-right-width: 8px !important
  }

  [dir] .xl\:border-t {
    border-top-width: 1px !important
  }

  [dir=ltr] .xl\:border-r {
border-right-width: 1px !important
  }

  [dir=rtl] .xl\:border-r {
    border-left-width: 1px !important
  }

  [dir] .xl\:border-b {
    border-bottom-width: 1px !important
  }

  [dir=ltr] .xl\:border-l {
border-left-width: 1px !important
  }

  [dir=rtl] .xl\:border-l {
    border-right-width: 1px !important
  }

  .xl\:block {
    display: block !important
  }

  .xl\:inline-block {
    display: inline-block !important
  }

  .xl\:inline {
    display: inline !important
  }

  .xl\:flex {
    display: -webkit-box !important;
    display: flex !important
  }

  .xl\:inline-flex {
    display: -webkit-inline-box !important;
    display: inline-flex !important
  }

  .xl\:table {
    display: table !important
  }

  .xl\:table-row {
    display: table-row !important
  }

  .xl\:table-cell {
    display: table-cell !important
  }

  .xl\:hidden {
    display: none !important
  }

  .xl\:flex-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
            flex-direction: row !important
  }

  .xl\:flex-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
            flex-direction: row-reverse !important
  }

  .xl\:flex-col {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
            flex-direction: column !important
  }

  .xl\:flex-col-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
            flex-direction: column-reverse !important
  }

  .xl\:flex-wrap {
    flex-wrap: wrap !important
  }

  .xl\:flex-wrap-reverse {
    flex-wrap: wrap-reverse !important
  }

  .xl\:flex-no-wrap {
    flex-wrap: nowrap !important
  }

  .xl\:items-start {
    -webkit-box-align: start !important;
            align-items: flex-start !important
  }

  .xl\:items-end {
    -webkit-box-align: end !important;
            align-items: flex-end !important
  }

  .xl\:items-center {
    -webkit-box-align: center !important;
            align-items: center !important
  }

  .xl\:items-baseline {
    -webkit-box-align: baseline !important;
            align-items: baseline !important
  }

  .xl\:items-stretch {
    -webkit-box-align: stretch !important;
            align-items: stretch !important
  }

  .xl\:self-auto {
    align-self: auto !important
  }

  .xl\:self-start {
    align-self: flex-start !important
  }

  .xl\:self-end {
    align-self: flex-end !important
  }

  .xl\:self-center {
    align-self: center !important
  }

  .xl\:self-stretch {
    align-self: stretch !important
  }

  .xl\:justify-start {
    -webkit-box-pack: start !important;
            justify-content: flex-start !important
  }

  .xl\:justify-end {
    -webkit-box-pack: end !important;
            justify-content: flex-end !important
  }

  .xl\:justify-center {
    -webkit-box-pack: center !important;
            justify-content: center !important
  }

  .xl\:justify-between {
    -webkit-box-pack: justify !important;
            justify-content: space-between !important
  }

  .xl\:justify-around {
    justify-content: space-around !important
  }

  .xl\:content-center {
    align-content: center !important
  }

  .xl\:content-start {
    align-content: flex-start !important
  }

  .xl\:content-end {
    align-content: flex-end !important
  }

  .xl\:content-between {
    align-content: space-between !important
  }

  .xl\:content-around {
    align-content: space-around !important
  }

  .xl\:flex-1 {
    -webkit-box-flex: 1 !important;
            flex: 1 1 0% !important
  }

  .xl\:flex-auto {
    -webkit-box-flex: 1 !important;
            flex: 1 1 auto !important
  }

  .xl\:flex-initial {
    -webkit-box-flex: 0 !important;
            flex: 0 1 auto !important
  }

  .xl\:flex-none {
    -webkit-box-flex: 0 !important;
            flex: none !important
  }

  .xl\:flex-grow-0 {
    -webkit-box-flex: 0 !important;
            flex-grow: 0 !important
  }

  .xl\:flex-grow {
    -webkit-box-flex: 1 !important;
            flex-grow: 1 !important
  }

  .xl\:flex-shrink-0 {
    flex-shrink: 0 !important
  }

  .xl\:flex-shrink {
    flex-shrink: 1 !important
  }

  .xl\:order-1 {
    -webkit-box-ordinal-group: 2 !important;
            order: 1 !important
  }

  .xl\:order-2 {
    -webkit-box-ordinal-group: 3 !important;
            order: 2 !important
  }

  .xl\:order-3 {
    -webkit-box-ordinal-group: 4 !important;
            order: 3 !important
  }

  .xl\:order-4 {
    -webkit-box-ordinal-group: 5 !important;
            order: 4 !important
  }

  .xl\:order-5 {
    -webkit-box-ordinal-group: 6 !important;
            order: 5 !important
  }

  .xl\:order-6 {
    -webkit-box-ordinal-group: 7 !important;
            order: 6 !important
  }

  .xl\:order-first {
    -webkit-box-ordinal-group: 0 !important;
            order: -1 !important
  }

  .xl\:order-last {
    -webkit-box-ordinal-group: 1000 !important;
            order: 999 !important
  }

  .xl\:order-normal {
    -webkit-box-ordinal-group: 1 !important;
            order: 0 !important
  }

  .xl\:hover\:order-1:hover {
    -webkit-box-ordinal-group: 2 !important;
            order: 1 !important
  }

  .xl\:hover\:order-2:hover {
    -webkit-box-ordinal-group: 3 !important;
            order: 2 !important
  }

  .xl\:hover\:order-3:hover {
    -webkit-box-ordinal-group: 4 !important;
            order: 3 !important
  }

  .xl\:hover\:order-4:hover {
    -webkit-box-ordinal-group: 5 !important;
            order: 4 !important
  }

  .xl\:hover\:order-5:hover {
    -webkit-box-ordinal-group: 6 !important;
            order: 5 !important
  }

  .xl\:hover\:order-6:hover {
    -webkit-box-ordinal-group: 7 !important;
            order: 6 !important
  }

  .xl\:hover\:order-first:hover {
    -webkit-box-ordinal-group: 0 !important;
            order: -1 !important
  }

  .xl\:hover\:order-last:hover {
    -webkit-box-ordinal-group: 1000 !important;
            order: 999 !important
  }

  .xl\:hover\:order-normal:hover {
    -webkit-box-ordinal-group: 1 !important;
            order: 0 !important
  }

  .xl\:focus\:order-1:focus {
    -webkit-box-ordinal-group: 2 !important;
            order: 1 !important
  }

  .xl\:focus\:order-2:focus {
    -webkit-box-ordinal-group: 3 !important;
            order: 2 !important
  }

  .xl\:focus\:order-3:focus {
    -webkit-box-ordinal-group: 4 !important;
            order: 3 !important
  }

  .xl\:focus\:order-4:focus {
    -webkit-box-ordinal-group: 5 !important;
            order: 4 !important
  }

  .xl\:focus\:order-5:focus {
    -webkit-box-ordinal-group: 6 !important;
            order: 5 !important
  }

  .xl\:focus\:order-6:focus {
    -webkit-box-ordinal-group: 7 !important;
            order: 6 !important
  }

  .xl\:focus\:order-first:focus {
    -webkit-box-ordinal-group: 0 !important;
            order: -1 !important
  }

  .xl\:focus\:order-last:focus {
    -webkit-box-ordinal-group: 1000 !important;
            order: 999 !important
  }

  .xl\:focus\:order-normal:focus {
    -webkit-box-ordinal-group: 1 !important;
            order: 0 !important
  }

  [dir=ltr] .xl\:float-right {
float: right !important
  }

  [dir=rtl] .xl\:float-right {
    float: left !important
  }

  [dir=ltr] .xl\:float-left {
float: left !important
  }

  [dir=rtl] .xl\:float-left {
    float: right !important
  }

  [dir] .xl\:float-none {
    float: none !important
  }

  .xl\:clearfix:after {
    content: "" !important;
    display: table !important
  }

  [dir] .xl\:clearfix:after {
    clear: both !important
  }

  .xl\:font-light {
    font-weight: 300 !important
  }

  .xl\:font-normal {
    font-weight: 400 !important
  }

  .xl\:font-medium {
    font-weight: 500 !important
  }

  .xl\:font-semibold {
    font-weight: 600 !important
  }

  .xl\:font-bold {
    font-weight: 700 !important
  }

  .xl\:font-extrabold {
    font-weight: 800 !important
  }

  .xl\:font-black {
    font-weight: 900 !important
  }

  .xl\:hover\:font-light:hover {
    font-weight: 300 !important
  }

  .xl\:hover\:font-normal:hover {
    font-weight: 400 !important
  }

  .xl\:hover\:font-medium:hover {
    font-weight: 500 !important
  }

  .xl\:hover\:font-semibold:hover {
    font-weight: 600 !important
  }

  .xl\:hover\:font-bold:hover {
    font-weight: 700 !important
  }

  .xl\:hover\:font-extrabold:hover {
    font-weight: 800 !important
  }

  .xl\:hover\:font-black:hover {
    font-weight: 900 !important
  }

  .xl\:h-1 {
    height: 0.25rem !important
  }

  .xl\:h-2 {
    height: 0.5rem !important
  }

  .xl\:h-3 {
    height: 0.75rem !important
  }

  .xl\:h-4 {
    height: 1rem !important
  }

  .xl\:h-5 {
    height: 1.25rem !important
  }

  .xl\:h-6 {
    height: 1.5rem !important
  }

  .xl\:h-8 {
    height: 2rem !important
  }

  .xl\:h-10 {
    height: 2.5rem !important
  }

  .xl\:h-12 {
    height: 3rem !important
  }

  .xl\:h-16 {
    height: 4rem !important
  }

  .xl\:h-24 {
    height: 6rem !important
  }

  .xl\:h-32 {
    height: 8rem !important
  }

  .xl\:h-48 {
    height: 12rem !important
  }

  .xl\:h-64 {
    height: 16rem !important
  }

  .xl\:h-auto {
    height: auto !important
  }

  .xl\:h-px {
    height: 1px !important
  }

  .xl\:h-full {
    height: 100% !important
  }

  .xl\:h-screen {
    height: 100vh !important
  }

  .xl\:leading-none {
    line-height: 1 !important
  }

  .xl\:leading-tight {
    line-height: 1.25 !important
  }

  .xl\:leading-normal {
    line-height: 1.5 !important
  }

  .xl\:leading-loose {
    line-height: 2 !important
  }

  .xl\:list-inside {
    list-style-position: inside !important
  }

  .xl\:list-outside {
    list-style-position: outside !important
  }

  [dir] .xl\:m-0 {
    margin: 0 !important
  }

  [dir] .xl\:m-1 {
    margin: 0.25rem !important
  }

  [dir] .xl\:m-2 {
    margin: 0.5rem !important
  }

  [dir] .xl\:m-3 {
    margin: 0.75rem !important
  }

  [dir] .xl\:m-4 {
    margin: 1rem !important
  }

  [dir] .xl\:m-5 {
    margin: 1.25rem !important
  }

  [dir] .xl\:m-6 {
    margin: 1.5rem !important
  }

  [dir] .xl\:m-8 {
    margin: 2rem !important
  }

  [dir] .xl\:m-10 {
    margin: 2.5rem !important
  }

  [dir] .xl\:m-12 {
    margin: 3rem !important
  }

  [dir] .xl\:m-16 {
    margin: 4rem !important
  }

  [dir] .xl\:m-20 {
    margin: 5rem !important
  }

  [dir] .xl\:m-24 {
    margin: 6rem !important
  }

  [dir] .xl\:m-32 {
    margin: 8rem !important
  }

  [dir] .xl\:m-auto {
    margin: auto !important
  }

  [dir] .xl\:m-px {
    margin: 1px !important
  }

  [dir] .xl\:m-base {
    margin: 2.2rem !important
  }

  [dir] .xl\:-m-px {
    margin: -1px !important
  }

  [dir] .xl\:-m-1 {
    margin: -0.25rem !important
  }

  [dir] .xl\:-m-2 {
    margin: -0.5rem !important
  }

  [dir] .xl\:-m-3 {
    margin: -0.75rem !important
  }

  [dir] .xl\:-m-4 {
    margin: -1rem !important
  }

  [dir] .xl\:my-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important
  }

  [dir=ltr] .xl\:mx-0 {
margin-left: 0 !important;
margin-right: 0 !important
  }

  [dir=rtl] .xl\:mx-0 {
    margin-right: 0 !important;
    margin-left: 0 !important
  }

  [dir] .xl\:my-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important
  }

  [dir=ltr] .xl\:mx-1 {
margin-left: 0.25rem !important;
margin-right: 0.25rem !important
  }

  [dir=rtl] .xl\:mx-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important
  }

  [dir] .xl\:my-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important
  }

  [dir=ltr] .xl\:mx-2 {
margin-left: 0.5rem !important;
margin-right: 0.5rem !important
  }

  [dir=rtl] .xl\:mx-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important
  }

  [dir] .xl\:my-3 {
    margin-top: 0.75rem !important;
    margin-bottom: 0.75rem !important
  }

  [dir=ltr] .xl\:mx-3 {
margin-left: 0.75rem !important;
margin-right: 0.75rem !important
  }

  [dir=rtl] .xl\:mx-3 {
    margin-right: 0.75rem !important;
    margin-left: 0.75rem !important
  }

  [dir] .xl\:my-4 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important
  }

  [dir=ltr] .xl\:mx-4 {
margin-left: 1rem !important;
margin-right: 1rem !important
  }

  [dir=rtl] .xl\:mx-4 {
    margin-right: 1rem !important;
    margin-left: 1rem !important
  }

  [dir] .xl\:my-5 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important
  }

  [dir=ltr] .xl\:mx-5 {
margin-left: 1.25rem !important;
margin-right: 1.25rem !important
  }

  [dir=rtl] .xl\:mx-5 {
    margin-right: 1.25rem !important;
    margin-left: 1.25rem !important
  }

  [dir] .xl\:my-6 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important
  }

  [dir=ltr] .xl\:mx-6 {
margin-left: 1.5rem !important;
margin-right: 1.5rem !important
  }

  [dir=rtl] .xl\:mx-6 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important
  }

  [dir] .xl\:my-8 {
    margin-top: 2rem !important;
    margin-bottom: 2rem !important
  }

  [dir=ltr] .xl\:mx-8 {
margin-left: 2rem !important;
margin-right: 2rem !important
  }

  [dir=rtl] .xl\:mx-8 {
    margin-right: 2rem !important;
    margin-left: 2rem !important
  }

  [dir] .xl\:my-10 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important
  }

  [dir=ltr] .xl\:mx-10 {
margin-left: 2.5rem !important;
margin-right: 2.5rem !important
  }

  [dir=rtl] .xl\:mx-10 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important
  }

  [dir] .xl\:my-12 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important
  }

  [dir=ltr] .xl\:mx-12 {
margin-left: 3rem !important;
margin-right: 3rem !important
  }

  [dir=rtl] .xl\:mx-12 {
    margin-right: 3rem !important;
    margin-left: 3rem !important
  }

  [dir] .xl\:my-16 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important
  }

  [dir=ltr] .xl\:mx-16 {
margin-left: 4rem !important;
margin-right: 4rem !important
  }

  [dir=rtl] .xl\:mx-16 {
    margin-right: 4rem !important;
    margin-left: 4rem !important
  }

  [dir] .xl\:my-20 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important
  }

  [dir=ltr] .xl\:mx-20 {
margin-left: 5rem !important;
margin-right: 5rem !important
  }

  [dir=rtl] .xl\:mx-20 {
    margin-right: 5rem !important;
    margin-left: 5rem !important
  }

  [dir] .xl\:my-24 {
    margin-top: 6rem !important;
    margin-bottom: 6rem !important
  }

  [dir=ltr] .xl\:mx-24 {
margin-left: 6rem !important;
margin-right: 6rem !important
  }

  [dir=rtl] .xl\:mx-24 {
    margin-right: 6rem !important;
    margin-left: 6rem !important
  }

  [dir] .xl\:my-32 {
    margin-top: 8rem !important;
    margin-bottom: 8rem !important
  }

  [dir=ltr] .xl\:mx-32 {
margin-left: 8rem !important;
margin-right: 8rem !important
  }

  [dir=rtl] .xl\:mx-32 {
    margin-right: 8rem !important;
    margin-left: 8rem !important
  }

  [dir] .xl\:my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important
  }

  [dir=ltr] .xl\:mx-auto {
margin-left: auto !important;
margin-right: auto !important
  }

  [dir=rtl] .xl\:mx-auto {
    margin-right: auto !important;
    margin-left: auto !important
  }

  [dir] .xl\:my-px {
    margin-top: 1px !important;
    margin-bottom: 1px !important
  }

  [dir=ltr] .xl\:mx-px {
margin-left: 1px !important;
margin-right: 1px !important
  }

  [dir=rtl] .xl\:mx-px {
    margin-right: 1px !important;
    margin-left: 1px !important
  }

  [dir] .xl\:my-base {
    margin-top: 2.2rem !important;
    margin-bottom: 2.2rem !important
  }

  [dir=ltr] .xl\:mx-base {
margin-left: 2.2rem !important;
margin-right: 2.2rem !important
  }

  [dir=rtl] .xl\:mx-base {
    margin-right: 2.2rem !important;
    margin-left: 2.2rem !important
  }

  [dir] .xl\:-my-px {
    margin-top: -1px !important;
    margin-bottom: -1px !important
  }

  [dir=ltr] .xl\:-mx-px {
margin-left: -1px !important;
margin-right: -1px !important
  }

  [dir=rtl] .xl\:-mx-px {
    margin-right: -1px !important;
    margin-left: -1px !important
  }

  [dir] .xl\:-my-1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important
  }

  [dir=ltr] .xl\:-mx-1 {
margin-left: -0.25rem !important;
margin-right: -0.25rem !important
  }

  [dir=rtl] .xl\:-mx-1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important
  }

  [dir] .xl\:-my-2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important
  }

  [dir=ltr] .xl\:-mx-2 {
margin-left: -0.5rem !important;
margin-right: -0.5rem !important
  }

  [dir=rtl] .xl\:-mx-2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important
  }

  [dir] .xl\:-my-3 {
    margin-top: -0.75rem !important;
    margin-bottom: -0.75rem !important
  }

  [dir=ltr] .xl\:-mx-3 {
margin-left: -0.75rem !important;
margin-right: -0.75rem !important
  }

  [dir=rtl] .xl\:-mx-3 {
    margin-right: -0.75rem !important;
    margin-left: -0.75rem !important
  }

  [dir] .xl\:-my-4 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important
  }

  [dir=ltr] .xl\:-mx-4 {
margin-left: -1rem !important;
margin-right: -1rem !important
  }

  [dir=rtl] .xl\:-mx-4 {
    margin-right: -1rem !important;
    margin-left: -1rem !important
  }

  [dir] .xl\:mt-0 {
    margin-top: 0 !important
  }

  [dir=ltr] .xl\:mr-0 {
margin-right: 0 !important
  }

  [dir=rtl] .xl\:mr-0 {
    margin-left: 0 !important
  }

  [dir] .xl\:mb-0 {
    margin-bottom: 0 !important
  }

  [dir=ltr] .xl\:ml-0 {
margin-left: 0 !important
  }

  [dir=rtl] .xl\:ml-0 {
    margin-right: 0 !important
  }

  [dir] .xl\:mt-1 {
    margin-top: 0.25rem !important
  }

  [dir=ltr] .xl\:mr-1 {
margin-right: 0.25rem !important
  }

  [dir=rtl] .xl\:mr-1 {
    margin-left: 0.25rem !important
  }

  [dir] .xl\:mb-1 {
    margin-bottom: 0.25rem !important
  }

  [dir=ltr] .xl\:ml-1 {
margin-left: 0.25rem !important
  }

  [dir=rtl] .xl\:ml-1 {
    margin-right: 0.25rem !important
  }

  [dir] .xl\:mt-2 {
    margin-top: 0.5rem !important
  }

  [dir=ltr] .xl\:mr-2 {
margin-right: 0.5rem !important
  }

  [dir=rtl] .xl\:mr-2 {
    margin-left: 0.5rem !important
  }

  [dir] .xl\:mb-2 {
    margin-bottom: 0.5rem !important
  }

  [dir=ltr] .xl\:ml-2 {
margin-left: 0.5rem !important
  }

  [dir=rtl] .xl\:ml-2 {
    margin-right: 0.5rem !important
  }

  [dir] .xl\:mt-3 {
    margin-top: 0.75rem !important
  }

  [dir=ltr] .xl\:mr-3 {
margin-right: 0.75rem !important
  }

  [dir=rtl] .xl\:mr-3 {
    margin-left: 0.75rem !important
  }

  [dir] .xl\:mb-3 {
    margin-bottom: 0.75rem !important
  }

  [dir=ltr] .xl\:ml-3 {
margin-left: 0.75rem !important
  }

  [dir=rtl] .xl\:ml-3 {
    margin-right: 0.75rem !important
  }

  [dir] .xl\:mt-4 {
    margin-top: 1rem !important
  }

  [dir=ltr] .xl\:mr-4 {
margin-right: 1rem !important
  }

  [dir=rtl] .xl\:mr-4 {
    margin-left: 1rem !important
  }

  [dir] .xl\:mb-4 {
    margin-bottom: 1rem !important
  }

  [dir=ltr] .xl\:ml-4 {
margin-left: 1rem !important
  }

  [dir=rtl] .xl\:ml-4 {
    margin-right: 1rem !important
  }

  [dir] .xl\:mt-5 {
    margin-top: 1.25rem !important
  }

  [dir=ltr] .xl\:mr-5 {
margin-right: 1.25rem !important
  }

  [dir=rtl] .xl\:mr-5 {
    margin-left: 1.25rem !important
  }

  [dir] .xl\:mb-5 {
    margin-bottom: 1.25rem !important
  }

  [dir=ltr] .xl\:ml-5 {
margin-left: 1.25rem !important
  }

  [dir=rtl] .xl\:ml-5 {
    margin-right: 1.25rem !important
  }

  [dir] .xl\:mt-6 {
    margin-top: 1.5rem !important
  }

  [dir=ltr] .xl\:mr-6 {
margin-right: 1.5rem !important
  }

  [dir=rtl] .xl\:mr-6 {
    margin-left: 1.5rem !important
  }

  [dir] .xl\:mb-6 {
    margin-bottom: 1.5rem !important
  }

  [dir=ltr] .xl\:ml-6 {
margin-left: 1.5rem !important
  }

  [dir=rtl] .xl\:ml-6 {
    margin-right: 1.5rem !important
  }

  [dir] .xl\:mt-8 {
    margin-top: 2rem !important
  }

  [dir=ltr] .xl\:mr-8 {
margin-right: 2rem !important
  }

  [dir=rtl] .xl\:mr-8 {
    margin-left: 2rem !important
  }

  [dir] .xl\:mb-8 {
    margin-bottom: 2rem !important
  }

  [dir=ltr] .xl\:ml-8 {
margin-left: 2rem !important
  }

  [dir=rtl] .xl\:ml-8 {
    margin-right: 2rem !important
  }

  [dir] .xl\:mt-10 {
    margin-top: 2.5rem !important
  }

  [dir=ltr] .xl\:mr-10 {
margin-right: 2.5rem !important
  }

  [dir=rtl] .xl\:mr-10 {
    margin-left: 2.5rem !important
  }

  [dir] .xl\:mb-10 {
    margin-bottom: 2.5rem !important
  }

  [dir=ltr] .xl\:ml-10 {
margin-left: 2.5rem !important
  }

  [dir=rtl] .xl\:ml-10 {
    margin-right: 2.5rem !important
  }

  [dir] .xl\:mt-12 {
    margin-top: 3rem !important
  }

  [dir=ltr] .xl\:mr-12 {
margin-right: 3rem !important
  }

  [dir=rtl] .xl\:mr-12 {
    margin-left: 3rem !important
  }

  [dir] .xl\:mb-12 {
    margin-bottom: 3rem !important
  }

  [dir=ltr] .xl\:ml-12 {
margin-left: 3rem !important
  }

  [dir=rtl] .xl\:ml-12 {
    margin-right: 3rem !important
  }

  [dir] .xl\:mt-16 {
    margin-top: 4rem !important
  }

  [dir=ltr] .xl\:mr-16 {
margin-right: 4rem !important
  }

  [dir=rtl] .xl\:mr-16 {
    margin-left: 4rem !important
  }

  [dir] .xl\:mb-16 {
    margin-bottom: 4rem !important
  }

  [dir=ltr] .xl\:ml-16 {
margin-left: 4rem !important
  }

  [dir=rtl] .xl\:ml-16 {
    margin-right: 4rem !important
  }

  [dir] .xl\:mt-20 {
    margin-top: 5rem !important
  }

  [dir=ltr] .xl\:mr-20 {
margin-right: 5rem !important
  }

  [dir=rtl] .xl\:mr-20 {
    margin-left: 5rem !important
  }

  [dir] .xl\:mb-20 {
    margin-bottom: 5rem !important
  }

  [dir=ltr] .xl\:ml-20 {
margin-left: 5rem !important
  }

  [dir=rtl] .xl\:ml-20 {
    margin-right: 5rem !important
  }

  [dir] .xl\:mt-24 {
    margin-top: 6rem !important
  }

  [dir=ltr] .xl\:mr-24 {
margin-right: 6rem !important
  }

  [dir=rtl] .xl\:mr-24 {
    margin-left: 6rem !important
  }

  [dir] .xl\:mb-24 {
    margin-bottom: 6rem !important
  }

  [dir=ltr] .xl\:ml-24 {
margin-left: 6rem !important
  }

  [dir=rtl] .xl\:ml-24 {
    margin-right: 6rem !important
  }

  [dir] .xl\:mt-32 {
    margin-top: 8rem !important
  }

  [dir=ltr] .xl\:mr-32 {
margin-right: 8rem !important
  }

  [dir=rtl] .xl\:mr-32 {
    margin-left: 8rem !important
  }

  [dir] .xl\:mb-32 {
    margin-bottom: 8rem !important
  }

  [dir=ltr] .xl\:ml-32 {
margin-left: 8rem !important
  }

  [dir=rtl] .xl\:ml-32 {
    margin-right: 8rem !important
  }

  [dir] .xl\:mt-auto {
    margin-top: auto !important
  }

  [dir=ltr] .xl\:mr-auto {
margin-right: auto !important
  }

  [dir=rtl] .xl\:mr-auto {
    margin-left: auto !important
  }

  [dir] .xl\:mb-auto {
    margin-bottom: auto !important
  }

  [dir=ltr] .xl\:ml-auto {
margin-left: auto !important
  }

  [dir=rtl] .xl\:ml-auto {
    margin-right: auto !important
  }

  [dir] .xl\:mt-px {
    margin-top: 1px !important
  }

  [dir=ltr] .xl\:mr-px {
margin-right: 1px !important
  }

  [dir=rtl] .xl\:mr-px {
    margin-left: 1px !important
  }

  [dir] .xl\:mb-px {
    margin-bottom: 1px !important
  }

  [dir=ltr] .xl\:ml-px {
margin-left: 1px !important
  }

  [dir=rtl] .xl\:ml-px {
    margin-right: 1px !important
  }

  [dir] .xl\:mt-base {
    margin-top: 2.2rem !important
  }

  [dir=ltr] .xl\:mr-base {
margin-right: 2.2rem !important
  }

  [dir=rtl] .xl\:mr-base {
    margin-left: 2.2rem !important
  }

  [dir] .xl\:mb-base {
    margin-bottom: 2.2rem !important
  }

  [dir=ltr] .xl\:ml-base {
margin-left: 2.2rem !important
  }

  [dir=rtl] .xl\:ml-base {
    margin-right: 2.2rem !important
  }

  [dir] .xl\:-mt-px {
    margin-top: -1px !important
  }

  [dir=ltr] .xl\:-mr-px {
margin-right: -1px !important
  }

  [dir=rtl] .xl\:-mr-px {
    margin-left: -1px !important
  }

  [dir] .xl\:-mb-px {
    margin-bottom: -1px !important
  }

  [dir=ltr] .xl\:-ml-px {
margin-left: -1px !important
  }

  [dir=rtl] .xl\:-ml-px {
    margin-right: -1px !important
  }

  [dir] .xl\:-mt-1 {
    margin-top: -0.25rem !important
  }

  [dir=ltr] .xl\:-mr-1 {
margin-right: -0.25rem !important
  }

  [dir=rtl] .xl\:-mr-1 {
    margin-left: -0.25rem !important
  }

  [dir] .xl\:-mb-1 {
    margin-bottom: -0.25rem !important
  }

  [dir=ltr] .xl\:-ml-1 {
margin-left: -0.25rem !important
  }

  [dir=rtl] .xl\:-ml-1 {
    margin-right: -0.25rem !important
  }

  [dir] .xl\:-mt-2 {
    margin-top: -0.5rem !important
  }

  [dir=ltr] .xl\:-mr-2 {
margin-right: -0.5rem !important
  }

  [dir=rtl] .xl\:-mr-2 {
    margin-left: -0.5rem !important
  }

  [dir] .xl\:-mb-2 {
    margin-bottom: -0.5rem !important
  }

  [dir=ltr] .xl\:-ml-2 {
margin-left: -0.5rem !important
  }

  [dir=rtl] .xl\:-ml-2 {
    margin-right: -0.5rem !important
  }

  [dir] .xl\:-mt-3 {
    margin-top: -0.75rem !important
  }

  [dir=ltr] .xl\:-mr-3 {
margin-right: -0.75rem !important
  }

  [dir=rtl] .xl\:-mr-3 {
    margin-left: -0.75rem !important
  }

  [dir] .xl\:-mb-3 {
    margin-bottom: -0.75rem !important
  }

  [dir=ltr] .xl\:-ml-3 {
margin-left: -0.75rem !important
  }

  [dir=rtl] .xl\:-ml-3 {
    margin-right: -0.75rem !important
  }

  [dir] .xl\:-mt-4 {
    margin-top: -1rem !important
  }

  [dir=ltr] .xl\:-mr-4 {
margin-right: -1rem !important
  }

  [dir=rtl] .xl\:-mr-4 {
    margin-left: -1rem !important
  }

  [dir] .xl\:-mb-4 {
    margin-bottom: -1rem !important
  }

  [dir=ltr] .xl\:-ml-4 {
margin-left: -1rem !important
  }

  [dir=rtl] .xl\:-ml-4 {
    margin-right: -1rem !important
  }

  .xl\:max-h-full {
    max-height: 100% !important
  }

  .xl\:max-h-screen {
    max-height: 100vh !important
  }

  .xl\:max-w-xs {
    max-width: 20rem !important
  }

  .xl\:max-w-sm {
    max-width: 30rem !important
  }

  .xl\:max-w-md {
    max-width: 40rem !important
  }

  .xl\:max-w-lg {
    max-width: 50rem !important
  }

  .xl\:max-w-xl {
    max-width: 60rem !important
  }

  .xl\:max-w-2xl {
    max-width: 70rem !important
  }

  .xl\:max-w-3xl {
    max-width: 80rem !important
  }

  .xl\:max-w-4xl {
    max-width: 90rem !important
  }

  .xl\:max-w-5xl {
    max-width: 100rem !important
  }

  .xl\:max-w-full {
    max-width: 100% !important
  }

  .xl\:min-h-0 {
    min-height: 0 !important
  }

  .xl\:min-h-full {
    min-height: 100% !important
  }

  .xl\:min-h-screen {
    min-height: 100vh !important
  }

  .xl\:min-w-0 {
    min-width: 0 !important
  }

  .xl\:min-w-full {
    min-width: 100% !important
  }

  .xl\:object-contain {
    -o-object-fit: contain !important;
       object-fit: contain !important
  }

  .xl\:object-cover {
    -o-object-fit: cover !important;
       object-fit: cover !important
  }

  .xl\:object-fill {
    -o-object-fit: fill !important;
       object-fit: fill !important
  }

  .xl\:object-none {
    -o-object-fit: none !important;
       object-fit: none !important
  }

  .xl\:object-scale-down {
    -o-object-fit: scale-down !important;
       object-fit: scale-down !important
  }

  .xl\:object-bottom {
    -o-object-position: bottom !important;
       object-position: bottom !important
  }

  .xl\:object-center {
    -o-object-position: center !important;
       object-position: center !important
  }

  .xl\:object-left {
    -o-object-position: left !important;
       object-position: left !important
  }

  .xl\:object-left-bottom {
    -o-object-position: left bottom !important;
       object-position: left bottom !important
  }

  .xl\:object-left-top {
    -o-object-position: left top !important;
       object-position: left top !important
  }

  .xl\:object-right {
    -o-object-position: right !important;
       object-position: right !important
  }

  .xl\:object-right-bottom {
    -o-object-position: right bottom !important;
       object-position: right bottom !important
  }

  .xl\:object-right-top {
    -o-object-position: right top !important;
       object-position: right top !important
  }

  .xl\:object-top {
    -o-object-position: top !important;
       object-position: top !important
  }

  .xl\:opacity-0 {
    opacity: 0 !important
  }

  .xl\:opacity-25 {
    opacity: 0.25 !important
  }

  .xl\:opacity-50 {
    opacity: 0.5 !important
  }

  .xl\:opacity-75 {
    opacity: 0.75 !important
  }

  .xl\:opacity-100 {
    opacity: 1 !important
  }

  .xl\:overflow-auto {
    overflow: auto !important
  }

  .xl\:overflow-hidden {
    overflow: hidden !important
  }

  .xl\:overflow-visible {
    overflow: visible !important
  }

  .xl\:overflow-scroll {
    overflow: scroll !important
  }

  .xl\:overflow-x-auto {
    overflow-x: auto !important
  }

  .xl\:overflow-y-auto {
    overflow-y: auto !important
  }

  .xl\:overflow-x-hidden {
    overflow-x: hidden !important
  }

  .xl\:overflow-y-hidden {
    overflow-y: hidden !important
  }

  .xl\:overflow-x-visible {
    overflow-x: visible !important
  }

  .xl\:overflow-y-visible {
    overflow-y: visible !important
  }

  .xl\:overflow-x-scroll {
    overflow-x: scroll !important
  }

  .xl\:overflow-y-scroll {
    overflow-y: scroll !important
  }

  .xl\:scrolling-touch {
    -webkit-overflow-scrolling: touch !important
  }

  .xl\:scrolling-auto {
    -webkit-overflow-scrolling: auto !important
  }

  [dir] .xl\:p-0 {
    padding: 0 !important
  }

  [dir] .xl\:p-1 {
    padding: 0.25rem !important
  }

  [dir] .xl\:p-2 {
    padding: 0.5rem !important
  }

  [dir] .xl\:p-3 {
    padding: 0.75rem !important
  }

  [dir] .xl\:p-4 {
    padding: 1rem !important
  }

  [dir] .xl\:p-5 {
    padding: 1.25rem !important
  }

  [dir] .xl\:p-6 {
    padding: 1.5rem !important
  }

  [dir] .xl\:p-8 {
    padding: 2rem !important
  }

  [dir] .xl\:p-10 {
    padding: 2.5rem !important
  }

  [dir] .xl\:p-12 {
    padding: 3rem !important
  }

  [dir] .xl\:p-16 {
    padding: 4rem !important
  }

  [dir] .xl\:p-20 {
    padding: 5rem !important
  }

  [dir] .xl\:p-24 {
    padding: 6rem !important
  }

  [dir] .xl\:p-32 {
    padding: 8rem !important
  }

  [dir] .xl\:p-px {
    padding: 1px !important
  }

  [dir] .xl\:p-base {
    padding: 2.2rem !important
  }

  [dir] .xl\:py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important
  }

  [dir=ltr] .xl\:px-0 {
padding-left: 0 !important;
padding-right: 0 !important
  }

  [dir=rtl] .xl\:px-0 {
    padding-right: 0 !important;
    padding-left: 0 !important
  }

  [dir] .xl\:py-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important
  }

  [dir=ltr] .xl\:px-1 {
padding-left: 0.25rem !important;
padding-right: 0.25rem !important
  }

  [dir=rtl] .xl\:px-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important
  }

  [dir] .xl\:py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important
  }

  [dir=ltr] .xl\:px-2 {
padding-left: 0.5rem !important;
padding-right: 0.5rem !important
  }

  [dir=rtl] .xl\:px-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important
  }

  [dir] .xl\:py-3 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important
  }

  [dir=ltr] .xl\:px-3 {
padding-left: 0.75rem !important;
padding-right: 0.75rem !important
  }

  [dir=rtl] .xl\:px-3 {
    padding-right: 0.75rem !important;
    padding-left: 0.75rem !important
  }

  [dir] .xl\:py-4 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important
  }

  [dir=ltr] .xl\:px-4 {
padding-left: 1rem !important;
padding-right: 1rem !important
  }

  [dir=rtl] .xl\:px-4 {
    padding-right: 1rem !important;
    padding-left: 1rem !important
  }

  [dir] .xl\:py-5 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important
  }

  [dir=ltr] .xl\:px-5 {
padding-left: 1.25rem !important;
padding-right: 1.25rem !important
  }

  [dir=rtl] .xl\:px-5 {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important
  }

  [dir] .xl\:py-6 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important
  }

  [dir=ltr] .xl\:px-6 {
padding-left: 1.5rem !important;
padding-right: 1.5rem !important
  }

  [dir=rtl] .xl\:px-6 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important
  }

  [dir] .xl\:py-8 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important
  }

  [dir=ltr] .xl\:px-8 {
padding-left: 2rem !important;
padding-right: 2rem !important
  }

  [dir=rtl] .xl\:px-8 {
    padding-right: 2rem !important;
    padding-left: 2rem !important
  }

  [dir] .xl\:py-10 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important
  }

  [dir=ltr] .xl\:px-10 {
padding-left: 2.5rem !important;
padding-right: 2.5rem !important
  }

  [dir=rtl] .xl\:px-10 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important
  }

  [dir] .xl\:py-12 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important
  }

  [dir=ltr] .xl\:px-12 {
padding-left: 3rem !important;
padding-right: 3rem !important
  }

  [dir=rtl] .xl\:px-12 {
    padding-right: 3rem !important;
    padding-left: 3rem !important
  }

  [dir] .xl\:py-16 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important
  }

  [dir=ltr] .xl\:px-16 {
padding-left: 4rem !important;
padding-right: 4rem !important
  }

  [dir=rtl] .xl\:px-16 {
    padding-right: 4rem !important;
    padding-left: 4rem !important
  }

  [dir] .xl\:py-20 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important
  }

  [dir=ltr] .xl\:px-20 {
padding-left: 5rem !important;
padding-right: 5rem !important
  }

  [dir=rtl] .xl\:px-20 {
    padding-right: 5rem !important;
    padding-left: 5rem !important
  }

  [dir] .xl\:py-24 {
    padding-top: 6rem !important;
    padding-bottom: 6rem !important
  }

  [dir=ltr] .xl\:px-24 {
padding-left: 6rem !important;
padding-right: 6rem !important
  }

  [dir=rtl] .xl\:px-24 {
    padding-right: 6rem !important;
    padding-left: 6rem !important
  }

  [dir] .xl\:py-32 {
    padding-top: 8rem !important;
    padding-bottom: 8rem !important
  }

  [dir=ltr] .xl\:px-32 {
padding-left: 8rem !important;
padding-right: 8rem !important
  }

  [dir=rtl] .xl\:px-32 {
    padding-right: 8rem !important;
    padding-left: 8rem !important
  }

  [dir] .xl\:py-px {
    padding-top: 1px !important;
    padding-bottom: 1px !important
  }

  [dir=ltr] .xl\:px-px {
padding-left: 1px !important;
padding-right: 1px !important
  }

  [dir=rtl] .xl\:px-px {
    padding-right: 1px !important;
    padding-left: 1px !important
  }

  [dir] .xl\:py-base {
    padding-top: 2.2rem !important;
    padding-bottom: 2.2rem !important
  }

  [dir=ltr] .xl\:px-base {
padding-left: 2.2rem !important;
padding-right: 2.2rem !important
  }

  [dir=rtl] .xl\:px-base {
    padding-right: 2.2rem !important;
    padding-left: 2.2rem !important
  }

  [dir] .xl\:pt-0 {
    padding-top: 0 !important
  }

  [dir=ltr] .xl\:pr-0 {
padding-right: 0 !important
  }

  [dir=rtl] .xl\:pr-0 {
    padding-left: 0 !important
  }

  [dir] .xl\:pb-0 {
    padding-bottom: 0 !important
  }

  [dir=ltr] .xl\:pl-0 {
padding-left: 0 !important
  }

  [dir=rtl] .xl\:pl-0 {
    padding-right: 0 !important
  }

  [dir] .xl\:pt-1 {
    padding-top: 0.25rem !important
  }

  [dir=ltr] .xl\:pr-1 {
padding-right: 0.25rem !important
  }

  [dir=rtl] .xl\:pr-1 {
    padding-left: 0.25rem !important
  }

  [dir] .xl\:pb-1 {
    padding-bottom: 0.25rem !important
  }

  [dir=ltr] .xl\:pl-1 {
padding-left: 0.25rem !important
  }

  [dir=rtl] .xl\:pl-1 {
    padding-right: 0.25rem !important
  }

  [dir] .xl\:pt-2 {
    padding-top: 0.5rem !important
  }

  [dir=ltr] .xl\:pr-2 {
padding-right: 0.5rem !important
  }

  [dir=rtl] .xl\:pr-2 {
    padding-left: 0.5rem !important
  }

  [dir] .xl\:pb-2 {
    padding-bottom: 0.5rem !important
  }

  [dir=ltr] .xl\:pl-2 {
padding-left: 0.5rem !important
  }

  [dir=rtl] .xl\:pl-2 {
    padding-right: 0.5rem !important
  }

  [dir] .xl\:pt-3 {
    padding-top: 0.75rem !important
  }

  [dir=ltr] .xl\:pr-3 {
padding-right: 0.75rem !important
  }

  [dir=rtl] .xl\:pr-3 {
    padding-left: 0.75rem !important
  }

  [dir] .xl\:pb-3 {
    padding-bottom: 0.75rem !important
  }

  [dir=ltr] .xl\:pl-3 {
padding-left: 0.75rem !important
  }

  [dir=rtl] .xl\:pl-3 {
    padding-right: 0.75rem !important
  }

  [dir] .xl\:pt-4 {
    padding-top: 1rem !important
  }

  [dir=ltr] .xl\:pr-4 {
padding-right: 1rem !important
  }

  [dir=rtl] .xl\:pr-4 {
    padding-left: 1rem !important
  }

  [dir] .xl\:pb-4 {
    padding-bottom: 1rem !important
  }

  [dir=ltr] .xl\:pl-4 {
padding-left: 1rem !important
  }

  [dir=rtl] .xl\:pl-4 {
    padding-right: 1rem !important
  }

  [dir] .xl\:pt-5 {
    padding-top: 1.25rem !important
  }

  [dir=ltr] .xl\:pr-5 {
padding-right: 1.25rem !important
  }

  [dir=rtl] .xl\:pr-5 {
    padding-left: 1.25rem !important
  }

  [dir] .xl\:pb-5 {
    padding-bottom: 1.25rem !important
  }

  [dir=ltr] .xl\:pl-5 {
padding-left: 1.25rem !important
  }

  [dir=rtl] .xl\:pl-5 {
    padding-right: 1.25rem !important
  }

  [dir] .xl\:pt-6 {
    padding-top: 1.5rem !important
  }

  [dir=ltr] .xl\:pr-6 {
padding-right: 1.5rem !important
  }

  [dir=rtl] .xl\:pr-6 {
    padding-left: 1.5rem !important
  }

  [dir] .xl\:pb-6 {
    padding-bottom: 1.5rem !important
  }

  [dir=ltr] .xl\:pl-6 {
padding-left: 1.5rem !important
  }

  [dir=rtl] .xl\:pl-6 {
    padding-right: 1.5rem !important
  }

  [dir] .xl\:pt-8 {
    padding-top: 2rem !important
  }

  [dir=ltr] .xl\:pr-8 {
padding-right: 2rem !important
  }

  [dir=rtl] .xl\:pr-8 {
    padding-left: 2rem !important
  }

  [dir] .xl\:pb-8 {
    padding-bottom: 2rem !important
  }

  [dir=ltr] .xl\:pl-8 {
padding-left: 2rem !important
  }

  [dir=rtl] .xl\:pl-8 {
    padding-right: 2rem !important
  }

  [dir] .xl\:pt-10 {
    padding-top: 2.5rem !important
  }

  [dir=ltr] .xl\:pr-10 {
padding-right: 2.5rem !important
  }

  [dir=rtl] .xl\:pr-10 {
    padding-left: 2.5rem !important
  }

  [dir] .xl\:pb-10 {
    padding-bottom: 2.5rem !important
  }

  [dir=ltr] .xl\:pl-10 {
padding-left: 2.5rem !important
  }

  [dir=rtl] .xl\:pl-10 {
    padding-right: 2.5rem !important
  }

  [dir] .xl\:pt-12 {
    padding-top: 3rem !important
  }

  [dir=ltr] .xl\:pr-12 {
padding-right: 3rem !important
  }

  [dir=rtl] .xl\:pr-12 {
    padding-left: 3rem !important
  }

  [dir] .xl\:pb-12 {
    padding-bottom: 3rem !important
  }

  [dir=ltr] .xl\:pl-12 {
padding-left: 3rem !important
  }

  [dir=rtl] .xl\:pl-12 {
    padding-right: 3rem !important
  }

  [dir] .xl\:pt-16 {
    padding-top: 4rem !important
  }

  [dir=ltr] .xl\:pr-16 {
padding-right: 4rem !important
  }

  [dir=rtl] .xl\:pr-16 {
    padding-left: 4rem !important
  }

  [dir] .xl\:pb-16 {
    padding-bottom: 4rem !important
  }

  [dir=ltr] .xl\:pl-16 {
padding-left: 4rem !important
  }

  [dir=rtl] .xl\:pl-16 {
    padding-right: 4rem !important
  }

  [dir] .xl\:pt-20 {
    padding-top: 5rem !important
  }

  [dir=ltr] .xl\:pr-20 {
padding-right: 5rem !important
  }

  [dir=rtl] .xl\:pr-20 {
    padding-left: 5rem !important
  }

  [dir] .xl\:pb-20 {
    padding-bottom: 5rem !important
  }

  [dir=ltr] .xl\:pl-20 {
padding-left: 5rem !important
  }

  [dir=rtl] .xl\:pl-20 {
    padding-right: 5rem !important
  }

  [dir] .xl\:pt-24 {
    padding-top: 6rem !important
  }

  [dir=ltr] .xl\:pr-24 {
padding-right: 6rem !important
  }

  [dir=rtl] .xl\:pr-24 {
    padding-left: 6rem !important
  }

  [dir] .xl\:pb-24 {
    padding-bottom: 6rem !important
  }

  [dir=ltr] .xl\:pl-24 {
padding-left: 6rem !important
  }

  [dir=rtl] .xl\:pl-24 {
    padding-right: 6rem !important
  }

  [dir] .xl\:pt-32 {
    padding-top: 8rem !important
  }

  [dir=ltr] .xl\:pr-32 {
padding-right: 8rem !important
  }

  [dir=rtl] .xl\:pr-32 {
    padding-left: 8rem !important
  }

  [dir] .xl\:pb-32 {
    padding-bottom: 8rem !important
  }

  [dir=ltr] .xl\:pl-32 {
padding-left: 8rem !important
  }

  [dir=rtl] .xl\:pl-32 {
    padding-right: 8rem !important
  }

  [dir] .xl\:pt-px {
    padding-top: 1px !important
  }

  [dir=ltr] .xl\:pr-px {
padding-right: 1px !important
  }

  [dir=rtl] .xl\:pr-px {
    padding-left: 1px !important
  }

  [dir] .xl\:pb-px {
    padding-bottom: 1px !important
  }

  [dir=ltr] .xl\:pl-px {
padding-left: 1px !important
  }

  [dir=rtl] .xl\:pl-px {
    padding-right: 1px !important
  }

  [dir] .xl\:pt-base {
    padding-top: 2.2rem !important
  }

  [dir=ltr] .xl\:pr-base {
padding-right: 2.2rem !important
  }

  [dir=rtl] .xl\:pr-base {
    padding-left: 2.2rem !important
  }

  [dir] .xl\:pb-base {
    padding-bottom: 2.2rem !important
  }

  [dir=ltr] .xl\:pl-base {
padding-left: 2.2rem !important
  }

  [dir=rtl] .xl\:pl-base {
    padding-right: 2.2rem !important
  }

  .xl\:placeholder-transparent::-webkit-input-placeholder {
    color: transparent !important
  }

  .xl\:placeholder-transparent::-moz-placeholder {
    color: transparent !important
  }

  .xl\:placeholder-transparent:-ms-input-placeholder {
    color: transparent !important
  }

  .xl\:placeholder-transparent::-ms-input-placeholder {
    color: transparent !important
  }

  .xl\:placeholder-transparent::placeholder {
    color: transparent !important
  }

  .xl\:placeholder-black::-webkit-input-placeholder {
    color: #22292f !important
  }

  .xl\:placeholder-black::-moz-placeholder {
    color: #22292f !important
  }

  .xl\:placeholder-black:-ms-input-placeholder {
    color: #22292f !important
  }

  .xl\:placeholder-black::-ms-input-placeholder {
    color: #22292f !important
  }

  .xl\:placeholder-black::placeholder {
    color: #22292f !important
  }

  .xl\:placeholder-white::-webkit-input-placeholder {
    color: #ffffff !important
  }

  .xl\:placeholder-white::-moz-placeholder {
    color: #ffffff !important
  }

  .xl\:placeholder-white:-ms-input-placeholder {
    color: #ffffff !important
  }

  .xl\:placeholder-white::-ms-input-placeholder {
    color: #ffffff !important
  }

  .xl\:placeholder-white::placeholder {
    color: #ffffff !important
  }

  .xl\:placeholder-grey::-webkit-input-placeholder {
    color: #b8c2cc !important
  }

  .xl\:placeholder-grey::-moz-placeholder {
    color: #b8c2cc !important
  }

  .xl\:placeholder-grey:-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .xl\:placeholder-grey::-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .xl\:placeholder-grey::placeholder {
    color: #b8c2cc !important
  }

  .xl\:placeholder-grey-light::-webkit-input-placeholder {
    color: #dae1e7 !important
  }

  .xl\:placeholder-grey-light::-moz-placeholder {
    color: #dae1e7 !important
  }

  .xl\:placeholder-grey-light:-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .xl\:placeholder-grey-light::-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .xl\:placeholder-grey-light::placeholder {
    color: #dae1e7 !important
  }

  .xl\:focus\:placeholder-transparent:focus::-webkit-input-placeholder {
    color: transparent !important
  }

  .xl\:focus\:placeholder-transparent:focus::-moz-placeholder {
    color: transparent !important
  }

  .xl\:focus\:placeholder-transparent:focus:-ms-input-placeholder {
    color: transparent !important
  }

  .xl\:focus\:placeholder-transparent:focus::-ms-input-placeholder {
    color: transparent !important
  }

  .xl\:focus\:placeholder-transparent:focus::placeholder {
    color: transparent !important
  }

  .xl\:focus\:placeholder-black:focus::-webkit-input-placeholder {
    color: #22292f !important
  }

  .xl\:focus\:placeholder-black:focus::-moz-placeholder {
    color: #22292f !important
  }

  .xl\:focus\:placeholder-black:focus:-ms-input-placeholder {
    color: #22292f !important
  }

  .xl\:focus\:placeholder-black:focus::-ms-input-placeholder {
    color: #22292f !important
  }

  .xl\:focus\:placeholder-black:focus::placeholder {
    color: #22292f !important
  }

  .xl\:focus\:placeholder-white:focus::-webkit-input-placeholder {
    color: #ffffff !important
  }

  .xl\:focus\:placeholder-white:focus::-moz-placeholder {
    color: #ffffff !important
  }

  .xl\:focus\:placeholder-white:focus:-ms-input-placeholder {
    color: #ffffff !important
  }

  .xl\:focus\:placeholder-white:focus::-ms-input-placeholder {
    color: #ffffff !important
  }

  .xl\:focus\:placeholder-white:focus::placeholder {
    color: #ffffff !important
  }

  .xl\:focus\:placeholder-grey:focus::-webkit-input-placeholder {
    color: #b8c2cc !important
  }

  .xl\:focus\:placeholder-grey:focus::-moz-placeholder {
    color: #b8c2cc !important
  }

  .xl\:focus\:placeholder-grey:focus:-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .xl\:focus\:placeholder-grey:focus::-ms-input-placeholder {
    color: #b8c2cc !important
  }

  .xl\:focus\:placeholder-grey:focus::placeholder {
    color: #b8c2cc !important
  }

  .xl\:focus\:placeholder-grey-light:focus::-webkit-input-placeholder {
    color: #dae1e7 !important
  }

  .xl\:focus\:placeholder-grey-light:focus::-moz-placeholder {
    color: #dae1e7 !important
  }

  .xl\:focus\:placeholder-grey-light:focus:-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .xl\:focus\:placeholder-grey-light:focus::-ms-input-placeholder {
    color: #dae1e7 !important
  }

  .xl\:focus\:placeholder-grey-light:focus::placeholder {
    color: #dae1e7 !important
  }

  .xl\:pointer-events-none {
    pointer-events: none !important
  }

  .xl\:pointer-events-auto {
    pointer-events: auto !important
  }

  .xl\:static {
    position: static !important
  }

  .xl\:fixed {
    position: fixed !important
  }

  .xl\:absolute {
    position: absolute !important
  }

  .xl\:relative {
    position: relative !important
  }

  .xl\:sticky {
    position: -webkit-sticky !important;
    position: sticky !important
  }

  .xl\:inset-0 {
    top: 0 !important;
    bottom: 0 !important
  }

  [dir=ltr] .xl\:inset-0 {
right: 0 !important;
left: 0 !important
  }

  [dir=rtl] .xl\:inset-0 {
    left: 0 !important;
    right: 0 !important
  }

  .xl\:inset-auto {
    top: auto !important;
    bottom: auto !important
  }

  [dir=ltr] .xl\:inset-auto {
right: auto !important;
left: auto !important
  }

  [dir=rtl] .xl\:inset-auto {
    left: auto !important;
    right: auto !important
  }

  .xl\:inset-y-0 {
    top: 0 !important;
    bottom: 0 !important
  }

  [dir=ltr] .xl\:inset-x-0 {
right: 0 !important;
left: 0 !important
  }

  [dir=rtl] .xl\:inset-x-0 {
    left: 0 !important;
    right: 0 !important
  }

  .xl\:inset-y-auto {
    top: auto !important;
    bottom: auto !important
  }

  [dir=ltr] .xl\:inset-x-auto {
right: auto !important;
left: auto !important
  }

  [dir=rtl] .xl\:inset-x-auto {
    left: auto !important;
    right: auto !important
  }

  .xl\:top-0 {
    top: 0 !important
  }

  [dir=ltr] .xl\:right-0 {
right: 0 !important
  }

  [dir=rtl] .xl\:right-0 {
    left: 0 !important
  }

  .xl\:bottom-0 {
    bottom: 0 !important
  }

  [dir=ltr] .xl\:left-0 {
left: 0 !important
  }

  [dir=rtl] .xl\:left-0 {
    right: 0 !important
  }

  .xl\:top-auto {
    top: auto !important
  }

  [dir=ltr] .xl\:right-auto {
right: auto !important
  }

  [dir=rtl] .xl\:right-auto {
    left: auto !important
  }

  .xl\:bottom-auto {
    bottom: auto !important
  }

  [dir=ltr] .xl\:left-auto {
left: auto !important
  }

  [dir=rtl] .xl\:left-auto {
    right: auto !important
  }

  .xl\:resize-none {
    resize: none !important
  }

  .xl\:resize-y {
    resize: vertical !important
  }

  .xl\:resize-x {
    resize: horizontal !important
  }

  .xl\:resize {
    resize: both !important
  }

  [dir] .xl\:shadow {
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
  }

  [dir] .xl\:shadow-md {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .xl\:shadow-lg {
    box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .xl\:shadow-inner {
    box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
  }

  [dir] .xl\:shadow-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
  }

  [dir] .xl\:shadow-2xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
  }

  [dir] .xl\:shadow-outline {
    box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
  }

  [dir] .xl\:shadow-none {
    box-shadow: none !important
  }

  [dir] .xl\:shadow-drop {
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
  }

  [dir] .xl\:hover\:shadow:hover {
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
  }

  [dir] .xl\:hover\:shadow-md:hover {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .xl\:hover\:shadow-lg:hover {
    box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .xl\:hover\:shadow-inner:hover {
    box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
  }

  [dir] .xl\:hover\:shadow-xl:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
  }

  [dir] .xl\:hover\:shadow-2xl:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
  }

  [dir] .xl\:hover\:shadow-outline:hover {
    box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
  }

  [dir] .xl\:hover\:shadow-none:hover {
    box-shadow: none !important
  }

  [dir] .xl\:hover\:shadow-drop:hover {
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
  }

  [dir] .xl\:focus\:shadow:focus {
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10) !important
  }

  [dir] .xl\:focus\:shadow-md:focus {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.12), 0 2px 4px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .xl\:focus\:shadow-lg:focus {
    box-shadow: 0 15px 30px 0 rgba(0,0,0,0.11), 0 5px 15px 0 rgba(0,0,0,0.08) !important
  }

  [dir] .xl\:focus\:shadow-inner:focus {
    box-shadow: inset 0 2px 4px 0 rgba(0,0,0,0.06) !important
  }

  [dir] .xl\:focus\:shadow-xl:focus {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important
  }

  [dir] .xl\:focus\:shadow-2xl:focus {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important
  }

  [dir] .xl\:focus\:shadow-outline:focus {
    box-shadow: 0 0 0 3px rgba(52,144,220,0.5) !important
  }

  [dir] .xl\:focus\:shadow-none:focus {
    box-shadow: none !important
  }

  [dir] .xl\:focus\:shadow-drop:focus {
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.14) !important
  }

  .xl\:table-auto {
    table-layout: auto !important
  }

  .xl\:table-fixed {
    table-layout: fixed !important
  }

  [dir=ltr] .xl\:text-left {
text-align: left !important
  }

  [dir=rtl] .xl\:text-left {
    text-align: right !important
  }

  [dir] .xl\:text-center {
    text-align: center !important
  }

  [dir=ltr] .xl\:text-right {
text-align: right !important
  }

  [dir=rtl] .xl\:text-right {
    text-align: left !important
  }

  [dir] .xl\:text-justify {
    text-align: justify !important
  }

  .xl\:text-inherit {
    color: inherit !important
  }

  .xl\:text-transparent {
    color: transparent !important
  }

  .xl\:text-black {
    color: #22292f !important
  }

  .xl\:text-white {
    color: #ffffff !important
  }

  .xl\:text-grey {
    color: #b8c2cc !important
  }

  .xl\:text-grey-light {
    color: #dae1e7 !important
  }

  .xl\:hover\:text-inherit:hover {
    color: inherit !important
  }

  .xl\:hover\:text-transparent:hover {
    color: transparent !important
  }

  .xl\:hover\:text-black:hover {
    color: #22292f !important
  }

  .xl\:hover\:text-white:hover {
    color: #ffffff !important
  }

  .xl\:hover\:text-grey:hover {
    color: #b8c2cc !important
  }

  .xl\:hover\:text-grey-light:hover {
    color: #dae1e7 !important
  }

  .xl\:focus\:text-inherit:focus {
    color: inherit !important
  }

  .xl\:focus\:text-transparent:focus {
    color: transparent !important
  }

  .xl\:focus\:text-black:focus {
    color: #22292f !important
  }

  .xl\:focus\:text-white:focus {
    color: #ffffff !important
  }

  .xl\:focus\:text-grey:focus {
    color: #b8c2cc !important
  }

  .xl\:focus\:text-grey-light:focus {
    color: #dae1e7 !important
  }

  .xl\:text-xs {
    font-size: .75rem !important
  }

  .xl\:text-sm {
    font-size: .875rem !important
  }

  .xl\:text-base {
    font-size: 1rem !important
  }

  .xl\:text-lg {
    font-size: 1.125rem !important
  }

  .xl\:text-xl {
    font-size: 1.25rem !important
  }

  .xl\:text-2xl {
    font-size: 1.5rem !important
  }

  .xl\:text-3xl {
    font-size: 1.875rem !important
  }

  .xl\:text-4xl {
    font-size: 2.25rem !important
  }

  .xl\:text-5xl {
    font-size: 3rem !important
  }

  .xl\:text-6xl {
    font-size: 4rem !important
  }

  .xl\:italic {
    font-style: italic !important
  }

  .xl\:not-italic {
    font-style: normal !important
  }

  .xl\:hover\:italic:hover {
    font-style: italic !important
  }

  .xl\:hover\:not-italic:hover {
    font-style: normal !important
  }

  .xl\:focus\:italic:focus {
    font-style: italic !important
  }

  .xl\:focus\:not-italic:focus {
    font-style: normal !important
  }

  .xl\:uppercase {
    text-transform: uppercase !important
  }

  .xl\:lowercase {
    text-transform: lowercase !important
  }

  .xl\:capitalize {
    text-transform: capitalize !important
  }

  .xl\:normal-case {
    text-transform: none !important
  }

  .xl\:hover\:uppercase:hover {
    text-transform: uppercase !important
  }

  .xl\:hover\:lowercase:hover {
    text-transform: lowercase !important
  }

  .xl\:hover\:capitalize:hover {
    text-transform: capitalize !important
  }

  .xl\:hover\:normal-case:hover {
    text-transform: none !important
  }

  .xl\:focus\:uppercase:focus {
    text-transform: uppercase !important
  }

  .xl\:focus\:lowercase:focus {
    text-transform: lowercase !important
  }

  .xl\:focus\:capitalize:focus {
    text-transform: capitalize !important
  }

  .xl\:focus\:normal-case:focus {
    text-transform: none !important
  }

  .xl\:underline {
    text-decoration: underline !important
  }

  .xl\:line-through {
    text-decoration: line-through !important
  }

  .xl\:no-underline {
    text-decoration: none !important
  }

  .xl\:hover\:underline:hover {
    text-decoration: underline !important
  }

  .xl\:hover\:line-through:hover {
    text-decoration: line-through !important
  }

  .xl\:hover\:no-underline:hover {
    text-decoration: none !important
  }

  .xl\:focus\:underline:focus {
    text-decoration: underline !important
  }

  .xl\:focus\:line-through:focus {
    text-decoration: line-through !important
  }

  .xl\:focus\:no-underline:focus {
    text-decoration: none !important
  }

  .xl\:antialiased {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important
  }

  .xl\:subpixel-antialiased {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important
  }

  .xl\:hover\:antialiased:hover {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important
  }

  .xl\:hover\:subpixel-antialiased:hover {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important
  }

  .xl\:focus\:antialiased:focus {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important
  }

  .xl\:focus\:subpixel-antialiased:focus {
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important
  }

  .xl\:select-none {
    -webkit-user-select: none !important;
       -moz-user-select: none !important;
        -ms-user-select: none !important;
            user-select: none !important
  }

  .xl\:select-text {
    -webkit-user-select: text !important;
       -moz-user-select: text !important;
        -ms-user-select: text !important;
            user-select: text !important
  }

  .xl\:select-all {
    -webkit-user-select: all !important;
       -moz-user-select: all !important;
        -ms-user-select: all !important;
            user-select: all !important
  }

  .xl\:select-auto {
    -webkit-user-select: auto !important;
       -moz-user-select: auto !important;
        -ms-user-select: auto !important;
            user-select: auto !important
  }

  .xl\:align-baseline {
    vertical-align: baseline !important
  }

  .xl\:align-top {
    vertical-align: top !important
  }

  .xl\:align-middle {
    vertical-align: middle !important
  }

  .xl\:align-bottom {
    vertical-align: bottom !important
  }

  .xl\:align-text-top {
    vertical-align: text-top !important
  }

  .xl\:align-text-bottom {
    vertical-align: text-bottom !important
  }

  .xl\:visible {
    visibility: visible !important
  }

  .xl\:invisible {
    visibility: hidden !important
  }

  .xl\:whitespace-normal {
    white-space: normal !important
  }

  .xl\:whitespace-no-wrap {
    white-space: nowrap !important
  }

  .xl\:whitespace-pre {
    white-space: pre !important
  }

  .xl\:whitespace-pre-line {
    white-space: pre-line !important
  }

  .xl\:whitespace-pre-wrap {
    white-space: pre-wrap !important
  }

  .xl\:break-normal {
    overflow-wrap: normal !important;
    word-break: normal !important
  }

  .xl\:break-words {
    overflow-wrap: break-word !important
  }

  .xl\:break-all {
    word-break: break-all !important
  }

  .xl\:truncate {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important
  }

  .xl\:w-1 {
    width: 0.25rem !important
  }

  .xl\:w-2 {
    width: 0.5rem !important
  }

  .xl\:w-3 {
    width: 0.75rem !important
  }

  .xl\:w-4 {
    width: 1rem !important
  }

  .xl\:w-5 {
    width: 1.25rem !important
  }

  .xl\:w-6 {
    width: 1.5rem !important
  }

  .xl\:w-8 {
    width: 2rem !important
  }

  .xl\:w-10 {
    width: 2.5rem !important
  }

  .xl\:w-12 {
    width: 3rem !important
  }

  .xl\:w-16 {
    width: 4rem !important
  }

  .xl\:w-24 {
    width: 6rem !important
  }

  .xl\:w-32 {
    width: 8rem !important
  }

  .xl\:w-48 {
    width: 12rem !important
  }

  .xl\:w-64 {
    width: 16rem !important
  }

  .xl\:w-auto {
    width: auto !important
  }

  .xl\:w-px {
    width: 1px !important
  }

  .xl\:w-1\/2 {
    width: 50% !important
  }

  .xl\:w-1\/3 {
    width: 33.33333% !important
  }

  .xl\:w-2\/3 {
    width: 66.66667% !important
  }

  .xl\:w-1\/4 {
    width: 25% !important
  }

  .xl\:w-3\/4 {
    width: 75% !important
  }

  .xl\:w-1\/5 {
    width: 20% !important
  }

  .xl\:w-2\/5 {
    width: 40% !important
  }

  .xl\:w-3\/5 {
    width: 60% !important
  }

  .xl\:w-4\/5 {
    width: 80% !important
  }

  .xl\:w-1\/6 {
    width: 16.66667% !important
  }

  .xl\:w-5\/6 {
    width: 83.33333% !important
  }

  .xl\:w-1\/12 {
    width: 8.33333% !important
  }

  .xl\:w-2\/12 {
    width: 16.66667% !important
  }

  .xl\:w-3\/12 {
    width: 25% !important
  }

  .xl\:w-4\/12 {
    width: 33.33333% !important
  }

  .xl\:w-5\/12 {
    width: 41.66667% !important
  }

  .xl\:w-6\/12 {
    width: 50% !important
  }

  .xl\:w-7\/12 {
    width: 58.33333% !important
  }

  .xl\:w-8\/12 {
    width: 66.66667% !important
  }

  .xl\:w-9\/12 {
    width: 75% !important
  }

  .xl\:w-10\/12 {
    width: 83.33333% !important
  }

  .xl\:w-11\/12 {
    width: 91.66667% !important
  }

  .xl\:w-full {
    width: 100% !important
  }

  .xl\:w-screen {
    width: 100vw !important
  }

  .xl\:z-0 {
    z-index: 0 !important
  }

  .xl\:z-10 {
    z-index: 10 !important
  }

  .xl\:z-20 {
    z-index: 20 !important
  }

  .xl\:z-30 {
    z-index: 30 !important
  }

  .xl\:z-40 {
    z-index: 40 !important
  }

  .xl\:z-50 {
    z-index: 50 !important
  }

  .xl\:z-auto {
    z-index: auto !important
  }
}
