<template>
    <div class="vx-col w-full mb-base">
        <vx-card id="cs-perf-widget" title="Customer Service Performance" class="vs-con-loading__container">
            <div v-for="(crm, index) in crmPerformance" :key="index" :class="{'mt-4': index}">
                <div class="flex justify-between">
                    <div class="flex flex-col">
                        <vx-tooltip :text="'Total Cases: ' + crm.total + ' | Breach: ' + crm.breach" position="right">
                            <span class="mb-1">{{ crm.name }}</span>
                        </vx-tooltip>
                    </div>
                    <div class="flex flex-col text-right">
            <span class="flex -mr-1">
              <h5 class="mr-1">{{ crm.ratio }}%</h5>
              <feather-icon
                  :icon=" crm.ratio < 80 ? 'FrownIcon' : 'SmileIcon'"
                  :svgClasses="[crm.ratio < 80 ? 'text-danger' : 'text-success'  ,'stroke-current h-4 w-4 mb-1 mr-1']"
              ></feather-icon>
            </span>
                    </div>
                </div>
                <vs-progress :percent="crm.ratio" :color=" crm.ratio < 80 ? 'danger' : 'success'"></vs-progress>
            </div>
        </vx-card>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                crmPerformance: []
            };
        },
        components: {},

        mounted() {
            const vm = this;
            vm.fetchCrmPerformance();

            setInterval(() => {
                //vm.fetchCrmPerformance();
            }, 300000) // 5 min
        },

        methods: {
            fetchCrmPerformance: function () {
                let url = '/api/crm/cs-performance';
                this.$vs.loading({
                    container: '#cs-perf-widget',
                    scale: 0.6
                });
                fetch(url)
                    .then(res => res.json())
                    .then(res => {
                        this.crmPerformance = res.data;
                        this.$vs.loading.close('#cs-perf-widget > .con-vs-loading');
                    })
                    .catch(err => console.log(err));

            }
        }
    };
</script>
