<?php

/*
 * Function : Insert data from table in db crm into table sla_itspec in db poms
 * Date : 4 Oct 2019
 * Author : Nur Asmaa
 */

namespace App\crm\sla;

use Carbon\Carbon;
use Log;
use DB;
use App\Nagios\MigrateUtils;
use DateInterval;
use DatePeriod;

class insertITSpecSlaDataAdhoc
{

    public static function runAdhocInsertSpecialist($begin, $end)
    {
        // insert specific date
        dump('Process by specified date...');

        $interval = DateInterval::createFromDateString('1 day');
        $period = new DatePeriod($begin, $interval, $end);

        foreach ($period as $dtperiod) {
            self::insertITSpecSlaData($dtperiod);
        }
    }

    /* Read data from Database crm copy into database cdc_poms */

    private static function insertITSpecSlaData($datePeriod = null)
    {

        $dateStart = Carbon::now();

        Log::info(self::class . ' Start : ' . __FUNCTION__ . ' ->> Date Start: ' . $dateStart);
        $dtStartTime = Carbon::now();

        $processDate = null;

        if ($datePeriod) {
            $processDate = $datePeriod->format('Y-m-d');
        } else {
            $yesterdayData = Carbon::yesterday();
            $getCurr = strtotime($yesterdayData);
            $processDate = date('Y-m-d', $getCurr);
        }

        dump("Inserted data for date... " . $processDate);

        try {

            dump('querying data... ');
            $queryStart = microtime(true);

            $result = "select distinct `c`.`case_number` AS `case_number`,
                        CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00') AS `itspec_case_created`,
                        `c`.`name` AS `itspec_name`,
                        `itspec_tc`.`task_number_c` AS `itspec_task_number`,
                        `itspec_tc`.`sla_task_flag_c` AS `itspec_sla_flag`,
                        convert_tz(`itspec_t`.`date_start`,'+00:00','+08:00') AS `itspec_start_datetime`,
                        convert_tz(`itspec_t`.`date_due`,'+00:00','+08:00') AS `itspec_due_datetime`,
                        convert_tz(`itspec_tc`.`sla_start_4hr_c`,'+00:00','+08:00') AS `itspec_actual_start_datetime`,
                        convert_tz(`itspec_tc`.`sla_stop_4hr_c`,'+00:00','+08:00') AS `itspec_completed_datetime`,
                        timestampdiff(SECOND,convert_tz(`itspec_t`.`date_start`,'+00:00','+08:00'),convert_tz(`itspec_t`.`date_due`,'+00:00','+08:00')) AS `itspec_available_duration`,
                        timestampdiff(SECOND,convert_tz(`itspec_tc`.`sla_start_4hr_c`,'+00:00','+08:00'),convert_tz(`itspec_tc`.`sla_stop_4hr_c`,'+00:00','+08:00')) AS `itspec_actual_duration`
                        from ((((`cases` `c`
                        join `cases_cstm` `cc` on((`c`.`id` = `cc`.`id_c`)))
                        left join `tasks` `itspec_t` on((`c`.`id` = `itspec_t`.`parent_id`)))
                        left join `tasks_cstm` `itspec_tc` on(((`itspec_t`.`id` = `itspec_tc`.`id_c`)
                        and (`itspec_tc`.`sla_task_flag_c` = '3'))))
                        left join `cstm_list_app` `subcat` on(((`cc`.`sub_category_c` = `subcat`.`value_code`)
                        and (`subcat`.`type_code` = 'cdc_sub_category_list')
                        and (trim(`subcat`.`value_code`) <> '')
                        and (`subcat`.`value_code` not in ('10712_15034','10714_15842','10713_15534')))))
                        where ((`cc`.`incident_service_type_c` = 'incident_it')
                        and (`cc`.`request_type_c` = 'incident')
                        and (itspec_t.`name` IN ('Assigned to Production Support','Assigned to Group IT Specialist(Production Support)'))
                        and (`c`.`deleted` = 0)
                        and (`itspec_t`.`deleted` = 0)
                        AND (`c`.`status` IN ('Pending_User_Verification', 'Open_Resolved', 'Closed_Approved', 'Closed_Cancelled_Eaduan', 'Closed_Closed', 'Closed_Rejected', 'Closed_Rejected_Eaduan', 'Closed_Verified_Eaduan'))
                        AND (`itspec_t`.`status` = 'Completed')
                        AND (`c`.`date_entered` >= '2019-09-01')
                        AND ( itspec_t.`assigned_user_id` <> '15c7bd74-12f3-311b-9e8f-5a810702a1a5')
                        and (`itspec_tc`.`task_number_c` is not null)
                        AND (STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') <= ?)
                        and (trim(`subcat`.`value_code`) <> ''))
                        order by `c`.`case_number` desc";

            $results = DB::connection('mysql_crm')->select($result, array($processDate));
            $total = count($results);

            $time_elapsed_secs = microtime(true) - $queryStart;
            dump('query execution time: ' . $time_elapsed_secs . ' | total records: ' . $total);

            if (is_array($results) || is_object($results)) {
                foreach ($results as $data) {
                    $count = DB::connection('mysql')->table('sla_itspec_temp')
                        ->where('itspec_task_number', $data->itspec_task_number)
                        ->count();

                    $insertedTaskNum = DB::connection('mysql_crm')->table('cases')
                        ->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                        ->join('tasks', 'tasks.parent_id', '=', 'cases_cstm.id_c')
                        ->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
                        ->where('tasks_cstm.task_number_c', $data->itspec_task_number)
                        ->where('cases_cstm.incident_service_type_c', 'incident_it')
                        ->where('tasks_cstm.sla_task_flag_c', 3)
                        ->where('tasks_cstm.is_sent', 1)
                        ->count();

                    //bypass inserted task checking
                    $insertedTaskNum = 0;

                    if ($count == 0 && $insertedTaskNum == 0) {
                        dump('case no: ' . $data->case_number . ' - insert');

                        $insertDataDate = Carbon::now();

                        DB::connection('mysql')
                            ->insert('insert into sla_itspec_temp
                            (case_number, itspec_case_created, itspec_name, itspec_task_number, itspec_sla_flag, itspec_start_datetime, itspec_due_datetime, itspec_actual_start_datetime, itspec_completed_datetime, itspec_available_duration, itspec_actual_duration, itcoord_insert_data_datetime)
                            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                                $data->case_number,
                                $data->itspec_case_created,
                                $data->itspec_name,
                                $data->itspec_task_number,
                                $data->itspec_sla_flag,
                                $data->itspec_start_datetime,
                                $data->itspec_due_datetime,
                                $data->itspec_actual_start_datetime,
                                $data->itspec_completed_datetime,
                                $data->itspec_available_duration,
                                $data->itspec_actual_duration,
                                $insertDataDate
                            ]);

                        // DB::connection('mysql_crm')
                        //         ->table('cases')
                        //         ->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
                        //         ->join('tasks', 'tasks.parent_id', '=', 'cases_cstm.id_c')
                        //         ->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
                        //         ->where('tasks_cstm.task_number_c', $data->itspec_task_number)
                        //         ->where('cases_cstm.incident_service_type_c', 'incident_it')
                        //         ->where ('tasks_cstm.sla_task_flag_c', 3)
                        //         ->update([
                        //             'tasks_cstm.is_sent' => 1,
                        //             'tasks_cstm.poms_inserted_date' => $insertDataDate
                        // ]);

                        $logsdata = self::class . ' Successfully insert data for SLA IT Specialist => Date Start : ' . $dtStartTime . ' -> '
                            . 'Query Date End : ' . Carbon::now() . ' Task Number : ' . $data->itspec_task_number . ' , Completed --- Taken Time : ' .
                            json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

                        Log::info($logsdata);
                        dump($logsdata);
                    }
                    dump('case no: ' . $data->case_number . ' - skip');
                }
                $total_time = microtime(true) - $queryStart;
                $note = ' Successfully insert data for SLA IT Specialist (4 Hour) => Total : ' . $total . ' | TIme taken: ' . $total_time;

                dump('DONE! ' . $note);
            }

            return $results;
        } catch (\Exception $exc) {
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            echo $exc->getTraceAsString();
        }
        return null;
    }
}
