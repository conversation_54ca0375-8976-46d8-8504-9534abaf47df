<template>
    <!-- LINE CHART -->
    <div class="vx-col w-full mb-base">
        <vx-card title="ePerolehan User Login">
            <template slot="no-body">
                <div class="pb-0">
                    <vue-apex-charts type=area height=266 :options="lineAreaChartSpline.chartOptions" :series="lineAreaChartSpline.series" />
                    <!-- DATA -->
                </div>
            </template>
            <div class="flex justify-between text-center" slot="no-body-bottom">
                <div class="w-1/3 border border-solid d-theme-border-grey-light border-r-0 border-b-0 border-l-0">
                    <p class="mt-4">PTJ</p>
                    <p class="mb-4 text-3xl font-semibold text-primary">450</p>
                </div>
                <div class="w-1/3 border border-solid d-theme-border-grey-light border-r-0 border-b-0">
                    <p class="mt-4">Supplier</p>
                    <p class="mb-4 text-3xl font-semibold text-success">855</p>
                </div>
                <div class="w-1/3 border border-solid d-theme-border-grey-light border-r-0 border-b-0">
                    <p class="mt-4">Total</p>
                    <p class="mb-4 text-3xl font-semibold">1305</p>
                </div>
            </div>
        </vx-card>
    </div>
</template>

<script>
    import VueApexCharts from 'vue-apexcharts'

    const themeColors = ['#7367F0', '#28C76F', '#EA5455', '#FF9F43', '#1E1E1E'];

    export default {
        data() {
            return {
                lineAreaChartSpline: {
                    series: [{
                        name: 'PTJ',
                        data: []
                    }, {
                        name: 'Supplier',
                        data: []
                    }],
                    chartOptions: {
                        dataLabels: {
                            enabled: false
                        },
                        stroke: {
                            curve: 'smooth'
                        },
                        colors: themeColors,
                        xaxis: {
                            type: 'datetime',
                            categories: []
                        },
                        tooltip: {
                            x: {
                                format: 'dd/MM/yy HH:mm'
                            },

                        }
                    }
                },
            }
        },
        created() {
            this.getUserLogin()
        },
        components: {
            VueApexCharts
        },
        methods: {
            getUserLogin: function(){
                let url = '/api/epss/login-stats';
                fetch(url)
                    .then(res => res.json())
                    .then(res => {
                        let supplierHourlyLoginData = res.dataSupplierHourlyStat;
                        let ptjHourlyLoginData = res.dataPTJHourlyStat;
                        let dataTimeStat = res.dataTimeStat;

                        this.lineAreaChartSpline.chartOptions.xaxis.categories = dataTimeStat;
                        this.lineAreaChartSpline.series[0]['data'] = ptjHourlyLoginData;
                        this.lineAreaChartSpline.series[1]['data'] = supplierHourlyLoginData;

                        // console.log("categories: " + this.lineAreaChartSpline.chartOptions.xaxis.categories);
                        //this.lineAreaChartSpline.chartOptions.xaxis.categories = dataTimeStat;

                    })
                    .catch(err => console.log(err));
            }
        }

    }
</script>

