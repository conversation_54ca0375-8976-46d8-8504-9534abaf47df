<template>
    <div id="extra-component-chartjs-demo">
        <div class="vx-row" style="display: flex;">
            <vs-tabs alignment="fixed">
                <vs-tab label="Codification" icon-pack="feather" icon="icon-code">
                    <div class="vx-col w-full md:w-1/1" style="flex: 1;">
                        <codification-status></codification-status>
                    </div>
                </vs-tab>
                <vs-tab label="Quotation Tender Process Status" icon-pack="feather" icon="icon-layers">
                    <div id="tab_2" class="vx-col w-full md:w-1/1" style="flex: 1;">
                        <quotation-tender-monitoring></quotation-tender-monitoring>
                    </div>
                </vs-tab>
                <vs-tab label="Fulfilment Process Status" icon-pack="feather" icon="icon-file-text">
                    <div class="vx-col w-full md:w-1/1" style="flex: 1;">
                        <fulfilment-process-status></fulfilment-process-status>
                    </div>
                </vs-tab>
                <vs-tab label="Bidding Activity" icon-pack="feather" icon="icon-loader">
                    <div class="vx-col w-full md:w-1/1" style="flex: 1;">
                        <bidding-activity></bidding-activity>
                    </div>
                </vs-tab>
            </vs-tabs>
        </div>
    </div>
</template>

<script>
import CodificationStatus from './CodificationStatus.vue'
import BiddingActivity from './BiddingActivityStatus.vue'
import QuotationTenderMonitoring from './QuotationTenderMonitoring.vue'
import FulfilmentProcessStatus from './FulfilmentProcessStatus.vue'
export default {
    components: {
        CodificationStatus,
        BiddingActivity,
        QuotationTenderMonitoring,
        FulfilmentProcessStatus,
    },
    methods: {
        loadSpinner(){
            this.$vs.loading({
                container: "#tab_2",
                scale: 0.6,
            });
        },
    },
    created: function () {
        // this.loadSpinner();
    }
}
</script>