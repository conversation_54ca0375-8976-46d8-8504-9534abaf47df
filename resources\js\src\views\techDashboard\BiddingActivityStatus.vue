<template>
    <div id="bid-container" class="vs-con-loading__container">
        <div class="vx-row">
            <div class="vx-col w-1/2 md:w-1/2 xl:w-1/2">
                <statistics-card-line
                  hideChart
                  class="mb-base"
                  icon="BookOpenIcon"
                  :statistic="totalTodayBid"
                  statisticTitle="Total Today Bidding"
                  color='primary' />
            </div>
            <div class="vx-col w-1/2 md:w-1/2 xl:w-1/2">
                <statistics-card-line
                  hideChart
                  class="mb-base"
                  icon="ArchiveIcon"
                  :statistic="totalMonthClosedBid"
                  statisticTitle="Total Closed Bidding (Month to Date)"
                  color='success' />
            </div>
        </div>
        <div class="vx-row">
            <div class="vx-col w-1/2 md:w-1/2 xl:w-1/2">
                <statistics-card-line
                  hideChart
                  class="mb-base"
                  icon="ClockIcon"
                  :statistic="totalTomorrowBid"
                  statisticTitle="Total Tomorrow Bidding"
                  color='warning' />
            </div>
            <div class="vx-col w-1/2 md:w-1/2 xl:w-1/2">
                <statistics-card-line
                  hideChart
                  class="mb-base"
                  icon="XCircleIcon"
                  :statistic="totalMonthNoBidder"
                  statisticTitle="Total Bidding with No Bidder (Month to Date)"
                  color='danger' />
            </div>
        </div>
    </div>
</template>

<script>
import StatisticsCardLine from '@/components/statistics-cards/StatisticsCardLine.vue'
import axios from "axios";

export default{
    data() {
        return {
            totalTodayBid: 0,
            totalTomorrowBid: 0,
            totalMonthClosedBid: 0,
            totalMonthNoBidder: 0
        }
    },
    components: {
        StatisticsCardLine
    },
    methods: {
        getBidMonitoringData: function () {
            const url = "/api/kpi/bid/monitoring"

            this.$vs.loading({
                container: '#bid-container',
                scale: 0.6
            });
            
            axios.get(url).then(response => {
                let data = response.data
                this.totalTodayBid = data.total_today
                this.totalTomorrowBid = data.total_tomorrow
                this.totalMonthClosedBid = data.total_month
                this.totalMonthNoBidder = data.total_no_bid

                this.$vs.loading.close('#bid-container > .con-vs-loading');
            })
            .catch(error => {
                err => console.log(error)
                this.$vs.loading.close('#bid-container > .con-vs-loading');
            })
        }
    },
    mounted: function () {
        this.getBidMonitoringData();
    },
}
</script>