<?php

/*
 * Function : Insert data from table in db crm into table sla_byapprover in db poms
 * Date : 22 Oct 2019
 * Author : Nur Asmaa
 */

namespace App\crm\sla;

use Carbon\Carbon;
use DateTime;
use App\Nagios\MigrateUtils;
use App\BatchMonitoringStatistic;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class insertITServiceRequestData
{

    public static function runCheckingSlaITServiceRequestData($dateStart = null, $dateEnd = null)
    {
        Log::debug(self::class . ' Starting ... Checking for SLA IT Service Request Data', ['Query Start Date' => $dateStart, 'Query End Date' => $dateEnd]);
        self::insertITServiceRequestSlaData($dateStart, $dateEnd);
    }

    /* Read data from HELPDESK db and insert into POMS */
    private static function insertITServiceRequestSlaData($dateStart, $dateEnd)
    {

        Log::info(self::class . ' Start : ' . __FUNCTION__ . '     ->> Date Start: ' . $dateStart . ', Date End: ' . $dateEnd);
        var_dump('Date Start: ' . $dateStart);
        var_dump('Date End: ' . $dateEnd);
        $dtStartTime = Carbon::now();
        $yesterdayData = Carbon::yesterday();
        $getCurr = strtotime($yesterdayData);
        $processDate = date('Y-m-d', $getCurr);

        try {

            $result = 'SELECT 
            t.ticket_id,
            ht.topic AS category,
            CONCAT(\'#\',t.number) AS ticket_number,
            t.created AS created_date,
            ts.name AS status,
            t.dept_id,
            s.name AS sla_plan,
            u.name AS requested_by,
            ue.address AS requested_email,
            d.name AS department,
            t.source AS source,
            t.est_duedate AS due_date,
            t.closed AS closed,
            (SELECT  
            CONCAT(
                   \'{\',
                   GROUP_CONCAT(
                       CONCAT(
                           \'"\', ff.label, \'":"\', ev.value , \'"\' 
                       )
                   ),
                   \'}\'
                ) AS form_data
            FROM ost_form_entry_values ev
            LEFT JOIN ost_form_field ff ON ff.id = ev.field_id
            WHERE 
            ev.entry_id IN (SELECT id FROM ost_form_entry WHERE object_id = t.ticket_id)) AS form_data
            FROM ost_ticket t 
            LEFT JOIN ost_help_topic ht ON ht.topic_id = t.topic_id
            LEFT JOIN ost_ticket_status ts ON ts.id = t.status_id
            LEFT JOIN ost_user u ON u.id = t.user_id
            LEFT JOIN ost_user_email ue ON ue.user_id = u.id
            LEFT JOIN ost_sla s ON s.id = t.sla_id
            LEFT JOIN ost_department d ON d.id = t.dept_id
            WHERE t.topic_id = 66
            AND ts.name IN (\'Request Completed\', \'Closed\')
            AND YEAR(t.created) >= 2023';

            $results = DB::connection('mysql_helpdesk')->select($result, array($processDate));

            if (is_array($results) || is_object($results)) {
                $counter = 0;

                foreach ($results as $data) {
                    $formData = json_decode($data->form_data);

                    $isExist = DB::connection('mysql')->table('sla_itservice_request')
                        ->where('ticket_number', $data->ticket_number)
                        ->count();

                    if ($isExist === 0) {
                        $expectedStart = new DateTime($formData->{'Expected Start Time/Date'});
                        $expectedComplete = new DateTime($formData->{'Expected Completion Time/Date'});
                        $expectedDuration = floor(($expectedComplete->getTimestamp() - $expectedStart->getTimestamp()) / 3600);

                        $actualStart = $expectedStart;
                        $actualCompleted = new DateTime($data->closed);
                        $actualDuration = floor(($actualCompleted->getTimestamp() - $actualStart->getTimestamp()) / 3600);

                        $insertDataDate = Carbon::now();

                        DB::connection('mysql')->table('sla_itservice_request')->insert([
                            'ticket_number' => $data->ticket_number,
                            'category' => $data->category,
                            'created_date' => new DateTime($data->created_date),
                            'status' => $data->status,
                            'department' => $data->department,
                            'request_subject' => $formData->{'Request Subject'},
                            'reason_for_request' => strip_tags($formData->{'Reason for Request'}),
                            'expected_start_date' => $expectedStart,
                            'expected_completed_date' => $expectedComplete,
                            'expected_duration' => $expectedDuration,
                            'actual_start_date' => $actualStart,
                            'actual_completed_date' => $actualCompleted,
                            'actual_duration' => $actualDuration,
                            'inserted_data_date' => $insertDataDate,
                        ]);

                        $counter++;
                        if ($counter == 20) {
                            sleep(1);
                            $counter = 0;
                        }

                        $logsdata = self::class . ' Successfully insert data for SLA IT Service Request => Date Start : ' . $dtStartTime . ' -> '
                            . 'Query Date End : ' . Carbon::now() . ' Ticket Number : ' . $data->ticket_number . ' , Completed --- Taken Time : ' .
                            json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

                        Log::info($logsdata);
                        dump($logsdata);
                    }
                }

                $total = count($results);
                $note = ' Successfully insert data for SLA IT Service Request => Total : ' . $total;
                $projName = 'POMS Integration';
                $batchName = 'insertITServiceRequestSlaData';
                $remarks = $note;
                $dateModified = Carbon::now();
                $status = 'Success';
                $list = BatchMonitoringStatistic::getBatchName($batchName);
                $dataList = BatchMonitoringStatistic::where('batch_name', 'insertITServiceRequestSlaData')
                    ->first();
                if (count($list) > 0) {
                    BatchMonitoringStatistic::updateBatchMonitoring($dataList, $status, $remarks, $dateModified);
                } else {
                    BatchMonitoringStatistic::createBatchMonitoring($projName, $batchName, $status, $remarks, $dateModified);
                }
            }
            return $results;
        } catch (\Exception $exc) {
            Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $projName = 'POMS Integration';
            $batchName = 'insertITServiceRequestSlaData';
            $remarks = $exc->getMessage();
            $dateModified = Carbon::now();
            $status = 'Failed';
            $list = BatchMonitoringStatistic::getBatchName($batchName);
            $dataList = BatchMonitoringStatistic::where('batch_name', 'insertITServiceRequestSlaData')
                ->first();
            if (count($list) > 0) {
                BatchMonitoringStatistic::updateBatchMonitoring($dataList, $status, $remarks, $dateModified);
            } else {
                BatchMonitoringStatistic::createBatchMonitoring($projName, $batchName, $status, $remarks, $dateModified);
            }
            echo $exc->getTraceAsString();
        }
        return null;
    }
}
