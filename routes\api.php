<?php

use App\Http\Controllers\CrmSlaController;
use App\Http\Controllers\EmailInboundController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

Route::prefix('auth')->group(function () {
    Route::post('register', 'AuthController@register')->middleware('cors');
    Route::post('login', 'AuthController@login')->middleware('cors');
    Route::get('refresh', 'AuthController@refresh')->middleware('cors');
    Route::group(['middleware' => 'auth:api'], function () {
        Route::get('user', 'AuthController@user');
        Route::post('logout', 'AuthController@logout');
    });
});

Route::group(['middleware' => 'auth:api'], function () {
    Route::get('users', 'UserController@userList')->middleware('isAdmin');
    Route::get('users/{id}', 'UserController@show')->middleware('isAdminOrSelf');
    Route::post('user/new', 'UserController@newUser')->middleware('isAdmin');
    Route::post('user/update', 'UserController@update')->middleware('isAdmin');
    Route::post('user/update-pass', 'UserController@updatePassword')->middleware('isAdmin');
    Route::post('user/reset-password', 'UserController@resetPassword');
    Route::post('user/edit-avatar', 'UserController@editAvatar');
});

//Route::post('login', 'AuthController@login');

/* START API DEMO */

// List articles
Route::get('articles', 'ArticleController@index');

// List single article
Route::get('article/{id}', 'ArticleController@show');

// Create new article
Route::post('article', 'ArticleController@store');

// Update article
Route::put('article', 'ArticleController@store');

// Delete article
Route::delete('article/{id}', 'ArticleController@destroy');

/* END API DEMO */

// index nagios
Route::get('nagios/hosts', 'NagiosController@index');
Route::get('nagios/scrape', 'NagiosController@scrapeNagios');
Route::get('nagios/hostlist', 'NagiosController@nagiosHostStatusApi');

// get login stats
Route::post('epss/login-stats', 'EpSupportController@getEpLoginStats');

// get login stats
// Route::get('crm/incident-stats', 'CrmController@getIncidentStats');
Route::get('crm/incident-stats', 'CrmSlaController@dashboardCrmSlaCount');
Route::get('crm/incident-stats-list/{status}', 'CrmSlaController@dashboardCrmSlaList');
Route::get('crm/incident-dashboard', 'CrmSlaController@dashboardCrmCount');
Route::post('crm/incident-list', 'CrmSlaController@getTaskList');

// get login stats
Route::get('callcenter/dashboard', 'CallCenterSlaController@getTalkdeskDashboardData');

// get Parameter lists - all
Route::get('parameter', 'ParameterController@getParameterList');

// Update parameter
Route::put('parameter', 'ParameterController@store');

// get Parameter lists - by page
Route::get('parameter/{page}', 'ParameterController@getParameterPageList');

// get cs sla performance
Route::get('crm/cs-performance', 'CrmController@csPerformanceDashboard');

// get cs sla performance
Route::get('crm/list-it-coordinator', 'CrmController@getITCoordinatorCasesList');
Route::get('crm/list-it-specialist', 'CrmController@getITSpecialistCasesList');

Route::get('ep/performance', 'PomsController@fetchEPPerformanceData');

Route::get('crm/slareportperformance/{cYear}/{cMonth}', 'SLAReportController@getSLAReportData');

// Store generated SLA report record
Route::get('report/list', 'ReportController@index');
Route::get('report/list-admin', 'ReportController@indexAdmin');
Route::post('report/store', 'ReportController@store');
Route::post('report/addtoqueue', 'ReportController@addToQueue');
Route::post('report/delete/{id}', 'ReportController@delete');
Route::get('report/log/{id}', 'ReportController@getLog');
Route::post('report/download', 'ReportController@download')->where('filename', '[A-Za-z0-9\-\_\.]+');

// Store generated Inventory Report record
Route::get('list/inventory/list', 'InventoryReportController@index');
Route::post('list/inventory/store', 'InventoryReportController@store');
Route::get('list/inventory/generate', 'InventoryReportController@export');
Route::get('list/inventory/getCategoryProducts', 'InventoryReportController@getCategoryProducts');
Route::get('list/inventory/getProductModels', 'InventoryReportController@getProductModels');
Route::get('list/inventory/download/{filename}', function($filename)
{
    // Check if file exists in app/storage/file folder
    $file_path = storage_path() .'/app/Export/'. $filename ;
    // dd($file_path);
    if (file_exists($file_path))
    {
        // Send Download
        return Response::download($file_path, $filename, [
            'Content-Length: '. filesize($file_path)
        ]);
    }
    else
    {
        // Error
        exit('Requested file does not exist on our server!');
    }
})
->where('filename', '[A-Za-z0-9\-\_\.]+');

/* KPI PERFORMANCE API */
Route::get('kpi/codification/today', 'epKpiProcessController@getCodificationData');
Route::get('kpi/qt/monitoring', 'epKpiProcessController@getQtData');
Route::get('kpi/fl/monitoring', 'epKpiProcessController@getFulfilmentData');
Route::get('kpi/fl/monitoring-month', 'epKpiProcessController@getFulfilmentMonthlyData');
Route::get('kpi/bid/monitoring', 'epKpiProcessController@getBiddingMonitoringData');
