<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class CheckIsAdmin
{
    public function handle($request, Closure $next)
    {
        // dd(Auth::user()->role);
        // var_dump(Auth::user()->role);
        $roles = json_decode(Auth::user()->role);
        // dd($roles);
        foreach($roles as $key => $value) {
            if($value === '1') {
                return $next($request);
            }
        }

        return response()->json(['error' => 'Unauthorized'], 403);

        // if (Auth::user()->role === 2) {
        //     return $next($request);
        // } else {
        //     return response()->json(['error' => 'Unauthorized'], 403);
        // }
    }
}
