# POMS (Performance Operations Management System)

[![License](https://img.shields.io/badge/License-MIT-blue.svg)](https://opensource.org/licenses/MIT)
[![PHP Version](https://img.shields.io/badge/PHP-7.4%2B-8892BF.svg)](https://php.net/)
[![Laravel Version](https://img.shields.io/badge/Laravel-8.x-FF2D20.svg)](https://laravel.com/)
[![Vue.js](https://img.shields.io/badge/Vue.js-3.x-4FC08D.svg)](https://vuejs.org/)

A comprehensive Performance Operations Management System built with Laravel and Vue.js, featuring JWT authentication and a modern, responsive interface.

## Table of Contents

- [Features](#features)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [Development](#development)
- [Running the Application](#running-the-application)
- [License](#license)

## Features

- SLA Dashboard
- SLA report generation
- Role-based access control
- Responsive design
- Real-time updates

## Prerequisites

Before you begin, ensure you have the following installed on your system:

- PHP 7.4
- Composer (PHP package manager)
- Node.js 12.x (includes npm)
- MySQL 5.7 or higher
- Git

## Installation

### 1. Clone the Repository

```bash
git clone [your-repository-url].git
cd poms
```

### 2. Install Backend Dependencies

```bash
composer install
```

### 3. Install Frontend Dependencies

```bash
npm install
```

### 4. Environment Setup

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Generate application key:
   ```bash
   php artisan key:generate
   ```

3. Generate JWT secret key:
   ```bash
   php artisan jwt:secret
   ```

4. Configure your `.env` file with your database credentials and other settings:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=poms
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   
   APP_URL=http://localhost:8000
   ```

### 5. Database Setup

Run database migrations and seed the database with initial data:

```bash
php artisan migrate --seed
```

### 6. Build Frontend Assets

For development:
```bash
npm run dev
```

Or for automatic rebuilding during development:
```bash
npm run watch
```

## Configuration

### JWT Configuration

To modify the JWT token expiry time (in minutes), update the following in your `.env` file:

```env
JWT_TTL=1440  # Default: 24 hours (in minutes)
```

### Application URL

Make sure to set the correct `APP_URL` in your `.env` file to match your application's URL, especially if you're using virtual hosts or a custom domain.

## Running the Application

Start the development server:

```bash
php artisan serve
```

The application will be available at `http://127.0.0.1:8000` by default.

To specify a custom port:

```bash
php artisan serve --port=8080
```

## Development

### Frontend Development

For frontend development, you can use the following commands:

- `npm run dev` - Compile assets once
- `npm run watch` - Watch for changes and auto-compile
- `npm run production` - Compile for production

### Backend Development

- Follow PSR-4 autoloading standards
- Write tests for new features
- Document your code with PHPDoc

## License

This project is open-source software licensed under the [MIT license](https://opensource.org/licenses/MIT).