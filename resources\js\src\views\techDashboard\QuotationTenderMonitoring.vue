<template>
<div>
    <div class="vx-row">
        <div class="vx-col w-full sm:w-1/1 lg:w-1/1 mb-base">
            <vx-card id="top-stats" title="Quotation Tender Monitoring (Today)" subtitle="(Valid before 12PM)" class="vs-con-loading__container">
                <!-- <img :src="card_2.img" alt="content-img" class="responsive rounded-lg"> -->
                <div class="vx-row">
                    <div class="vx-col w-full sm:w-1/2 lg:w-1/2">
                        <div class="text-center">
                            <vs-avatar color="primary" size="large" icon-pack="feather" icon="icon-thumbs-up" />
                            <h5 class="mb-2"><span style="color: green;" class="mb-4 text-3xl font-semibold">{{totalPublished}}</span>/{{totalAccumulatedPublished}}</h5>
                            <p class="text-grey">Published</p>
                        </div>
                    </div>
                    <div class="vx-col w-full sm:w-1/2 lg:w-1/2">
                        <div class="text-center">
                            <vs-avatar color="primary" size="large" icon-pack="feather" icon="icon-book" />
                            <h5 class="mb-2"><span style="color: green;" class="mb-4 text-3xl font-semibold">{{totalClosing}}</span>/{{totalAccumulatedClosing}}</h5>
                            <p class="text-grey">Closing</p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between text-center mt-4" slot="no-body-bottom">

                    <div class="w-1/2 border border-solid d-theme-border-grey-light border-r-0 border-b-0 border-l-0">
                        <p class="mt-4">Pending Publish</p>
                        <p class="mb-4 text-3xl font-semibold">{{totalPendingPublish}}</p>
                    </div>
                    <div class="w-1/2 border border-solid d-theme-border-grey-light border-r-0 border-b-0">
                        <p class="mt-4">Pending Closing</p>
                        <p class="mb-4 text-3xl font-semibold">{{totalPendingClosing}}</p>
                    </div>
                </div>
            </vx-card>
        </div>
    </div>
    <div class="vx-row">
        <div class="vx-col w-full sm:w-1/1 lg:w-1/1 mb-base">
        <vx-card id="bottom-stats" title="Quotation / Tender By Status" subtitle="(Month to Date)" class="vs-con-loading__container">
            <div class="vx-row flex-col-reverse lg:flex-row">

                <!-- LEFT COL -->
                <div class="vx-col w-full lg:w-1/2 xl:w-1/2 flex flex-col justify-between" v-if="salesBarSession.analyticsData">
                    <div v-if="codificationRequestByStatusToday.series">
                        <!-- CHART -->
                        <div slot="no-body">
                            <vue-apex-charts type="donut" height="340" class="mb-12 mt-4" :options="codificationRequestByStatusToday.chartOptions" :series="codificationRequestByStatusToday.series" />
                        </div>
                    </div>
                    <!-- <vs-button icon-pack="feather" icon="icon-chevrons-right" icon-after class="shadow-md w-full lg:mt-0 mt-4">View Details</vs-button> -->
                </div>

                <!-- RIGHT COL -->
                <div class="vx-col w-full lg:w-1/2 xl:w-1/2 flex flex-col lg:mb-0 mb-base">
                    <div class="flex justify-between text-center mt-4" slot="no-body-bottom" v-if="totalMonthlyPublished">
                            <div class="w-1/2">
                                <p class="mb-1 font-semibold">Total Published QT (Month to Date)</p>
                                <p class="text-3xl text-success">{{ totalMonthlyPublished.toLocaleString() }}</p>
                            </div>
                            <div class="w-1/2 border border-solid d-theme-border-grey-light border-r-0 border-b-0 border-t-0 ">
                                <p class="mb-1 font-semibold">Total Closed QT (Month to Date)</p>
                                <p class="text-3xl text-success">{{ totalMonthlyClosing.toLocaleString() }}</p>
                            </div>
                        </div>
                    <vs-divider class="my-6"></vs-divider>
                    <ul>
                        <li v-for="codiData in codificationRequestByStatusToday.analyticsData" :key="codiData.device" class="flex mb-3">
                            <feather-icon :icon="codiData.icon" :svgClasses="[`h-5 w-5 stroke-current text-${codiData.color}`]"></feather-icon>
                            <span class="ml-2 inline-block font-semibold">{{ codiData.status }}</span>
                            <span class="mx-2">-</span>
                            <div class="ml-auto flex -mr-1">
                                <span class="mr-1">{{ codiData.total.toLocaleString() }}</span>
                            </div>
                        </li>
                    </ul>
                </div>

            </div>
        </vx-card>
        </div>
    </div>
</div>
</template>

<script>
import StatisticsCardLine from '@/components/statistics-cards/StatisticsCardLine.vue'
import VueApexCharts from 'vue-apexcharts'
import axios from "axios";

export default {
    data() {
        return {
            totalPublished: 0,
            totalClosing: 0,
            totalPendingPublish: 0,
            totalPendingClosing: 0,
            totalAccumulatedPublished: 0,
            totalAccumulatedClosing: 0,
            totalMonthlyPublished: 0,
            totalMonthlyClosing: 0,

            codificationRequestByStatusToday: {
                analyticsData: [
                    { status: 'Evaluate', icon: 'CircleIcon', color: 'primary', total: 4321 },
                    { status: 'LOA', icon: 'CircleIcon', color: 'warning', total: 3456 },
                ],
                series: [82,180],
                chartOptions: {
                    labels: ['Evaluate', 'LOA'],
                    dataLabels: {
                        enabled: false
                    },
                    legend: { show: false },
                    chart: {
                        offsetY: 30,
                        type: 'donut',
                        toolbar: {
                            show: false
                        }
                    },
                    stroke: { width: 0 },
                    colors: ['#7961F9', '#FF9F43', '#EA5455'],
                    fill: {
                        type: 'gradient',
                        gradient: {
                            gradientToColors: ['#9c8cfc', '#FFC085', '#f29292']
                        }
                    }
                }
            },
            
            salesBarSession: {
                    series: [{
                        name: 'PO/CO',
                        data: [75, 125, 225, 175, 125, 75, 25, 100, 25, 120, 50, 60, 70, 80, 90]
                    }],
                    analyticsData: {
                    session: 16876012,
                    comparison : {
                        str: "Yesterday",
                        result: +17809
                    }
                }
            },
        }
    },
    components: {
        StatisticsCardLine,
        VueApexCharts
    },
    methods: {
        initLoadingSpinner(elementId) {
            this.$vs.loading({
                container: elementId,
                scale: 0.6
            });
        },
        getQtMonitoringData: function () {
            const url = "/api/kpi/qt/monitoring"
            
            axios.get(url).then(response => {
                let data = response.data
                this.totalPublished = data.total_published
                this.totalClosing = data.total_closing
                this.totalPendingPublish = data.total_pending_publish
                this.totalPendingClosing = data.total_pending_closing
                this.totalAccumulatedPublished = data.total_accumulated_published
                this.totalAccumulatedClosing = data.total_accumulated_closing
                this.totalMonthlyPublished = data.total_monthly_published
                this.totalMonthlyClosing = data.total_monthly_closing

                this.codificationRequestByStatusToday.series = data.total_loa_evaluate_series.series
                this.codificationRequestByStatusToday.chartOptions = {
                    labels: data.total_loa_evaluate_series.label
                }
                this.codificationRequestByStatusToday.analyticsData = data.total_loa_evaluate

                this.$vs.loading.close('#top-stats > .con-vs-loading');
                this.$vs.loading.close('#bottom-stats > .con-vs-loading');
            })
            .catch(error => {
                err => console.log(error)
                this.$vs.loading.close('#top-stats > .con-vs-loading');
                this.$vs.loading.close('#bottom-stats > .con-vs-loading');
            })
        },
    },
    mounted: function () {
        this.initLoadingSpinner('#top-stats');
         this.initLoadingSpinner('#bottom-stats');
        this.getQtMonitoringData();
    },
}
</script>
